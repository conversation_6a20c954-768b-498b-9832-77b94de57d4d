# 🎨 DISEÑO FRONTEND SISTEMA HIPÓCRATES

## 📋 ÍNDICE
1. [Arquitectura Frontend](#arquitectura-frontend)
2. [Stack Tecnológico](#stack-tecnológico)
3. [Estructura del Proyecto](#estructura-del-proyecto)
4. [Sistema de Diseño](#sistema-de-diseño)
5. [Mó<PERSON><PERSON> y Componentes](#módulos-y-componentes)
6. [Integración con Backend](#integración-con-backend)
7. [Etapas de Desarrollo](#etapas-de-desarrollo)

---

## 🏗️ ARQUITECTURA FRONTEND

### **Patrón Arquitectónico: Modular + Component-Based**

```
┌─────────────────────────────────────────────────────────────┐
│                    LAYOUT PRINCIPAL                        │
│  ┌─────────────┐ ┌─────────────────────┐ ┌─────────────┐   │
│  │   HEADER    │ │      CONTENT        │ │   SIDEBAR   │   │
│  │(Glassmorphism)│ │   (Fondo Elegante)  │ │(Glassmorphism)│   │
│  └─────────────┘ └─────────────────────┘ └─────────────┘   │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                   FOOTER                            │   │
│  │                (Glassmorphism)                      │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### **Principios de Diseño:**
- **Glassmorphism** solo en Header, Footer, Sidebar
- **Fondos elegantes básicos** para contenido principal
- **Modo oscuro/claro** alternativo
- **Responsive design** mobile-first
- **Accesibilidad** WCAG 2.1 AA

---

## 🛠️ STACK TECNOLÓGICO

### **Core Framework:**
- **React 18** con TypeScript
- **Vite** para build y desarrollo
- **React Router v6** para navegación

### **Estado y Datos:**
- **Zustand** para estado global
- **TanStack Query** para estado del servidor
- **React Hook Form** para formularios
- **Zod** para validación

### **UI y Estilos:**
- **Tailwind CSS** para estilos
- **Headless UI** para componentes accesibles
- **Framer Motion** para animaciones
- **React Hot Toast** para notificaciones

### **Mapas y Geolocalización:**
- **React Leaflet** para mapas
- **OpenStreetMap** como proveedor de mapas

### **Gráficos y Visualización:**
- **Recharts** para gráficos
- **D3.js** para visualizaciones complejas

### **Comunicación:**
- **Axios** para HTTP requests
- **Socket.io-client** para WebSocket
- **MSW** para mocking en desarrollo

---

## 📁 ESTRUCTURA DEL PROYECTO ACTUALIZADA

```
frontend/src/
├── components/                    # Componentes reutilizables
│   ├── layout/                   # Componentes de layout
│   │   ├── Header.tsx            # Header con glassmorphism
│   │   ├── Sidebar.tsx           # Sidebar con glassmorphism
│   │   ├── Footer.tsx            # Footer con glassmorphism
│   │   ├── Layout.tsx            # Layout principal
│   │   └── ThemeProvider.tsx     # Proveedor de tema
│   ├── ui/                       # Componentes UI básicos
│   │   ├── Button.tsx            # Botones con variantes
│   │   ├── Input.tsx             # Inputs con validación
│   │   ├── Modal.tsx             # Modales elegantes
│   │   ├── Card.tsx              # Cards con fondo elegante
│   │   ├── Table.tsx             # Tablas responsivas
│   │   ├── Form.tsx              # Formularios estilizados
│   │   └── ThemeToggle.tsx       # Toggle modo oscuro/claro
│   ├── diagnosticos/             # Componentes CIE-10/CIE-11
│   │   ├── DiagnosticSearch.tsx  # Búsqueda unificada
│   │   ├── DiagnosticSelector.tsx # Selector con autocompletado
│   │   └── TransitionHelper.tsx  # Ayuda transición CIE-10→CIE-11
│   └── maps/                     # Componentes de mapas
│       ├── AmbulanceMap.tsx      # Mapa de ambulancias
│       ├── LocationPicker.tsx    # Selector de ubicación
│       └── RouteTracker.tsx      # Seguimiento de rutas
├── modules/                      # Módulos de negocio
│   ├── auth/                     # Autenticación
│   ├── dashboard/                # Dashboard principal
│   ├── pacientes/                # Gestión de pacientes
│   ├── citas/                    # Sistema de citas
│   ├── consultas/                # Consultas médicas
│   ├── ambulancias/              # Sistema de ambulancias
│   ├── telemedicina/             # Telemedicina
│   ├── inventario/               # Inventario y farmacia
│   ├── financiero/               # Facturación y pagos
│   ├── erp/                      # ERP (RRHH, Contabilidad, Presupuestos)
│   └── admin/                    # Administración
├── services/                     # Servicios API
│   ├── api.ts                    # Cliente HTTP base
│   ├── auth.service.ts           # Servicios de autenticación
│   ├── diagnosis.service.ts      # Servicios CIE-10/CIE-11
│   ├── ambulance.service.ts      # Servicios de ambulancias
│   ├── websocket.service.ts      # Cliente WebSocket
│   └── [módulo].service.ts       # Servicios por módulo
├── store/                        # Estado global
│   ├── auth.store.ts             # Estado de autenticación
│   ├── theme.store.ts            # Estado del tema
│   ├── ambulance.store.ts        # Estado de ambulancias
│   └── settings.store.ts         # Configuraciones
├── hooks/                        # Custom hooks
│   ├── useAuth.ts                # Hook de autenticación
│   ├── useWebSocket.ts           # Hook WebSocket
│   ├── useGeolocation.ts         # Hook geolocalización
│   ├── useDiagnosis.ts           # Hook diagnósticos
│   └── useTheme.ts               # Hook de tema
├── types/                        # Tipos TypeScript
│   ├── api.types.ts              # Tipos de API
│   ├── auth.types.ts             # Tipos de autenticación
│   ├── diagnosis.types.ts        # Tipos CIE-10/CIE-11
│   ├── ambulance.types.ts        # Tipos de ambulancias
│   └── [módulo].types.ts         # Tipos por módulo
├── utils/                        # Utilidades
│   ├── api.utils.ts              # Utilidades API
│   ├── date.utils.ts             # Utilidades de fecha
│   ├── format.utils.ts           # Formateo de datos
│   ├── validation.utils.ts       # Validaciones
│   └── theme.utils.ts            # Utilidades de tema
└── styles/                       # Estilos globales
    ├── globals.css               # Estilos globales
    ├── components.css            # Estilos de componentes
    └── themes.css                # Definición de temas
```

---

## 🎨 SISTEMA DE DISEÑO

### **Paleta de Colores**

#### **Modo Claro:**
```css
:root[data-theme="light"] {
  /* Colores principales */
  --primary: #3B82F6;           /* Azul principal */
  --primary-dark: #1E40AF;      /* Azul oscuro */
  --secondary: #10B981;         /* Verde secundario */
  --accent: #F59E0B;            /* Amarillo acento */
  
  /* Fondos */
  --bg-primary: #FFFFFF;        /* Fondo principal */
  --bg-secondary: #F8FAFC;      /* Fondo secundario */
  --bg-card: #FFFFFF;           /* Fondo de cards */
  
  /* Textos */
  --text-primary: #1F2937;      /* Texto principal */
  --text-secondary: #6B7280;    /* Texto secundario */
  --text-muted: #9CA3AF;        /* Texto atenuado */
  
  /* Bordes */
  --border: #E5E7EB;            /* Bordes */
  --border-light: #F3F4F6;      /* Bordes claros */
  
  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-backdrop: blur(10px);
}
```

#### **Modo Oscuro:**
```css
:root[data-theme="dark"] {
  /* Colores principales */
  --primary: #60A5FA;           /* Azul principal */
  --primary-dark: #3B82F6;      /* Azul oscuro */
  --secondary: #34D399;         /* Verde secundario */
  --accent: #FBBF24;            /* Amarillo acento */
  
  /* Fondos */
  --bg-primary: #0F172A;        /* Fondo principal */
  --bg-secondary: #1E293B;      /* Fondo secundario */
  --bg-card: #334155;           /* Fondo de cards */
  
  /* Textos */
  --text-primary: #F8FAFC;      /* Texto principal */
  --text-secondary: #CBD5E1;    /* Texto secundario */
  --text-muted: #94A3B8;        /* Texto atenuado */
  
  /* Bordes */
  --border: #475569;            /* Bordes */
  --border-light: #334155;      /* Bordes claros */
  
  /* Glassmorphism */
  --glass-bg: rgba(15, 23, 42, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-backdrop: blur(10px);
}
```

### **Tipografía**
```css
/* Familias de fuentes */
--font-sans: 'Inter', system-ui, sans-serif;
--font-mono: 'JetBrains Mono', monospace;

/* Tamaños */
--text-xs: 0.75rem;     /* 12px */
--text-sm: 0.875rem;    /* 14px */
--text-base: 1rem;      /* 16px */
--text-lg: 1.125rem;    /* 18px */
--text-xl: 1.25rem;     /* 20px */
--text-2xl: 1.5rem;     /* 24px */
--text-3xl: 1.875rem;   /* 30px */
--text-4xl: 2.25rem;    /* 36px */

/* Pesos */
--font-light: 300;
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
```

### **Espaciado y Dimensiones**
```css
/* Espaciado */
--space-1: 0.25rem;     /* 4px */
--space-2: 0.5rem;      /* 8px */
--space-3: 0.75rem;     /* 12px */
--space-4: 1rem;        /* 16px */
--space-6: 1.5rem;      /* 24px */
--space-8: 2rem;        /* 32px */
--space-12: 3rem;       /* 48px */
--space-16: 4rem;       /* 64px */

/* Radios de borde */
--radius-sm: 0.25rem;   /* 4px */
--radius: 0.5rem;       /* 8px */
--radius-lg: 0.75rem;   /* 12px */
--radius-xl: 1rem;      /* 16px */

/* Sombras */
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
```

---

## 🧩 COMPONENTES UI PRINCIPALES

### **Layout Components**

#### **Header Component**
```typescript
interface HeaderProps {
  user?: User;
  onThemeToggle: () => void;
  theme: 'light' | 'dark';
}

// Características:
// - Glassmorphism effect
// - User menu dropdown
// - Theme toggle
// - Notifications
// - Search global
```

#### **Sidebar Component**
```typescript
interface SidebarProps {
  collapsed?: boolean;
  onToggle: () => void;
  modules: ModuleConfig[];
}

// Características:
// - Glassmorphism effect
// - Collapsible
// - Module navigation
// - Role-based visibility
// - Active state indicators
```

#### **Content Area**
```typescript
interface ContentAreaProps {
  children: React.ReactNode;
  title?: string;
  breadcrumbs?: Breadcrumb[];
  actions?: React.ReactNode;
}

// Características:
// - Fondo elegante básico
// - Breadcrumbs
// - Page title
// - Action buttons
// - Responsive padding
```

### **Form Components**

#### **Enhanced Input**
```typescript
interface InputProps extends HTMLInputProps {
  label?: string;
  error?: string;
  helper?: string;
  icon?: React.ReactNode;
  variant?: 'default' | 'filled' | 'outlined';
}

// Características:
// - Validation states
// - Icon support
// - Helper text
// - Accessibility
// - Theme-aware
```

#### **Diagnostic Search**
```typescript
interface DiagnosticSearchProps {
  onSelect: (diagnosis: Diagnosis) => void;
  version?: 'CIE-10' | 'CIE-11' | 'BOTH';
  placeholder?: string;
  multiple?: boolean;
}

// Características:
// - Búsqueda unificada CIE-10/CIE-11
// - Autocompletado inteligente
// - Cache de resultados
// - Transición CIE-10→CIE-11
// - Multiselección
```

### **Data Display Components**

#### **Enhanced Table**
```typescript
interface TableProps<T> {
  data: T[];
  columns: ColumnDef<T>[];
  pagination?: PaginationConfig;
  sorting?: SortingConfig;
  filtering?: FilteringConfig;
  selection?: SelectionConfig;
}

// Características:
// - Sorting
// - Filtering
// - Pagination
// - Row selection
// - Responsive
// - Export functionality
```

#### **Dashboard Cards**
```typescript
interface DashboardCardProps {
  title: string;
  value: string | number;
  change?: number;
  trend?: 'up' | 'down' | 'stable';
  icon?: React.ReactNode;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
}

// Características:
// - Fondo elegante
// - Trend indicators
// - Animated counters
// - Responsive design
// - Theme-aware colors
```

---

## 🗺️ MÓDULOS PRINCIPALES ACTUALIZADOS

### **1. Dashboard Principal**
```typescript
// modules/dashboard/Dashboard.tsx
interface DashboardProps {
  hospitalId: string;
  userRole: string;
}

// Características:
// - KPIs en tiempo real
// - Gráficos interactivos
// - Alertas y notificaciones
// - Accesos rápidos por rol
// - Widgets personalizables
```

### **2. Módulo de Pacientes**
```typescript
// modules/pacientes/Pacientes.tsx
interface PacientesModuleProps {
  onPatientSelect?: (patient: Patient) => void;
}

// Características:
// - Búsqueda avanzada con filtros
// - Formulario con validación robusta
// - Datos sensibles encriptados
// - Consentimientos digitales
// - Historial médico integrado
// - Exportación RIPS
```

### **3. Módulo de Ambulancias (Fortalecido)**
```typescript
// modules/ambulancias/Ambulancias.tsx
interface AmbulanciaModuleProps {
  realTimeTracking?: boolean;
}

// Características:
// - Mapa en tiempo real con WebSocket
// - Gestión de servicios
// - Asignación automática
// - Rutas GPS optimizadas
// - Dashboard de monitoreo
// - Alertas de emergencia
// - Integración con dispositivos IoT
```

### **4. Módulo de Diagnósticos CIE-10/CIE-11**
```typescript
// modules/diagnosticos/Diagnosticos.tsx
interface DiagnosticosModuleProps {
  defaultVersion?: 'CIE-10' | 'CIE-11';
  transitionMode?: boolean;
}

// Características:
// - Búsqueda unificada OMS
// - Comparación CIE-10 vs CIE-11
// - Herramientas de transición
// - Cache inteligente
// - Soporte multiidioma
// - Validación automática
```

### **5. Módulo de Telemedicina**
```typescript
// modules/telemedicina/Telemedicina.tsx
interface TelemediciniaModuleProps {
  platform?: 'WebRTC' | 'Zoom' | 'Teams' | 'Jitsi';
}

// Características:
// - Sala de video integrada
// - Programación de teleconsultas
// - Monitoreo remoto de pacientes
// - Grabación con consentimiento
// - Chat en tiempo real
// - Compartir pantalla
// - Calidad de conexión
```

### **6. Módulo ERP Completo**
```typescript
// modules/erp/ERP.tsx
interface ERPModuleProps {
  module: 'rrhh' | 'contabilidad' | 'presupuestos';
}

// Submodulos:
// - Recursos Humanos: empleados, nómina, reportes
// - Contabilidad: plan de cuentas, asientos, balances
// - Presupuestos: creación, ejecución, control
```

---

## 🔌 INTEGRACIÓN CON BACKEND

### **API Client Configuration**
```typescript
// services/api.ts
import axios from 'axios';

const apiClient = axios.create({
  baseURL: process.env.VITE_API_URL || 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor para JWT
apiClient.interceptors.request.use((config) => {
  const token = useAuthStore.getState().token;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor para manejo de errores
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      useAuthStore.getState().logout();
    }
    return Promise.reject(error);
  }
);
```

### **WebSocket Integration**
```typescript
// services/websocket.service.ts
import { io, Socket } from 'socket.io-client';

class WebSocketService {
  private socket: Socket | null = null;

  connect(token: string) {
    this.socket = io(process.env.VITE_WS_URL || 'ws://localhost:3000', {
      auth: { token },
      transports: ['websocket'],
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    // Ambulancias
    this.socket.on('ambulance:location:update', (data) => {
      useAmbulanceStore.getState().updateLocation(data);
    });

    // Notificaciones
    this.socket.on('system:notification', (notification) => {
      toast.info(notification.message);
    });

    // Alertas de pacientes
    this.socket.on('patient:alert', (alert) => {
      toast.error(`Alerta: ${alert.message}`);
    });
  }

  // Métodos para emitir eventos
  trackAmbulance(ambulanceId: string) {
    this.socket?.emit('ambulance:track', ambulanceId);
  }

  untrackAmbulance(ambulanceId: string) {
    this.socket?.emit('ambulance:untrack', ambulanceId);
  }
}

export const wsService = new WebSocketService();
```

### **Diagnosis Service (CIE-10/CIE-11)**
```typescript
// services/diagnosis.service.ts
import { apiClient } from './api';

export interface DiagnosisSearchParams {
  query: string;
  version?: 'CIE-10' | 'CIE-11' | 'BOTH';
  language?: 'es' | 'en';
  limit?: number;
}

export class DiagnosisService {
  async search(params: DiagnosisSearchParams) {
    const response = await apiClient.get('/diagnosis/search', { params });
    return response.data;
  }

  async getById(id: string, version: 'CIE-10' | 'CIE-11' = 'CIE-11') {
    const response = await apiClient.get(`/diagnosis/${id}`, {
      params: { version }
    });
    return response.data;
  }

  async getTransitionReport() {
    const response = await apiClient.get('/diagnosis/transition-report');
    return response.data;
  }

  async mapCie10ToCie11(cie10Code: string) {
    const response = await apiClient.get(`/diagnosis/cie10-to-cie11/${cie10Code}`);
    return response.data;
  }
}

export const diagnosisService = new DiagnosisService();
```

---

## 🎯 ETAPAS DE DESARROLLO FRONTEND

### **ETAPA 1: FUNDACIÓN UI (Semana 1-2)**
**Objetivo:** Sistema de diseño y componentes base

#### **1.1 Sistema de Diseño**
- [ ] Configurar variables CSS para temas
- [ ] Implementar toggle modo oscuro/claro
- [ ] Crear paleta de colores completa
- [ ] Configurar tipografía y espaciado

#### **1.2 Layout Principal**
- [ ] Header con glassmorphism
- [ ] Sidebar con glassmorphism y navegación
- [ ] Footer con glassmorphism
- [ ] Content area con fondo elegante
- [ ] Responsive design

#### **1.3 Componentes UI Base**
- [ ] Button con variantes
- [ ] Input con validación
- [ ] Card con fondo elegante
- [ ] Modal y Dialog
- [ ] Table responsiva
- [ ] Form components

**Entregables:**
- Sistema de diseño funcional
- Layout responsive completo
- Componentes UI base
- Toggle de tema operativo

### **ETAPA 2: AUTENTICACIÓN Y NAVEGACIÓN (Semana 3)**
**Objetivo:** Sistema de autenticación y navegación

#### **2.1 Autenticación**
- [ ] Login form con validación
- [ ] JWT token management
- [ ] Protected routes
- [ ] User context y store

#### **2.2 Navegación**
- [ ] Router configuration
- [ ] Breadcrumbs
- [ ] Menu dinámico por roles
- [ ] Active states

**Entregables:**
- Sistema de login funcional
- Navegación por roles
- Rutas protegidas
- Gestión de estado de usuario

### **ETAPA 3: MÓDULOS CORE (Semana 4-5)**
**Objetivo:** Dashboard y módulos principales

#### **3.1 Dashboard**
- [ ] KPIs en tiempo real
- [ ] Gráficos con Recharts
- [ ] Widgets personalizables
- [ ] Notificaciones

#### **3.2 Módulo Pacientes**
- [ ] Lista con búsqueda avanzada
- [ ] Formulario de creación/edición
- [ ] Vista de detalles
- [ ] Validaciones robustas

#### **3.3 Módulo Citas**
- [ ] Calendario interactivo
- [ ] Programación de citas
- [ ] Estados y filtros
- [ ] Notificaciones

**Entregables:**
- Dashboard operativo
- Gestión completa de pacientes
- Sistema de citas funcional
- Integración con backend

### **ETAPA 4: DIAGNÓSTICOS CIE-10/CIE-11 (Semana 6)**
**Objetivo:** Integración con API OMS

#### **4.1 Componentes de Diagnóstico**
- [ ] Búsqueda unificada
- [ ] Autocompletado inteligente
- [ ] Comparador CIE-10 vs CIE-11
- [ ] Herramientas de transición

#### **4.2 Integración API OMS**
- [ ] Service para API OMS
- [ ] Cache de resultados
- [ ] Manejo de errores
- [ ] Soporte multiidioma

**Entregables:**
- Búsqueda de diagnósticos funcional
- Integración completa con API OMS
- Herramientas de transición CIE
- Cache optimizado

### **ETAPA 5: AMBULANCIAS GPS (Semana 7-8)**
**Objetivo:** Sistema de ambulancias con geolocalización

#### **5.1 Mapas y Geolocalización**
- [ ] Integración React Leaflet
- [ ] Marcadores de ambulancias
- [ ] Rutas en tiempo real
- [ ] Controles de mapa

#### **5.2 WebSocket en Tiempo Real**
- [ ] Conexión WebSocket
- [ ] Eventos de ubicación
- [ ] Notificaciones push
- [ ] Estado de conexión

#### **5.3 Dashboard de Ambulancias**
- [ ] Lista de ambulancias
- [ ] Estados en tiempo real
- [ ] Asignación de servicios
- [ ] Métricas operativas

**Entregables:**
- Mapa de ambulancias en tiempo real
- Dashboard de monitoreo
- Sistema de asignación
- WebSocket funcional

### **ETAPA 6: TELEMEDICINA (Semana 9)**
**Objetivo:** Plataforma de telemedicina

#### **6.1 Video Conferencia**
- [ ] Integración WebRTC
- [ ] Sala de video
- [ ] Controles de audio/video
- [ ] Chat en tiempo real

#### **6.2 Gestión de Teleconsultas**
- [ ] Programación
- [ ] Estados de consulta
- [ ] Grabación con consentimiento
- [ ] Calidad de conexión

**Entregables:**
- Sala de video funcional
- Gestión de teleconsultas
- Chat integrado
- Monitoreo de calidad

### **ETAPA 7: MÓDULOS ERP (Semana 10-11)**
**Objetivo:** Sistema ERP completo

#### **7.1 Recursos Humanos**
- [ ] Gestión de empleados
- [ ] Nómina automatizada
- [ ] Reportes laborales
- [ ] Dashboard RRHH

#### **7.2 Contabilidad**
- [ ] Plan de cuentas
- [ ] Asientos contables
- [ ] Balances automáticos
- [ ] Reportes financieros

#### **7.3 Presupuestos**
- [ ] Creación de presupuestos
- [ ] Control de ejecución
- [ ] Alertas presupuestales
- [ ] Dashboard ejecutivo

**Entregables:**
- Módulo RRHH completo
- Sistema contable funcional
- Gestión presupuestal
- Reportes integrados

### **ETAPA 8: OPTIMIZACIÓN Y PULIMIENTO (Semana 12)**
**Objetivo:** Optimización y mejoras finales

#### **8.1 Performance**
- [ ] Code splitting
- [ ] Lazy loading
- [ ] Optimización de imágenes
- [ ] Bundle analysis

#### **8.2 Accesibilidad**
- [ ] ARIA labels
- [ ] Keyboard navigation
- [ ] Screen reader support
- [ ] Color contrast

#### **8.3 Testing**
- [ ] Unit tests
- [ ] Integration tests
- [ ] E2E tests
- [ ] Visual regression tests

**Entregables:**
- Aplicación optimizada
- Accesibilidad completa
- Suite de tests
- Documentación final

---

## 📱 RESPONSIVE DESIGN

### **Breakpoints**
```css
/* Mobile first approach */
--breakpoint-sm: 640px;   /* Tablet */
--breakpoint-md: 768px;   /* Tablet landscape */
--breakpoint-lg: 1024px;  /* Desktop */
--breakpoint-xl: 1280px;  /* Large desktop */
--breakpoint-2xl: 1536px; /* Extra large */
```

### **Layout Adaptativo**
- **Mobile (< 640px):** Sidebar colapsado, navegación bottom
- **Tablet (640px - 1024px):** Sidebar overlay, contenido adaptado
- **Desktop (> 1024px):** Sidebar fijo, layout completo

---

## 🔧 CONFIGURACIÓN DE DESARROLLO

### **Vite Configuration**
```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@components': path.resolve(__dirname, './src/components'),
      '@modules': path.resolve(__dirname, './src/modules'),
      '@services': path.resolve(__dirname, './src/services'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@types': path.resolve(__dirname, './src/types'),
    },
  },
  server: {
    port: 3001,
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
      },
      '/ws': {
        target: 'ws://localhost:3000',
        ws: true,
      },
    },
  },
});
```

### **Environment Variables**
```env
# .env.development
VITE_API_URL=http://localhost:3000/api
VITE_WS_URL=ws://localhost:3000
VITE_APP_NAME=Hipócrates
VITE_APP_VERSION=2.0.0
VITE_ENABLE_MSW=true

# .env.production
VITE_API_URL=https://api.hipocrates.com/api
VITE_WS_URL=wss://api.hipocrates.com
VITE_APP_NAME=Hipócrates
VITE_APP_VERSION=2.0.0
VITE_ENABLE_MSW=false
```

---

## 🎨 EJEMPLOS DE COMPONENTES CLAVE

### **Theme Toggle Component**
```typescript
// components/ui/ThemeToggle.tsx
import { Moon, Sun } from 'lucide-react';
import { useTheme } from '@/hooks/useTheme';

export function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className="relative p-2 rounded-lg bg-glass border border-glass-border backdrop-blur-glass transition-all hover:bg-glass/80"
      aria-label={`Cambiar a modo ${theme === 'light' ? 'oscuro' : 'claro'}`}
    >
      <Sun className={`h-4 w-4 transition-all ${theme === 'dark' ? 'rotate-90 scale-0' : 'rotate-0 scale-100'}`} />
      <Moon className={`absolute top-2 left-2 h-4 w-4 transition-all ${theme === 'light' ? 'rotate-90 scale-0' : 'rotate-0 scale-100'}`} />
    </button>
  );
}
```

### **Diagnostic Search Component**
```typescript
// components/diagnosticos/DiagnosticSearch.tsx
import { useState, useEffect } from 'react';
import { useDiagnosis } from '@/hooks/useDiagnosis';
import { Combobox } from '@headlessui/react';

interface DiagnosticSearchProps {
  onSelect: (diagnosis: Diagnosis) => void;
  version?: 'CIE-10' | 'CIE-11' | 'BOTH';
  placeholder?: string;
}

export function DiagnosticSearch({ onSelect, version = 'BOTH', placeholder }: DiagnosticSearchProps) {
  const [query, setQuery] = useState('');
  const { searchDiagnosis, isLoading, results } = useDiagnosis();

  useEffect(() => {
    if (query.length >= 3) {
      searchDiagnosis({ query, version, language: 'es' });
    }
  }, [query, version]);

  return (
    <Combobox onChange={onSelect}>
      <div className="relative">
        <Combobox.Input
          className="w-full rounded-lg border border-border bg-bg-card px-3 py-2 text-text-primary placeholder:text-text-muted focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary"
          placeholder={placeholder || 'Buscar diagnóstico...'}
          onChange={(event) => setQuery(event.target.value)}
        />

        {isLoading && (
          <div className="absolute right-3 top-2.5">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
          </div>
        )}

        <Combobox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-lg bg-bg-card border border-border shadow-lg">
          {results.map((diagnosis) => (
            <Combobox.Option
              key={diagnosis.id}
              value={diagnosis}
              className={({ active }) =>
                `relative cursor-pointer select-none py-2 px-3 ${
                  active ? 'bg-primary text-white' : 'text-text-primary'
                }`
              }
            >
              <div className="flex items-center justify-between">
                <div>
                  <span className="font-medium">{diagnosis.code}</span>
                  <span className="ml-2">{diagnosis.title}</span>
                </div>
                <span className={`text-xs px-2 py-1 rounded ${
                  diagnosis.source === 'CIE-11'
                    ? 'bg-secondary/20 text-secondary'
                    : 'bg-accent/20 text-accent'
                }`}>
                  {diagnosis.source}
                </span>
              </div>
            </Combobox.Option>
          ))}
        </Combobox.Options>
      </div>
    </Combobox>
  );
}
```

---

## 🚀 PRÓXIMOS PASOS RECOMENDADOS

1. **Implementar sistema de diseño** con variables CSS y toggle de tema
2. **Crear layout principal** con glassmorphism en header, sidebar y footer
3. **Desarrollar componentes UI base** con fondo elegante para contenido
4. **Integrar autenticación** con JWT y rutas protegidas
5. **Implementar módulo de diagnósticos** con API OMS
6. **Desarrollar sistema de ambulancias** con mapas y WebSocket
7. **Crear módulos ERP** completos
8. **Optimizar performance** y accesibilidad

¿Te gustaría que desarrolle alguna etapa específica en detalle o que creemos los componentes base para comenzar la implementación?
