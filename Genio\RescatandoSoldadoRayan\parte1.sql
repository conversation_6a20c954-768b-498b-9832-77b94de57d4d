-- Script SQL completo y optimizado para Hipocrates2DB en PostgreSQL 16+
-- Incluye prioridades altas: rotación de claves, particionamiento, vistas materializadas RIPS, y estandarización de constraints
-- Diseñado para un sistema SaaS multi-tenant, cumple con RIPS, Ley 1581/2012, DIAN y es internacionalizable
-- Incluye módulo de ambulancias con georeferenciación y optimizaciones para modularidad, escalabilidad y seguridad

-- ############################################################################
-- ### CONFIGURACIÓN INICIAL PARA SAAS MULTITENANCY Y ESCALABILIDAD ###
-- ############################################################################

--Instalacion de extensiones

-- ### Extensiones requeridas para Hipocrates2DB ###
DO $$
DECLARE
    v_extensions TEXT[] := ARRAY[
        'postgis',
        'pgcrypto',
        'plpgsql',
        'vector',
        'timescaledb',
        'pg_stat_statements',
        'uuid-ossp',
        'btree_gist',
        'tablefunc',
        'fuzzystrmatch',
        'pg_trgm',
        'unaccent'
    ];
    v_ext TEXT;
    v_installed_extensions TEXT[];
    v_missing_extensions TEXT[];
BEGIN
    -- Obtener extensiones ya instaladas
    SELECT array_agg(extname) INTO v_installed_extensions FROM pg_extension;

    -- Si v_installed_extensions es NULL (ninguna extensión instalada), inicializar como array vacío
    IF v_installed_extensions IS NULL THEN
        v_installed_extensions := '{}'::TEXT[];
    END IF;

    -- Determinar extensiones faltantes
    v_missing_extensions := ARRAY(
        SELECT unnest(v_extensions)
        EXCEPT
        SELECT unnest(v_installed_extensions)
    );

    -- Instalar extensiones faltantes
    IF array_length(v_missing_extensions, 1) > 0 THEN
        FOREACH v_ext IN ARRAY v_missing_extensions
        LOOP
            RAISE NOTICE 'Instalando extensión faltante: %', v_ext;
            EXECUTE 'CREATE EXTENSION IF NOT EXISTS ' || quote_ident(v_ext);
        END LOOP;
        RAISE NOTICE 'Todas las extensiones requeridas han sido verificadas e instaladas.';
    ELSE
        RAISE NOTICE 'Todas las extensiones requeridas ya están instaladas.';
    END IF;

    -- Verificar si la extensión pgcrypto está instalada
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'pgcrypto') THEN
        RAISE NOTICE 'La extensión pgcrypto no está instalada. Intentando instalar...';
        CREATE EXTENSION IF NOT EXISTS pgcrypto;
        RAISE NOTICE 'Extensión pgcrypto instalada correctamente.';
    ELSE
        RAISE NOTICE 'La extensión pgcrypto ya está instalada.';
    END IF;

EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error durante la instalación de extensiones: %', SQLERRM;
        RAISE WARNING 'Asegúrese de que el usuario de base de datos tenga permisos para crear extensiones y que las bibliotecas de las extensiones estén disponibles en el sistema.';
END;
$$;

-- Configuración de conexiones y recursos para alta concurrencia
-- NOTA: Los siguientes comandos ALTER SYSTEM no pueden ejecutarse dentro de una transacción.
-- Por favor, ejecute estos comandos manualmente en la consola de PostgreSQL antes de ejecutar el resto del script.
/*
ALTER SYSTEM SET max_connections = '500';
ALTER SYSTEM SET shared_buffers = '4GB';
ALTER SYSTEM SET work_mem = '16MB';
ALTER SYSTEM SET maintenance_work_mem = '512MB';
ALTER SYSTEM SET effective_cache_size = '12GB';
ALTER SYSTEM SET max_parallel_workers_per_gather = '4';
ALTER SYSTEM SET max_parallel_workers = '8';
ALTER SYSTEM SET max_worker_processes = '16';

-- Configuración de connection pooling para multitenancy
ALTER SYSTEM SET idle_in_transaction_session_timeout = '60000';  -- 60 segundos
ALTER SYSTEM SET statement_timeout = '180000';                   -- 3 minutos
ALTER SYSTEM SET tcp_keepalives_idle = '60';
ALTER SYSTEM SET tcp_keepalives_interval = '10';
*/

-- Crear esquemas para organización modular
DO $$
DECLARE
    v_schemas TEXT[] := ARRAY[
        'schema_migrations',  -- Gestión de versiones y migraciones
        'gestion_general',    -- Configuración general del sistema
        'clinico',            -- Datos clínicos y de pacientes
        'inventario',         -- Gestión de inventario
        'financiero',         -- Módulo financiero
        'recursos_humanos',   -- Gestión de personal
        'telemedicina',       -- Funcionalidades de telemedicina
        'control_calidad',    -- Control de calidad
        'erp',                -- Planificación de recursos empresariales
        'ambulancias',        -- Gestión de ambulancias
        'configuracion'       -- Configuraciones del sistema
    ];
    v_schema TEXT;
BEGIN
    FOREACH v_schema IN ARRAY v_schemas LOOP
        BEGIN
            EXECUTE format('CREATE SCHEMA IF NOT EXISTS %I', v_schema);
            RAISE NOTICE 'Esquema % creado o ya existente', v_schema;
        EXCEPTION WHEN OTHERS THEN
            RAISE WARNING 'Error al crear el esquema %: %', v_schema, SQLERRM;
        END;
    END LOOP;
END $$;

-- Tabla de versiones y migraciones
DO $$
BEGIN
    BEGIN
        CREATE TABLE IF NOT EXISTS schema_migrations.versions (
            version_id VARCHAR(50) PRIMARY KEY,
            description TEXT NOT NULL,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            applied_by TEXT NOT NULL,
            script_hash TEXT NOT NULL,
            script_content TEXT,
            is_validated BOOLEAN DEFAULT FALSE
        );
        COMMENT ON TABLE schema_migrations.versions IS 'Registro de migraciones aplicadas con hash basado en contenido para validación';
    EXCEPTION WHEN OTHERS THEN
        RAISE WARNING 'Error al crear la tabla de versiones: %', SQLERRM;
    END;
END $$;
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'schema_migrations'
        AND table_name = 'versions'
    ) THEN
        CREATE TABLE schema_migrations.versions (
            version_id VARCHAR(50) PRIMARY KEY,
            description TEXT NOT NULL,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            applied_by TEXT NOT NULL,
            script_hash TEXT NOT NULL,
            script_content TEXT,
            is_validated BOOLEAN DEFAULT FALSE
        );
        COMMENT ON TABLE schema_migrations.versions IS 'Registro de migraciones aplicadas con hash basado en contenido para validación';
        RAISE NOTICE 'Tabla schema_migrations.versions creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla schema_migrations.versions ya existe, omitiendo creación';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear la tabla schema_migrations.versions: %', SQLERRM;
END $$;

-- Drop existing functions if they exist
DROP FUNCTION IF EXISTS schema_migrations.register_migration(VARCHAR, TEXT, TEXT);
DROP FUNCTION IF EXISTS schema_migrations.verify_migrations();

-- Create the register_migration function
CREATE OR REPLACE FUNCTION schema_migrations.register_migration(
    p_version_id VARCHAR(50),
    p_description TEXT,
    p_script_content TEXT
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO schema_migrations.versions (
        version_id,
        description,
        applied_by,
        script_hash,
        script_content,
        is_validated
    )
    VALUES (
        p_version_id,
        p_description,
        current_user,
        md5(p_script_content),
        p_script_content,
        TRUE
    );
END;
$$ LANGUAGE plpgsql;

-- Create the verify_migrations function
CREATE OR REPLACE FUNCTION schema_migrations.verify_migrations()
RETURNS TABLE(version_id VARCHAR(50), is_valid BOOLEAN, error_message TEXT) AS $$
DECLARE
    v_record RECORD;
    v_calculated_hash TEXT;
BEGIN
    FOR v_record IN SELECT * FROM schema_migrations.versions ORDER BY applied_at LOOP
        v_calculated_hash := md5(v_record.script_content);

        IF v_record.script_hash = v_calculated_hash THEN
            version_id := v_record.version_id;
            is_valid := TRUE;
            error_message := NULL;
        ELSE
            version_id := v_record.version_id;
            is_valid := FALSE;
            error_message := 'Hash mismatch: stored=' || v_record.script_hash || ', calculated=' || v_calculated_hash;
        END IF;

        RETURN NEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Add comments
COMMENT ON FUNCTION schema_migrations.register_migration IS 'Registra una migración con hash basado en el contenido del script';
COMMENT ON FUNCTION schema_migrations.verify_migrations IS 'Verifica la integridad de las migraciones comparando los hashes almacenados';

-- Register initial version if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM schema_migrations.versions WHERE version_id = 'v1.0.0') THEN
        PERFORM schema_migrations.register_migration(
            'v1.0.0',
            'Esquema inicial Hipocrates2DB',
            'Esquema inicial para sistema SAAS multitenancy Hipocrates2DB'
        );
    END IF;

    RAISE NOTICE 'Migration functions created successfully';
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error creating migration functions: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Documentación detallada de extensiones
BEGIN;
COMMENT ON EXTENSION pgcrypto IS 'Proporciona funciones criptográficas para encriptar datos sensibles de pacientes y usuarios según Ley 1581/2012';
COMMENT ON EXTENSION postgis IS 'Habilita capacidades de geolocalización para el módulo de ambulancias, ubicación de hospitales y optimización de rutas';
COMMENT ON EXTENSION vector IS 'Soporta embeddings vectoriales para funcionalidades de IA como búsqueda semántica en historias clínicas y análisis predictivo';
COMMENT ON EXTENSION timescaledb IS 'Optimiza el almacenamiento y consulta de series temporales como datos de monitoreo de pacientes y sensores médicos';
COMMENT ON EXTENSION pg_stat_statements IS 'Permite monitorear y analizar el rendimiento de consultas SQL para optimizar el sistema por tenant';
COMMIT;

-- Crear schemas para modularidad
BEGIN;
CREATE SCHEMA IF NOT EXISTS clinico;
CREATE SCHEMA IF NOT EXISTS financiero;
CREATE SCHEMA IF NOT EXISTS ambulancias;
CREATE SCHEMA IF NOT EXISTS gestion_general;
CREATE SCHEMA IF NOT EXISTS recursos_humanos;
CREATE SCHEMA IF NOT EXISTS inventario;
CREATE SCHEMA IF NOT EXISTS telemedicina;
CREATE SCHEMA IF NOT EXISTS paciente_portal;
CREATE SCHEMA IF NOT EXISTS control_calidad;
CREATE SCHEMA IF NOT EXISTS erp;
CREATE SCHEMA IF NOT EXISTS tenant_config;  -- Nuevo esquema para configuración de tenants
COMMIT;

-- Tabla de control de tenants mejorada
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'tenant_config'
        AND table_name = 'tenants'
    ) THEN
        CREATE TABLE tenant_config.tenants (
            id BIGSERIAL PRIMARY KEY, -- Identificador único del tenant
            nombre TEXT NOT NULL, -- Nombre del tenant
            schema_prefix TEXT NOT NULL UNIQUE, -- Prefijo para el esquema del tenant
            estado TEXT NOT NULL CHECK (estado IN ('activo', 'inactivo', 'suspendido', 'prueba')), -- Estado del tenant
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Fecha de creación
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- Fecha de última actualización
            max_connections INTEGER DEFAULT 50, -- Máximo número de conexiones permitidas
            plan_tipo TEXT NOT NULL CHECK (plan_tipo IN ('basico', 'estandar', 'premium', 'enterprise')), -- Tipo de plan
            fecha_expiracion TIMESTAMP, -- Fecha de expiración del plan
            configuracion JSONB DEFAULT '{}'::jsonb -- Configuraciones adicionales en formato JSON
        );
        COMMENT ON TABLE tenant_config.tenants IS 'Registro centralizado de tenants con control de recursos y configuración';
        RAISE NOTICE 'Tabla tenant_config.tenants creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla tenant_config.tenants ya existe, omitiendo creación';

        -- Asegurarse de que las columnas necesarias existan
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_schema = 'tenant_config'
            AND table_name = 'tenants'
            AND column_name = 'configuracion'
        ) THEN
            ALTER TABLE tenant_config.tenants
            ADD COLUMN IF NOT EXISTS configuracion JSONB DEFAULT '{}'::jsonb;

            RAISE NOTICE 'Columna configuracion añadida a tenant_config.tenants';
        END IF;
    END IF;

    -- Crear índices si no existen
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenants'
        AND indexname = 'idx_tenants_estado'
    ) THEN
        CREATE INDEX idx_tenants_estado ON tenant_config.tenants(estado);
        RAISE NOTICE 'Índice idx_tenants_estado creado correctamente';
    END IF;

    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenants'
        AND indexname = 'idx_tenants_plan_tipo'
    ) THEN
        CREATE INDEX idx_tenants_plan_tipo ON tenant_config.tenants(plan_tipo);
        RAISE NOTICE 'Índice idx_tenants_plan_tipo creado correctamente';
    END IF;

EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear o modificar tenant_config.tenants: %', SQLERRM;
END $$;
COMMIT;

-- Crear función para actualizar automáticamente updated_at
DO $$
BEGIN
    -- Intentar eliminar la función si existe para asegurar una recreación limpia.
    -- Usamos un bloque anidado BEGIN/EXCEPTION para que el DO principal no falle si el DROP falla.
    BEGIN
        DROP FUNCTION IF EXISTS tenant_config.update_updated_at_column();
        RAISE NOTICE 'Función tenant_config.update_updated_at_column() eliminada si existía (o no se encontró).';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'No se pudo eliminar la función tenant_config.update_updated_at_column() (puede que no exista o haya dependencias): %', SQLERRM;
    END;

EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error en el bloque DO para eliminar la función tenant_config.update_updated_at_column(): %', SQLERRM;
END $$;
COMMIT;

CREATE OR REPLACE FUNCTION tenant_config.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Crear trigger para actualizar automáticamente updated_at
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_trigger
        WHERE tgname = 'update_tenants_updated_at'
        AND tgrelid = 'tenant_config.tenants'::regclass
    ) THEN
        CREATE TRIGGER update_tenants_updated_at
        BEFORE UPDATE ON tenant_config.tenants
        FOR EACH ROW EXECUTE FUNCTION tenant_config.update_updated_at_column();

        RAISE NOTICE 'Trigger update_tenants_updated_at creado correctamente';
    ELSE
        RAISE NOTICE 'El trigger update_tenants_updated_at ya existe, omitiendo creación';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear el trigger update_tenants_updated_at: %', SQLERRM;
END $$;
COMMIT;

-- Función para establecer el contexto del tenant actual
BEGIN;
CREATE OR REPLACE FUNCTION tenant_config.set_tenant_context(tenant_id BIGINT)
RETURNS VOID AS $$
BEGIN
    -- Verificar si el tenant existe y está activo
    PERFORM 1 FROM tenant_config.tenants
    WHERE id = tenant_id AND estado = 'activo';

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Tenant % no existe o no está activo', tenant_id;
    END IF;

    -- Establecer el tenant actual en el contexto de la sesión
    PERFORM set_config('app.current_tenant_id', tenant_id::TEXT, FALSE);

    -- Obtener y establecer el hospital_id asociado al tenant
    PERFORM set_config('app.current_hospital_id',
                      (SELECT id::TEXT FROM gestion_general.hospitales WHERE tenant_id = tenant_id LIMIT 1),
                      FALSE);

    -- Registrar acceso solo si la tabla existe
    BEGIN
        INSERT INTO tenant_config.tenant_access_log (tenant_id, session_id)
        VALUES (tenant_id, pg_backend_pid());
    EXCEPTION
        WHEN undefined_table THEN
            -- La tabla no existe todavía, ignoramos el error
            NULL;
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
COMMENT ON FUNCTION tenant_config.set_tenant_context IS 'Establece el contexto de ejecución para el tenant actual';

-- Tabla para registro de acceso a tenants
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'tenant_config'
        AND table_name = 'tenant_access_log'
    ) THEN
        CREATE TABLE tenant_config.tenant_access_log (
            id BIGSERIAL PRIMARY KEY,
            tenant_id BIGINT NOT NULL REFERENCES tenant_config.tenants(id),
            session_id BIGINT NOT NULL,
            access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address INET,
            user_agent TEXT,
            endpoint TEXT,
            status_code INTEGER
        );

        -- Añadir restricción de clave foránea si la tabla de tenants existe
        IF EXISTS (
            SELECT 1
            FROM information_schema.tables
            WHERE table_schema = 'tenant_config'
            AND table_name = 'tenants'
        ) THEN
            ALTER TABLE tenant_config.tenant_access_log
            ADD CONSTRAINT fk_tenant_access_log_tenant
            FOREIGN KEY (tenant_id)
            REFERENCES tenant_config.tenants(id)
            ON DELETE CASCADE;
        END IF;

        COMMENT ON TABLE tenant_config.tenant_access_log IS 'Registro de accesos a cada tenant para auditoría y seguridad';
        RAISE NOTICE 'Tabla tenant_config.tenant_access_log creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla tenant_config.tenant_access_log ya existe, omitiendo creación';

        -- Asegurarse de que las columnas necesarias existan
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_schema = 'tenant_config'
            AND table_name = 'tenant_access_log'
            AND column_name = 'ip_address'
        ) THEN
            ALTER TABLE tenant_config.tenant_access_log
            ADD COLUMN IF NOT EXISTS ip_address INET;
        END IF;

        -- Agregar más comprobaciones de columnas según sea necesario
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear la tabla tenant_config.tenant_access_log: %', SQLERRM;
END $$;

-- Crear índices para mejorar el rendimiento de las consultas
DO $$
BEGIN
    -- Índice para búsquedas por tenant_id
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenant_access_log'
        AND indexname = 'idx_tenant_access_log_tenant_id'
    ) THEN
        CREATE INDEX idx_tenant_access_log_tenant_id ON tenant_config.tenant_access_log(tenant_id);
        RAISE NOTICE 'Índice idx_tenant_access_log_tenant_id creado correctamente';
    END IF;

    -- Índice para búsquedas por rango de fechas
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenant_access_log'
        AND indexname = 'idx_tenant_access_log_access_time'
    ) THEN
        CREATE INDEX idx_tenant_access_log_access_time ON tenant_config.tenant_access_log(access_time);
        RAISE NOTICE 'Índice idx_tenant_access_log_access_time creado correctamente';
    END IF;

    -- Índice compuesto para consultas comunes
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenant_access_log'
        AND indexname = 'idx_tenant_access_log_tenant_time'
    ) THEN
        CREATE INDEX idx_tenant_access_log_tenant_time ON tenant_config.tenant_access_log(tenant_id, access_time);
        RAISE NOTICE 'Índice idx_tenant_access_log_tenant_time creado correctamente';
    END IF;

    -- Índice para búsquedas por estado
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenant_access_log'
        AND indexname = 'idx_tenant_access_log_status_code'
    ) THEN
        CREATE INDEX idx_tenant_access_log_status_code ON tenant_config.tenant_access_log(status_code);
        RAISE NOTICE 'Índice idx_tenant_access_log_status_code creado correctamente';
    END IF;

EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear índices para tenant_config.tenant_access_log: %', SQLERRM;
END $$;

-- Tabla de cuotas de recursos por tenant
DO $$
BEGIN
    -- Crear la tabla si no existe
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'tenant_config'
        AND table_name = 'tenant_quotas'
    ) THEN
        CREATE TABLE tenant_config.tenant_quotas (
            tenant_id BIGINT PRIMARY KEY,
            max_storage_gb INTEGER DEFAULT 10,
            max_users INTEGER DEFAULT 10,
            max_patients INTEGER DEFAULT 1000,
            max_connections INTEGER DEFAULT 50,
            features_enabled JSONB DEFAULT '{}'::jsonb,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_tenant_quotas_tenant
                FOREIGN KEY (tenant_id)
                REFERENCES tenant_config.tenants(id)
                ON DELETE CASCADE
        );

        COMMENT ON TABLE tenant_config.tenant_quotas IS 'Cuotas de recursos y características habilitadas por tenant';
        RAISE NOTICE 'Tabla tenant_config.tenant_quotas creada correctamente';
    ELSE
        RAISE NOTICE 'La tabla tenant_config.tenant_quotas ya existe, verificando estructura...';

        -- Añadir columnas faltantes si es necesario
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_schema = 'tenant_config'
            AND table_name = 'tenant_quotas'
            AND column_name = 'features_enabled'
        ) THEN
            ALTER TABLE tenant_config.tenant_quotas
            ADD COLUMN features_enabled JSONB DEFAULT '{}'::jsonb;
            RAISE NOTICE 'Columna features_enabled añadida a tenant_config.tenant_quotas';
        END IF;
    END IF;

    -- Crear índices para mejorar el rendimiento
    IF NOT EXISTS (
        SELECT 1
        FROM pg_indexes
        WHERE schemaname = 'tenant_config'
        AND tablename = 'tenant_quotas'
        AND indexname = 'idx_tenant_quotas_tenant_id'
    ) THEN
        CREATE INDEX idx_tenant_quotas_tenant_id ON tenant_config.tenant_quotas(tenant_id);
        RAISE NOTICE 'Índice idx_tenant_quotas_tenant_id creado correctamente';
    END IF;

EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear o modificar tenant_config.tenant_quotas: %', SQLERRM;
END $$;

-- Crear o reemplazar función para actualizar automáticamente updated_at
DO $$
BEGIN
    -- Intentar eliminar la función si existe para asegurar una recreación limpia.
    -- Usamos un bloque anidado BEGIN/EXCEPTION para que el DO principal no falle si el DROP falla.
    BEGIN
        DROP FUNCTION IF EXISTS tenant_config.update_tenant_quotas_updated_at();
        RAISE NOTICE 'Función tenant_config.update_tenant_quotas_updated_at() eliminada si existía (o no se encontró).';
    EXCEPTION
        WHEN OTHERS THEN
            RAISE NOTICE 'No se pudo eliminar la función tenant_config.update_tenant_quotas_updated_at() (puede que no exista o haya dependencias): %', SQLERRM;
    END;

EXCEPTION
    WHEN OTHERS THEN
        RAISE WARNING 'Error en el bloque DO para eliminar la función tenant_config.update_tenant_quotas_updated_at(): %', SQLERRM;
END $$;

CREATE OR REPLACE FUNCTION tenant_config.update_tenant_quotas_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Crear trigger solo si no existe
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_trigger
        WHERE tgname = 'update_tenant_quotas_updated_at'
        AND tgrelid = 'tenant_config.tenant_quotas'::regclass
    ) THEN
        CREATE TRIGGER update_tenant_quotas_updated_at
        BEFORE UPDATE ON tenant_config.tenant_quotas
        FOR EACH ROW EXECUTE FUNCTION tenant_config.update_tenant_quotas_updated_at();

        RAISE NOTICE 'Trigger update_tenant_quotas_updated_at creado correctamente';
    ELSE
        RAISE NOTICE 'El trigger update_tenant_quotas_updated_at ya existe, omitiendo creación';
    END IF;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al crear el trigger update_tenant_quotas_updated_at: %', SQLERRM;
END $$;

COMMIT;

-- Función para obtener o crear cuotas por defecto para un tenant
CREATE OR REPLACE FUNCTION tenant_config.obtener_o_crear_cuotas(
    p_tenant_id BIGINT
)
RETURNS tenant_config.tenant_quotas AS $$
DECLARE
    v_cuota tenant_config.tenant_quotas%ROWTYPE;
BEGIN
    -- Verificar si el tenant existe
    IF NOT EXISTS (SELECT 1 FROM tenant_config.tenants WHERE id = p_tenant_id) THEN
        RAISE EXCEPTION 'El tenant con ID % no existe', p_tenant_id;
    END IF;

    -- Bloqueo para evitar condiciones de carrera
    LOCK TABLE tenant_config.tenant_quotas IN SHARE ROW EXCLUSIVE MODE;

    -- Intentar obtener las cuotas existentes
    SELECT * INTO v_cuota
    FROM tenant_config.tenant_quotas
    WHERE tenant_id = p_tenant_id;

    -- Si no existen, crear unas nuevas con valores por defecto
    IF NOT FOUND THEN
        INSERT INTO tenant_config.tenant_quotas (tenant_id)
        VALUES (p_tenant_id)
        RETURNING * INTO v_cuota;

        RAISE NOTICE 'Cuotas por defecto creadas para el tenant %', p_tenant_id;
    END IF;

    RETURN v_cuota;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al obtener o crear cuotas para el tenant %: %', p_tenant_id, SQLERRM;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Función para verificar si una característica está habilitada para un tenant
CREATE OR REPLACE FUNCTION tenant_config.caracteristica_habilitada(
    p_tenant_id BIGINT,
    p_feature_name TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    v_enabled BOOLEAN;
BEGIN
    -- Verificar si la característica está habilitada explícitamente
    SELECT COALESCE((features_enabled->>p_feature_name)::BOOLEAN, FALSE) INTO v_enabled
    FROM tenant_config.tenant_quotas
    WHERE tenant_id = p_tenant_id;

    RETURN v_enabled;
EXCEPTION WHEN OTHERS THEN
    RAISE WARNING 'Error al verificar la característica % para el tenant %: %',
        p_feature_name, p_tenant_id, SQLERRM;
    RETURN FALSE;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER;

COMMIT;

-- ### Tipos ENUM Unificados ###
BEGIN;
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'sexo_paciente' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'gestion_general')) THEN
        CREATE TYPE gestion_general.sexo_paciente AS ENUM ('M', 'F', 'O');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_cita' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.estado_cita AS ENUM ('Programada', 'Cancelada', 'Realizada', 'No Asistió');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_factura' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'financiero')) THEN
        CREATE TYPE financiero.estado_factura AS ENUM ('Generada', 'Validada', 'Pagada', 'Rechazada');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_diagnostico' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.tipo_diagnostico AS ENUM ('Primario', 'Secundario', 'Terciario');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_cama' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.estado_cama AS ENUM ('Libre', 'Ocupada', 'Reservada', 'En Limpieza');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_especialidad' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'gestion_general')) THEN
        CREATE TYPE gestion_general.tipo_especialidad AS ENUM ('Clínica', 'Quirúrgica', 'Diagnóstica', 'Pediatría', 'Salud Mental', 'Emergente');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_urgencia' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.estado_urgencia AS ENUM ('En Observación', 'Alta', 'Hospitalizado', 'Crítico');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_recurso' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'inventario')) THEN
        CREATE TYPE inventario.tipo_recurso AS ENUM ('Equipo', 'Instrumento', 'Dispositivo', 'Consumible');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_recurso' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'inventario')) THEN
        CREATE TYPE inventario.estado_recurso AS ENUM ('Operativo', 'En Mantenimiento', 'Fuera de Servicio');
    END IF;

    -- Cambiar nombre del ENUM para evitar conflicto con la tabla
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_documento_enum' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'gestion_general')) THEN
        CREATE TYPE gestion_general.tipo_documento_enum AS ENUM ('Licencia', 'Certificación', 'Permiso', 'Autorización');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_sensor' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.tipo_sensor AS ENUM ('Frecuencia Cardíaca', 'Presión Arterial', 'Temperatura', 'Oxígeno', 'Glucosa');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_pago' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'financiero')) THEN
        CREATE TYPE financiero.estado_pago AS ENUM ('Pendiente', 'Completado', 'Fallido');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_incidente' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'control_calidad')) THEN
        CREATE TYPE control_calidad.tipo_incidente AS ENUM ('Seguridad', 'Clínico', 'Administrativo');
    END IF;

    IF NOT EXISTS

 (SELECT 1 FROM pg_type WHERE typname = 'nivel_riesgo' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'control_calidad')) THEN
        CREATE TYPE control_calidad.nivel_riesgo AS ENUM ('Bajo', 'Medio', 'Alto', 'Crítico');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'regimen_paciente' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.regimen_paciente AS ENUM ('contributivo', 'subsidiado', 'vinculado', 'particular');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_generico' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'gestion_general')) THEN
        CREATE TYPE gestion_general.estado_generico AS ENUM ('Activo', 'Inactivo', 'En Revisión', 'Disponible', 'Ocupado', 'En Mantenimiento');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'categoria_residuo' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'erp')) THEN
        CREATE TYPE erp.categoria_residuo AS ENUM ('infeccioso', 'peligroso', 'común', 'biopeligroso');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_dian_detallado' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'financiero')) THEN
        CREATE TYPE financiero.estado_dian_detallado AS ENUM ('pendiente_validacion', 'validada', 'rechazada', 'correccion_requerida', 'anulada');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_quirofano' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'clinico')) THEN
        CREATE TYPE clinico.estado_quirofano AS ENUM ('Disponible', 'Ocupado', 'En_Mantenimiento');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_equipo' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'inventario')) THEN
        CREATE TYPE inventario.tipo_equipo AS ENUM ('Diagnóstico', 'Terapéutico', 'Soporte', 'Quirúrgico');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'estado_ambulancia' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'ambulancias')) THEN
        CREATE TYPE ambulancias.estado_ambulancia AS ENUM ('Disponible', 'En_Servicio', 'En_Mantenimiento', 'Fuera_Servicio');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tipo_ambulancia' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'ambulancias')) THEN
        CREATE TYPE ambulancias.tipo_ambulancia AS ENUM ('Básica', 'Medicalizada', 'Transporte_Asistencial');
    END IF;

    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'prioridad_servicio' AND typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'ambulancias')) THEN
        CREATE TYPE ambulancias.prioridad_servicio AS ENUM ('Baja', 'Media', 'Alta', 'Crítica');
    END IF;
END $$;
COMMIT;
-- ### Gestión General ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.hospitales (
    id BIGSERIAL PRIMARY KEY,
    tenant_id BIGINT NOT NULL REFERENCES tenant_config.tenants(id),
    nombre TEXT NOT NULL,
    codigo TEXT UNIQUE NOT NULL,
    nit VARCHAR(20) UNIQUE NOT NULL,
    direccion TEXT,
    telefono TEXT,
    email TEXT,
    ubicacion geometry(Point, 4326),
    categoria_ips TEXT CONSTRAINT chk_categoria_ips CHECK (categoria_ips IN ('consultorio', 'clinica', 'hospital1', 'hospital2', 'hospital3', 'hospital4')),
    codigo_habilitacion TEXT UNIQUE NOT NULL,
    estado_registro TEXT CONSTRAINT chk_estado_registro CHECK (estado_registro IN ('activo', 'inactivo')),
    fecha_inscripcion_reps DATE,
    fecha_vencimiento_habilitacion DATE,
    entidad_territorial TEXT,
    idioma_predeterminado VARCHAR(5) DEFAULT 'es',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE gestion_general.hospitales IS 'Almacena los datos de las IPS registradas como tenants en el sistema. Alineado con REPS (Resolución 3100/2019).';
CREATE INDEX IF NOT EXISTS idx_hospitales_tenant_id ON gestion_general.hospitales(tenant_id);
COMMIT;

-- Función para crear automáticamente un hospital cuando se crea un tenant
BEGIN;
CREATE OR REPLACE FUNCTION tenant_config.create_hospital_for_tenant()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO gestion_general.hospitales (
        tenant_id, nombre, codigo, nit, codigo_habilitacion, estado_registro
    ) VALUES (
        NEW.id, NEW.nombre, 'H' || NEW.id, '000000000', 'CH' || NEW.id, 'activo'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
COMMIT;

-- Crear trigger solo si no existe
BEGIN;
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger
        WHERE tgname = 'trigger_create_hospital'
        AND tgrelid = 'tenant_config.tenants'::regclass
    ) THEN
        EXECUTE 'CREATE TRIGGER trigger_create_hospital
                AFTER INSERT ON tenant_config.tenants
                FOR EACH ROW
                EXECUTE FUNCTION tenant_config.create_hospital_for_tenant()';
    END IF;
END $$;
COMMIT;

-- ### Tablas de Referencia Geográfica ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.pais (
    id CHAR(3) PRIMARY KEY,               -- Código ISO del país (ej. 'COL')
    nombre VARCHAR(100) NOT NULL          -- Nombre completo del país
);

CREATE TABLE IF NOT EXISTS gestion_general.departamento (
    id CHAR(2) PRIMARY KEY,               -- Código del departamento (ej. '05' para Antioquia)
    nombre VARCHAR(100) NOT NULL,         -- Nombre del departamento
    pais_id CHAR(3) NOT NULL REFERENCES gestion_general.pais(id)
);

CREATE TABLE IF NOT EXISTS gestion_general.municipio (
    id CHAR(5) PRIMARY KEY,               -- Código del municipio (ej. '05001' para Medellín)
    nombre VARCHAR(100) NOT NULL,         -- Nombre del municipio
    departamento_id CHAR(2) NOT NULL REFERENCES gestion_general.departamento(id)
);

CREATE TABLE IF NOT EXISTS gestion_general.zona_territorial (
    id CHAR(2) PRIMARY KEY,               -- Código de la zona (ej. 'U', 'R')
    descripcion VARCHAR(50) NOT NULL      -- Descripción de la zona (ej. 'Urbana', 'Rural')
);
COMMIT;
-- ### Tablas de Referencia Normativa y Catálogos ###
BEGIN;
CREATE TABLE IF NOT EXISTS gestion_general.tipo_documento (
    id CHAR(2) PRIMARY KEY,               -- Código del tipo de documento (ej. 'CC', 'TI')
    descripcion VARCHAR(50) NOT NULL      -- Descripción (ej. 'Cédula de Ciudadanía')
);

CREATE TABLE IF NOT EXISTS gestion_general.sexo (
    id CHAR(1) PRIMARY KEY,               -- Código del sexo (ej. 'M', 'F', 'O')
    descripcion VARCHAR(50) NOT NULL      -- Descripción (ej. 'Masculino', 'Femenino', 'Otro')
);

CREATE TABLE IF NOT EXISTS gestion_general.eps (
    nit VARCHAR(9) PRIMARY KEY,           -- NIT de la EPS
    nombre VARCHAR(100) NOT NULL          -- Nombre de la EPS
);

CREATE TABLE IF NOT EXISTS gestion_general.cie10 (
    id VARCHAR(10) PRIMARY KEY,           -- Código CIE-10 (ej. 'A00.0')
    descripcion TEXT NOT NULL             -- Descripción del diagnóstico
);

CREATE TABLE IF NOT EXISTS gestion_general.cups (
    id VARCHAR(10) PRIMARY KEY,           -- Código CUPS (ej. '890101')
    descripcion TEXT NOT NULL             -- Descripción del procedimiento
);

CREATE TABLE IF NOT EXISTS gestion_general.tipo_usuario (
    id CHAR(2) PRIMARY KEY,               -- Código del tipo de usuario (ej. '01')
    descripcion VARCHAR(50) NOT NULL      -- Descripción (ej. 'Contributivo')
);

CREATE TABLE IF NOT EXISTS gestion_general.tipo_nota (
    id CHAR(2) PRIMARY KEY,               -- Código del tipo de nota (ej. '01')
    descripcion VARCHAR(50) NOT NULL      -- Descripción (ej. 'Nota de Evolución')
);

CREATE TABLE IF NOT EXISTS gestion_general.IPSCodHabilitacion (
    id VARCHAR(12) PRIMARY KEY,           -- Código de habilitación
    nombre VARCHAR(100) NOT NULL,
    municipio_id CHAR(5) NOT NULL REFERENCES gestion_general.municipio(id)
);

CREATE TABLE IF NOT EXISTS gestion_general.IPSnoREPS (
    id VARCHAR(12) PRIMARY KEY,
    nombre VARCHAR(100) NOT NULL,
    municipio_id CHAR(5) NOT NULL REFERENCES gestion_general.municipio(id)
);

CREATE TABLE IF NOT EXISTS gestion_general.ModalidadAtencion (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.GrupoServicios (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.ServiciosComplementarios (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL,
    grupo_servicio_id CHAR(2) NOT NULL REFERENCES gestion_general.GrupoServicios(id)
);

CREATE TABLE IF NOT EXISTS gestion_general.RIPSFinalidadConsulta (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.RIPSCausaExterna (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.RIPSTipoDiagnosticoPrincipal (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.ConceptoRecaudo (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.ViaIngresoUsuario (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.CondicionyDestinoUsuarioEgreso (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.TipoMedicamentoPOS (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.IUM (
    id VARCHAR(20) PRIMARY KEY,
    descripcion TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.CatalogoCUM (
    id VARCHAR(20) PRIMARY KEY,
    descripcion TEXT NOT NULL,
    ium_id VARCHAR(20) NOT NULL REFERENCES gestion_general.IUM(id)
);

CREATE TABLE IF NOT EXISTS gestion_general.DCI (
    id VARCHAR(20) PRIMARY KEY,
    descripcion TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.UMM (
    id VARCHAR(10) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.FFM (
    id VARCHAR(10) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.UPR (
    id VARCHAR(10) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.TipoOtrosServicios (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(50) NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.IDM (
    id VARCHAR(20) PRIMARY KEY,
    descripcion TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS gestion_general.LstSiNo (
    id CHAR(2) PRIMARY KEY,
    descripcion VARCHAR(3) NOT NULL CONSTRAINT chk_lst_si_no_check_descripcion CHECK (descripcion IN ('Sí', 'No'))
);
COMMIT;