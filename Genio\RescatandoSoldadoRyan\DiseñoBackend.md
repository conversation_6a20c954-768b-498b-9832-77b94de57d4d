# 🏥 DISEÑO BACKEND SISTEMA HIPÓCRATES

## 📋 ÍNDICE
1. [Arquitectura General](#arquitectura-general)
2. [Stack Tecnológico](#stack-tecnológico)
3. [Estructura del Proyecto](#estructura-del-proyecto)
4. [Etapas de Desarrollo](#etapas-de-desarrollo)
5. [Configuración de Seguridad](#configuración-de-seguridad)
6. [APIs y Endpoints](#apis-y-endpoints)
7. [Integración con Base de Datos](#integración-con-base-de-datos)

---

## 🏗️ ARQUITECTURA GENERAL

### Patrón Arquitectónico: **Clean Architecture + Microservicios Modulares**

```
┌─────────────────────────────────────────────────────────────┐
│                    API GATEWAY                              │
│                 (Autenticación JWT)                         │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────┼───────────────────────────────────────┐
│                CORE SERVICES                                │
├─────────────────────┼───────────────────────────────────────┤
│  Auth Service       │  Tenant Service    │  Audit Service   │
│  User Service       │  Hospital Service  │  Config Service  │
└─────────────────────┼───────────────────────────────────────┘
                      │
┌─────────────────────┼───────────────────────────────────────┐
│              BUSINESS MODULES                               │
├─────────────────────┼───────────────────────────────────────┤
│  Clínico           │  Inventario       │  Financiero       │
│  Ambulancias       │  Telemedicina     │  ERP              │
└─────────────────────┼───────────────────────────────────────┘
                      │
┌─────────────────────┼───────────────────────────────────────┐
│               DATA LAYER                                    │
├─────────────────────┼───────────────────────────────────────┤
│  PostgreSQL + PostGIS  │  Redis Cache   │  File Storage    │
└─────────────────────────────────────────────────────────────┘
```

---

## 🛠️ STACK TECNOLÓGICO

### **Backend Framework:** Node.js + TypeScript + Express.js
**Justificación:** 
- Ecosistema maduro y amplia comunidad
- TypeScript para tipado fuerte y mejor mantenibilidad
- Excelente soporte para PostgreSQL y PostGIS
- Facilidad para implementar microservicios

### **Base de Datos:** PostgreSQL 15+ con PostGIS
- **ORM:** Prisma (para tipado automático y migraciones)
- **Query Builder:** Knex.js (para consultas complejas)
- **Conexiones:** pg-pool para connection pooling

### **Autenticación y Seguridad:**
- **JWT** con refresh tokens
- **bcrypt** para hashing de contraseñas
- **helmet** para headers de seguridad
- **rate-limiting** con express-rate-limit

### **Validación y Documentación:**
- **Zod** para validación de esquemas
- **Swagger/OpenAPI** para documentación
- **Jest** para testing

### **Monitoreo y Logging:**
- **Winston** para logging estructurado
- **Prometheus** para métricas
- **Sentry** para error tracking

---

## 📁 ESTRUCTURA DEL PROYECTO

```
hipocrates-backend/
├── src/
│   ├── core/                          # Servicios centrales
│   │   ├── auth/                      # Autenticación y autorización
│   │   ├── tenant/                    # Gestión multi-tenant
│   │   ├── audit/                     # Auditoría y logging
│   │   └── config/                    # Configuraciones
│   ├── modules/                       # Módulos de negocio
│   │   ├── clinico/                   # Módulo clínico
│   │   ├── ambulancias/               # Módulo ambulancias
│   │   ├── telemedicina/              # Módulo telemedicina
│   │   ├── inventario/                # Módulo inventario
│   │   ├── financiero/                # Módulo financiero
│   │   └── erp/                       # Módulo ERP
│   ├── shared/                        # Código compartido
│   │   ├── database/                  # Configuración DB
│   │   ├── middleware/                # Middlewares
│   │   ├── utils/                     # Utilidades
│   │   ├── types/                     # Tipos TypeScript
│   │   └── validators/                # Validadores Zod
│   ├── infrastructure/                # Infraestructura
│   │   ├── cache/                     # Redis
│   │   ├── storage/                   # Almacenamiento archivos
│   │   └── external/                  # APIs externas
│   └── app.ts                         # Aplicación principal
├── prisma/                            # Esquemas Prisma
├── migrations/                        # Migraciones SQL
├── tests/                            # Tests
├── docs/                             # Documentación
└── docker/                           # Configuración Docker
```

---

## 🚀 ETAPAS DE DESARROLLO

### **ETAPA 1: FUNDACIÓN (Semana 1-2)**
**Objetivo:** Configuración básica y autenticación funcional

#### **1.1 Configuración Inicial**
- [ ] Inicializar proyecto Node.js + TypeScript
- [ ] Configurar ESLint, Prettier, Husky
- [ ] Configurar Docker y docker-compose
- [ ] Configurar variables de entorno

#### **1.2 Base de Datos**
- [ ] Configurar conexión PostgreSQL
- [ ] Implementar Prisma schema básico
- [ ] Crear migraciones iniciales
- [ ] Configurar connection pooling

#### **1.3 Autenticación Básica**
- [ ] Implementar registro/login de usuarios
- [ ] JWT token generation/validation
- [ ] Middleware de autenticación
- [ ] Gestión de refresh tokens

#### **1.4 Multi-tenancy Básico**
- [ ] Middleware de tenant detection
- [ ] Row Level Security setup
- [ ] Context de hospital actual

**Entregables:**
- API de login/logout funcional
- Conexión a base de datos establecida
- Documentación Swagger básica
- Tests unitarios de autenticación

---

### **ETAPA 2: CORE SERVICES (Semana 3-4)**
**Objetivo:** Servicios centrales y gestión de usuarios

#### **2.1 Gestión de Usuarios**
- [ ] CRUD completo de usuarios
- [ ] Gestión de roles y permisos
- [ ] Encriptación de datos sensibles
- [ ] Validaciones robustas

#### **2.2 Gestión de Hospitales**
- [ ] CRUD de hospitales/tenants
- [ ] Configuraciones por hospital
- [ ] Traducciones e internacionalización

#### **2.3 Auditoría**
- [ ] Sistema de auditoría automática
- [ ] Logging estructurado
- [ ] Tracking de cambios

#### **2.4 Seguridad Avanzada**
- [ ] Rate limiting
- [ ] Validación de entrada robusta
- [ ] Headers de seguridad
- [ ] CORS configurado

**Entregables:**
- Panel de administración básico
- Gestión completa de usuarios
- Sistema de auditoría funcional
- APIs documentadas

---

### **ETAPA 3: MÓDULO CLÍNICO BÁSICO (Semana 5-6)**
**Objetivo:** Funcionalidades clínicas esenciales

#### **3.1 Gestión de Pacientes**
- [ ] CRUD de pacientes con encriptación
- [ ] Búsqueda y filtros avanzados
- [ ] Validaciones RIPS
- [ ] Gestión de consentimientos

#### **3.2 Citas Médicas**
- [ ] Programación de citas
- [ ] Gestión de disponibilidad
- [ ] Notificaciones básicas
- [ ] Estados de citas

#### **3.3 Consultas**
- [ ] Registro de consultas
- [ ] Integración con historias clínicas
- [ ] Diagnósticos CIE-10
- [ ] Procedimientos CUPS

**Entregables:**
- Gestión completa de pacientes
- Sistema de citas funcional
- Registro de consultas
- Validaciones normativas

---

### **ETAPA 4: AMBULANCIAS GPS (Semana 7-8)**
**Objetivo:** Sistema de ambulancias con geolocalización

#### **4.1 Gestión de Ambulancias**
- [ ] CRUD de ambulancias
- [ ] Gestión de estados
- [ ] Información de equipamiento
- [ ] Mantenimiento

#### **4.2 Geolocalización**
- [ ] API de actualización de ubicación
- [ ] Funciones PostGIS
- [ ] Cálculo de distancias
- [ ] Rutas GPS

#### **4.3 Servicios de Ambulancia**
- [ ] Creación de servicios
- [ ] Asignación automática
- [ ] Seguimiento en tiempo real
- [ ] Estados del servicio

#### **4.4 WebSocket para Tiempo Real**
- [ ] Configurar Socket.io
- [ ] Eventos de ubicación
- [ ] Notificaciones push
- [ ] Dashboard en tiempo real

**Entregables:**
- Sistema de ambulancias completo
- Geolocalización en tiempo real
- APIs de seguimiento
- Dashboard de monitoreo

---

### **ETAPA 5: TELEMEDICINA (Semana 9-10)**
**Objetivo:** Plataforma de telemedicina

#### **5.1 Teleconsultas**
- [ ] Programación de teleconsultas
- [ ] Integración con plataformas de video
- [ ] Gestión de salas virtuales
- [ ] Grabaciones y consentimientos

#### **5.2 Monitoreo Remoto**
- [ ] Programas de monitoreo
- [ ] Integración con dispositivos IoT
- [ ] Alertas automáticas
- [ ] Reportes de seguimiento

**Entregables:**
- Plataforma de teleconsultas
- Sistema de monitoreo remoto
- Integraciones con video
- Cumplimiento normativo

---

### **ETAPA 6: MÓDULOS FINANCIERO E INVENTARIO (Semana 11-12)**
**Objetivo:** Gestión financiera e inventario

#### **6.1 Facturación Electrónica**
- [ ] Generación de facturas DIAN
- [ ] Firma digital
- [ ] Códigos QR
- [ ] Estados de facturación

#### **6.2 Inventario**
- [ ] Gestión de medicamentos
- [ ] Control de stock
- [ ] Dispensaciones
- [ ] Alertas de inventario

#### **6.3 Pagos**
- [ ] Registro de pagos
- [ ] Conciliación
- [ ] Reportes financieros

**Entregables:**
- Sistema de facturación DIAN
- Gestión completa de inventario
- Módulo de pagos
- Reportes financieros

---

### **ETAPA 7: ERP COMPLETO (Semana 13-14)**
**Objetivo:** Sistema ERP integral

#### **7.1 Recursos Humanos**
- [ ] Gestión de empleados
- [ ] Nómina automatizada
- [ ] Reportes laborales
- [ ] Seguridad social

#### **7.2 Contabilidad**
- [ ] Plan de cuentas
- [ ] Asientos contables
- [ ] Balances automáticos
- [ ] Reportes contables

#### **7.3 Presupuestos**
- [ ] Creación de presupuestos
- [ ] Control de ejecución
- [ ] Alertas presupuestales
- [ ] Análisis de variaciones

**Entregables:**
- Sistema de RRHH completo
- Módulo contable funcional
- Gestión presupuestal
- Reportes ejecutivos

---

### **ETAPA 8: OPTIMIZACIÓN Y PRODUCCIÓN (Semana 15-16)**
**Objetivo:** Preparación para producción

#### **8.1 Performance**
- [ ] Optimización de consultas
- [ ] Implementar cache Redis
- [ ] Compresión de respuestas
- [ ] Lazy loading

#### **8.2 Monitoreo**
- [ ] Métricas Prometheus
- [ ] Dashboards Grafana
- [ ] Alertas automáticas
- [ ] Health checks

#### **8.3 Seguridad Final**
- [ ] Penetration testing
- [ ] Audit de seguridad
- [ ] Backup automático
- [ ] Disaster recovery

#### **8.4 Documentación**
- [ ] Documentación completa API
- [ ] Guías de despliegue
- [ ] Manual de operaciones
- [ ] Troubleshooting

**Entregables:**
- Sistema optimizado para producción
- Monitoreo completo
- Documentación exhaustiva
- Plan de contingencia

---

## 🔐 CONFIGURACIÓN DE SEGURIDAD

### **Autenticación JWT**
```typescript
interface JWTPayload {
  userId: string;
  hospitalId: string;
  roles: string[];
  permissions: string[];
  exp: number;
}
```

### **Row Level Security**
```sql
-- Configuración automática por middleware
SET app.current_hospital_id = '1';
SET app.current_user_id = '123';
```

### **Encriptación de Datos**
- Datos sensibles encriptados con AES-256-CBC
- Claves rotativas por tenant
- Funciones de encriptación/desencriptación automáticas

---

## 📡 APIS Y ENDPOINTS PRINCIPALES

### **Core APIs**
```
POST   /api/auth/login
POST   /api/auth/logout
POST   /api/auth/refresh
GET    /api/users/profile
PUT    /api/users/profile
```

### **Clínico APIs**
```
GET    /api/patients
POST   /api/patients
GET    /api/patients/:id
PUT    /api/patients/:id
GET    /api/appointments
POST   /api/appointments
```

### **Ambulancias APIs**
```
GET    /api/ambulances
POST   /api/ambulances/:id/location
GET    /api/ambulances/:id/route
POST   /api/ambulance-services
GET    /api/ambulance-services/:id/track
```

### **WebSocket Events**
```
ambulance:location:update
ambulance:service:status
patient:alert
system:notification
```

---

## 🗄️ INTEGRACIÓN CON BASE DE DATOS

### **Prisma Configuration**
```typescript
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}
```

### **Connection Pooling**
```typescript
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});
```

### **Migrations Strategy**
- Usar Prisma para cambios de esquema
- SQL directo para funciones complejas
- Versionado semántico de migraciones
- Rollback automático en caso de error

---

## 📊 MÉTRICAS Y MONITOREO

### **KPIs Técnicos**
- Response time < 200ms (95th percentile)
- Uptime > 99.9%
- Error rate < 0.1%
- Database connections < 80% pool

### **KPIs de Negocio**
- Usuarios activos diarios
- Consultas registradas
- Ambulancias en servicio
- Facturas generadas

---

## 🚀 COMANDOS DE DESARROLLO

```bash
# Desarrollo
npm run dev

# Testing
npm run test
npm run test:watch
npm run test:coverage

# Base de datos
npm run db:migrate
npm run db:seed
npm run db:studio

# Producción
npm run build
npm run start
```

---

## 💻 EJEMPLOS DE CÓDIGO CLAVE

### **Middleware de Autenticación**
```typescript
import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';

interface AuthRequest extends Request {
  user?: {
    id: string;
    hospitalId: string;
    roles: string[];
  };
}

export const authenticateToken = (req: AuthRequest, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Token de acceso requerido' });
  }

  jwt.verify(token, process.env.JWT_SECRET!, (err, decoded) => {
    if (err) {
      return res.status(403).json({ error: 'Token inválido' });
    }

    req.user = decoded as any;
    // Configurar contexto de base de datos
    req.db.raw('SET app.current_hospital_id = ?', [req.user.hospitalId]);
    req.db.raw('SET app.current_user_id = ?', [req.user.id]);

    next();
  });
};
```

### **Servicio de Geolocalización**
```typescript
import { Point } from 'geojson';

export class GeoLocationService {
  async updateAmbulanceLocation(
    ambulanceId: string,
    latitude: number,
    longitude: number,
    metadata?: {
      speed?: number;
      heading?: number;
      accuracy?: number;
    }
  ) {
    const location: Point = {
      type: 'Point',
      coordinates: [longitude, latitude]
    };

    // Actualizar ubicación en base de datos
    await this.db.raw(`
      SELECT ambulancias.actualizar_ubicacion_ambulancia(?, ?, ?, ?, ?, ?, ?)
    `, [
      ambulanceId,
      latitude,
      longitude,
      metadata?.accuracy,
      metadata?.speed,
      metadata?.heading,
      'GPS'
    ]);

    // Emitir evento WebSocket
    this.socketService.emit('ambulance:location:update', {
      ambulanceId,
      location,
      timestamp: new Date(),
      metadata
    });
  }

  async findNearestAmbulances(
    location: Point,
    radiusKm: number = 10,
    status: string[] = ['Disponible']
  ) {
    return await this.db.raw(`
      SELECT a.*,
             ambulancias.calcular_distancia_km(a.ubicacion_actual, ST_GeomFromGeoJSON(?)) as distancia_km
      FROM ambulancias.ambulancias a
      WHERE a.estado = ANY(?)
        AND ST_DWithin(
          a.ubicacion_actual::geography,
          ST_GeomFromGeoJSON(?)::geography,
          ? * 1000
        )
      ORDER BY distancia_km ASC
    `, [JSON.stringify(location), status, JSON.stringify(location), radiusKm]);
  }
}
```

### **Controlador de Pacientes**
```typescript
import { z } from 'zod';

const CreatePatientSchema = z.object({
  tipoIdentificacion: z.string(),
  numeroIdentificacion: z.string(),
  primerNombre: z.string().min(1),
  segundoNombre: z.string().optional(),
  primerApellido: z.string().min(1),
  segundoApellido: z.string().optional(),
  fechaNacimiento: z.string().datetime(),
  sexo: z.enum(['M', 'F', 'O']),
  direccion: z.string().optional(),
  telefono: z.string().optional(),
  email: z.string().email().optional(),
  eps: z.string().optional(),
  regimen: z.enum(['contributivo', 'subsidiado', 'vinculado', 'particular'])
});

export class PatientController {
  async create(req: AuthRequest, res: Response) {
    try {
      const validatedData = CreatePatientSchema.parse(req.body);

      // Encriptar datos sensibles
      const encryptedData = {
        ...validatedData,
        numeroIdentificacion: await this.encryptionService.encrypt(
          validatedData.numeroIdentificacion,
          req.user!.hospitalId
        ),
        direccion: validatedData.direccion ?
          await this.encryptionService.encrypt(validatedData.direccion, req.user!.hospitalId) : null,
        telefono: validatedData.telefono ?
          await this.encryptionService.encrypt(validatedData.telefono, req.user!.hospitalId) : null,
        email: validatedData.email ?
          await this.encryptionService.encrypt(validatedData.email, req.user!.hospitalId) : null
      };

      const patient = await this.patientService.create(encryptedData, req.user!.hospitalId);

      res.status(201).json({
        success: true,
        data: patient,
        message: 'Paciente creado exitosamente'
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          success: false,
          errors: error.errors,
          message: 'Datos de entrada inválidos'
        });
      }

      this.logger.error('Error creating patient:', error);
      res.status(500).json({
        success: false,
        message: 'Error interno del servidor'
      });
    }
  }

  async search(req: AuthRequest, res: Response) {
    try {
      const { q, page = 1, limit = 10 } = req.query;

      const results = await this.patientService.search({
        query: q as string,
        hospitalId: req.user!.hospitalId,
        pagination: {
          page: parseInt(page as string),
          limit: parseInt(limit as string)
        }
      });

      res.json({
        success: true,
        data: results.patients,
        pagination: {
          page: results.page,
          limit: results.limit,
          total: results.total,
          totalPages: Math.ceil(results.total / results.limit)
        }
      });
    } catch (error) {
      this.logger.error('Error searching patients:', error);
      res.status(500).json({
        success: false,
        message: 'Error en la búsqueda'
      });
    }
  }
}
```

### **WebSocket para Tiempo Real**
```typescript
import { Server as SocketIOServer } from 'socket.io';
import { authenticateSocketToken } from '../middleware/auth';

export class SocketService {
  private io: SocketIOServer;

  constructor(server: any) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL,
        methods: ['GET', 'POST']
      }
    });

    this.setupMiddleware();
    this.setupEventHandlers();
  }

  private setupMiddleware() {
    this.io.use(authenticateSocketToken);
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      const user = socket.data.user;

      // Unir a sala del hospital
      socket.join(`hospital:${user.hospitalId}`);

      // Eventos de ambulancias
      socket.on('ambulance:track', (ambulanceId) => {
        socket.join(`ambulance:${ambulanceId}`);
      });

      socket.on('ambulance:untrack', (ambulanceId) => {
        socket.leave(`ambulance:${ambulanceId}`);
      });

      // Eventos de pacientes
      socket.on('patient:monitor', (patientId) => {
        socket.join(`patient:${patientId}`);
      });

      socket.on('disconnect', () => {
        console.log(`Usuario ${user.id} desconectado`);
      });
    });
  }

  // Emitir actualización de ubicación de ambulancia
  emitAmbulanceLocationUpdate(ambulanceId: string, data: any) {
    this.io.to(`ambulance:${ambulanceId}`).emit('ambulance:location:update', data);
  }

  // Emitir alerta de paciente
  emitPatientAlert(hospitalId: string, patientId: string, alert: any) {
    this.io.to(`hospital:${hospitalId}`).emit('patient:alert', {
      patientId,
      alert,
      timestamp: new Date()
    });
  }

  // Emitir notificación del sistema
  emitSystemNotification(hospitalId: string, notification: any) {
    this.io.to(`hospital:${hospitalId}`).emit('system:notification', notification);
  }
}
```

---

## 🔧 CONFIGURACIÓN DE DESARROLLO

### **Docker Compose para Desarrollo**
```yaml
version: '3.8'
services:
  postgres:
    image: postgis/postgis:15-3.3
    environment:
      POSTGRES_DB: hipocrates_dev
      POSTGRES_USER: hipocrates
      POSTGRES_PASSWORD: hipocrates123
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  backend:
    build: .
    ports:
      - "3000:3000"
    environment:
      DATABASE_URL: ***************************************************/hipocrates_dev
      REDIS_URL: redis://redis:6379
      JWT_SECRET: your-super-secret-jwt-key
      NODE_ENV: development
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis

volumes:
  postgres_data:
  redis_data:
```

### **Package.json Scripts**
```json
{
  "scripts": {
    "dev": "nodemon --exec ts-node src/app.ts",
    "build": "tsc",
    "start": "node dist/app.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "db:migrate": "prisma migrate dev",
    "db:generate": "prisma generate",
    "db:studio": "prisma studio",
    "db:seed": "ts-node prisma/seed.ts",
    "lint": "eslint src/**/*.ts",
    "lint:fix": "eslint src/**/*.ts --fix"
  }
}
```

### **Variables de Entorno (.env)**
```env
# Base de datos
DATABASE_URL="postgresql://hipocrates:hipocrates123@localhost:5432/hipocrates_dev"
REDIS_URL="redis://localhost:6379"

# Autenticación
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_REFRESH_SECRET="your-refresh-secret-key"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Encriptación
ENCRYPTION_KEY="your-32-character-encryption-key"

# APIs externas
DIAN_API_URL="https://api.dian.gov.co"
DIAN_API_KEY="your-dian-api-key"

# Configuración del servidor
PORT=3000
NODE_ENV=development
CORS_ORIGIN="http://localhost:3001"

# Logging
LOG_LEVEL="debug"
LOG_FILE="logs/app.log"

# Monitoreo
SENTRY_DSN="your-sentry-dsn"
```

---

## 📋 CHECKLIST DE IMPLEMENTACIÓN

### **Etapa 1 - Fundación**
- [ ] ✅ Configurar proyecto TypeScript + Express
- [ ] ✅ Configurar Docker y docker-compose
- [ ] ✅ Configurar PostgreSQL + PostGIS
- [ ] ✅ Implementar autenticación JWT
- [ ] ✅ Configurar middleware de tenant
- [ ] ✅ Crear documentación Swagger básica
- [ ] ✅ Implementar tests unitarios básicos

### **Etapa 2 - Core Services**
- [ ] 🔄 CRUD de usuarios con roles
- [ ] 🔄 Gestión de hospitales/tenants
- [ ] 🔄 Sistema de auditoría
- [ ] 🔄 Encriptación de datos sensibles
- [ ] 🔄 Rate limiting y seguridad
- [ ] 🔄 Logging estructurado

### **Etapa 3 - Módulo Clínico**
- [ ] ⏳ Gestión de pacientes
- [ ] ⏳ Sistema de citas
- [ ] ⏳ Registro de consultas
- [ ] ⏳ Historias clínicas
- [ ] ⏳ Validaciones RIPS

### **Etapa 4 - Ambulancias GPS**
- [ ] ⏳ Gestión de ambulancias
- [ ] ⏳ Geolocalización PostGIS
- [ ] ⏳ WebSocket tiempo real
- [ ] ⏳ Servicios de ambulancia
- [ ] ⏳ Dashboard de monitoreo

**Leyenda:** ✅ Completado | 🔄 En progreso | ⏳ Pendiente

---

## 🎯 PRÓXIMOS PASOS RECOMENDADOS

1. **Configurar entorno de desarrollo** con Docker
2. **Implementar autenticación básica** (login/logout)
3. **Crear middleware de tenant** para multi-tenancy
4. **Desarrollar CRUD de usuarios** con roles
5. **Implementar gestión de pacientes** básica
6. **Agregar geolocalización** para ambulancias

¿Te gustaría que desarrolle alguna etapa específica en detalle o que creemos los archivos de configuración inicial para comenzar?
