{"name": "hipocrates-frontend", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^2.2.2", "@hookform/resolvers": "^3.3.4", "@tanstack/react-query": "^5.17.19", "@types/leaflet": "^1.9.17", "@types/react-datepicker": "^6.2.0", "@types/recharts": "^1.8.29", "axios": "^1.6.5", "date-fns": "^4.1.0", "leaflet": "^1.9.4", "lucide-react": "^0.511.0", "msw": "^2.0.13", "react": "^18.2.0", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "react-hot-toast": "^2.5.2", "react-leaflet": "^4.2.1", "react-router-dom": "^6.21.3", "react-select": "^5.10.1", "recharts": "^2.15.3", "tailwindcss": "^3.4.1", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@types/node": "^22.15.3", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.33", "prettier": "^3.2.4", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.2.1"}, "msw": {"workerDirectory": ["public"]}}