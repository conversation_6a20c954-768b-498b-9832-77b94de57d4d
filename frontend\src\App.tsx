import { useEffect } from 'react';
import { <PERSON>rowserRouter as Router } from 'react-router-dom';
import AppRoutes from './routes';
import { CustomAuthProvider } from './context/AuthContext';
import { initializeThemeVariables } from './utils/themeUtils';
import { ThemeProvider } from './components/layout/ThemeProvider';
import BackgroundImage from './components/ui/BackgroundImage';
import { Toaster } from './components/ui/Toaster';

function App() {
  // Inicializar las variables de tema al cargar la aplicación
  useEffect(() => {
    console.log('App mounted, initializing theme variables');

    // Asegurarse de que el cuerpo esté oculto inicialmente
    document.body.style.visibility = 'hidden';

    // Inicializar variables de tema (mantener compatibilidad)
    initializeThemeVariables();
  }, []);

  return (
    <ThemeProvider>
      {/* Componente de fondo con imagen y tinte */}
      <BackgroundImage />

      {/* Sistema de notificaciones global */}
      <Toaster />

      <Router>
        <CustomAuthProvider>
          <AppRoutes />
        </CustomAuthProvider>
      </Router>
    </ThemeProvider>
  );
}

export default App;
