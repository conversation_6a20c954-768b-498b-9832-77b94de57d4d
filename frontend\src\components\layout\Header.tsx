import { useState, useRef, useEffect, CSSProperties } from 'react';
import { useNavigate } from 'react-router-dom';
// import { Bell, Search, Settings } from 'lucide-react';
import { ThemeToggle } from '../ui/ThemeToggle';
import { UICustomizer } from '../ui/UICustomizer';
import { useTheme } from '../../hooks/useTheme';
// import { useAuth } from '../../context/AuthContext';

// Iconos SVG temporales hasta que lucide-react funcione
const SearchIcon = () => (
  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
);

const BellIcon = () => (
  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
  </svg>
);

const SettingsIcon = () => (
  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
);

// Icono discreto para personalización de UI
const PaletteIcon = () => (
  <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM7 3H5a2 2 0 00-2 2v12a4 4 0 004 4h2a2 2 0 002-2V5a2 2 0 00-2-2z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 9h6m-6 4h6m2-7h4a2 2 0 012 2v8a2 2 0 01-2 2h-4" />
  </svg>
);

export interface HeaderProps {
  glassStyle?: CSSProperties;
}

export const Header = ({ glassStyle }: HeaderProps) => {
  // Temporalmente, no usar el contexto de autenticación para depuración
  // const { user, logout } = useAuth();
  const user = { username: 'Usuario de prueba', hospital_id: '1' };
  const logout = () => console.log('Logout');
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const { isDark } = useTheme();

  // Cerrar el menú cuando se hace clic fuera de él
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Manejar clic en cambiar contraseña
  const handleChangePassword = () => {
    setIsMenuOpen(false);
    navigate('/perfil/cambiar-password');
  };

  // Manejar clic en cerrar sesión
  const handleLogout = () => {
    setIsMenuOpen(false);
    logout();
    navigate('/login');
  };

  return (
    <header
      className="h-16 flex items-center justify-between px-6 w-full bg-white border-b border-gray-200 shadow-sm"
    >
      {/* Logo y título */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">H</span>
          </div>
          <h1 className="text-gray-900 text-xl font-bold">Hipócrates</h1>
        </div>
      </div>

      {/* Barra de búsqueda central */}
      <div className="flex-1 max-w-md mx-8">
        <div className="relative">
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
            <SearchIcon />
          </div>
          <input
            type="text"
            placeholder="Buscar pacientes, citas, diagnósticos..."
            className="
              w-full pl-10 pr-4 py-2 bg-gray-50 border border-gray-300 rounded-lg
              text-gray-900 placeholder:text-gray-500
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent
              transition-all duration-200
            "
            onFocus={() => setIsSearchOpen(true)}
            onBlur={() => setIsSearchOpen(false)}
          />
          {isSearchOpen && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-50">
              <div className="p-3 text-sm text-gray-500">
                Comience a escribir para buscar...
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Acciones del header */}
      <div className="flex items-center space-x-3">
        {/* Theme Toggle - Simple */}
        <ThemeToggle size="md" variant="button" />

        {/* Personalización UI - Discreto */}
        <UICustomizer />

        {/* Notificaciones */}
        <div className="relative">
          <button className="
            p-2 rounded-lg bg-gray-50 border border-gray-300
            hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500
            transition-all duration-200
          ">
            <div className="text-gray-600">
              <BellIcon />
            </div>
          </button>
          <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full flex items-center justify-center">
            <span className="text-white text-xs font-bold">3</span>
          </span>
        </div>

        {/* Configuraciones */}
        <button className="
          p-2 rounded-lg bg-gray-50 border border-gray-300
          hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500
          transition-all duration-200
        ">
          <div className="text-gray-600">
            <SettingsIcon />
          </div>
        </button>

        {/* Menú de usuario */}
        <div className="relative" ref={menuRef}>
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="flex items-center space-x-3 p-2 rounded-lg bg-gray-50 border border-gray-300 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
          >
            <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center text-white font-medium">
              {user?.username.charAt(0).toUpperCase()}
            </div>
            <div className="hidden md:block text-left">
              <p className="text-gray-900 font-medium text-sm">{user?.username}</p>
              <p className="text-gray-500 text-xs">Hospital ID: {user?.hospital_id}</p>
            </div>
            <svg
              className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${isMenuOpen ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {isMenuOpen && (
            <div className="absolute right-0 mt-2 w-56 bg-white border border-gray-300 rounded-lg shadow-xl py-2 z-50">
              <div className="px-4 py-2 border-b border-gray-200">
                <p className="text-gray-900 font-medium">{user?.username}</p>
                <p className="text-gray-500 text-sm">Hospital ID: {user?.hospital_id}</p>
              </div>

              <button
                onClick={() => {
                  setIsMenuOpen(false);
                  navigate('/perfil');
                }}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
              >
                Ver Perfil
              </button>

              <button
                onClick={handleChangePassword}
                className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
              >
                Cambiar Contraseña
              </button>

              <div className="border-t border-gray-200 my-1"></div>

              <button
                onClick={handleLogout}
                className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200"
              >
                Cerrar Sesión
              </button>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};
