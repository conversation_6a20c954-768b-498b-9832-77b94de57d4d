import { ReactNode, useEffect } from 'react';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { Footer } from './Footer';
import { useSettingsStore } from '../../store/useSettingsStore';
import { useTheme } from '../../hooks/useTheme';

export interface LayoutProps {
  children: ReactNode;
}

export const Layout = ({ children }: LayoutProps) => {
  const {
    glassmorphismOpacity,
    glassmorphismColor,
    textContrast,
    backgroundTint,
    contentBackground
  } = useSettingsStore();

  const { theme, getThemeStyles } = useTheme();

  // Aplicar estilos de tema al cargar
  useEffect(() => {
    const styles = getThemeStyles();
    const root = document.documentElement;

    Object.entries(styles).forEach(([property, value]) => {
      root.style.setProperty(property, value as string);
    });
  }, [theme, getThemeStyles]);

  // Calcular el color del texto basado en el tinte del fondo
  const textColor = backgroundTint < 0
    ? `rgba(0, 0, 0, ${textContrast / 100})`
    : `rgba(255, 255, 255, ${textContrast / 100})`;

  // Definimos el estilo glassmorphism basado en la configuración
  const glassStyle = {
    backgroundColor: `rgba(${glassmorphismColor || '17, 25, 40'}, ${glassmorphismOpacity / 100})`,
    backdropFilter: `blur(${glassmorphismOpacity / 10 + 5}px) saturate(180%)`,
    WebkitBackdropFilter: `blur(${glassmorphismOpacity / 10 + 5}px) saturate(180%)`,
    borderRadius: '12px',
    border: '1px solid rgba(255, 255, 255, 0.125)',
    color: textColor
  };

  // Determinar la clase de fondo para el contenido
  const getContentBackgroundClass = () => {
    switch (contentBackground) {
      case 'glass':
        return 'glassmorphism';
      case 'solid':
        return 'bg-card';
      case 'elegant':
      default:
        return 'bg-elegant';
    }
  };

  return (
    <div className="flex min-h-screen overflow-hidden relative z-10 bg-gray-100 transition-colors duration-300">
      <Sidebar glassStyle={glassStyle} />
      <div className="flex flex-col flex-1 ml-64 overflow-hidden">
        <Header glassStyle={glassStyle} />
        <main className="flex-1 overflow-y-auto bg-gray-50 transition-colors duration-300">
          <div className="container mx-auto min-h-full">
            {children}
          </div>
        </main>
        <Footer glassStyle={glassStyle} />
      </div>
    </div>
  );
};
