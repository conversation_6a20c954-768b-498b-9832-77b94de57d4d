import React, { useEffect, ReactNode } from 'react';
import { useTheme } from '../../hooks/useTheme';
import { useSettingsStore } from '../../store/useSettingsStore';

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const { theme, getThemeStyles } = useTheme();
  const { setTheme } = useSettingsStore();

  // Inicializar tema al cargar la aplicación
  useEffect(() => {
    // Detectar preferencia del sistema si no hay configuración guardada
    const savedSettings = localStorage.getItem('hipocrates-ui-settings');
    if (!savedSettings) {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
      setTheme(systemTheme);
    }

    // Aplicar estilos de tema
    const styles = getThemeStyles();
    const root = document.documentElement;
    
    Object.entries(styles).forEach(([property, value]) => {
      root.style.setProperty(property, value as string);
    });

    // Aplicar atributo de tema
    root.setAttribute('data-theme', theme);
    root.classList.remove('light', 'dark');
    root.classList.add(theme);

    // Hacer visible el body después de aplicar estilos
    document.body.style.visibility = 'visible';
  }, [theme, getThemeStyles, setTheme]);

  // Escuchar cambios en la preferencia del sistema
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      // Solo cambiar automáticamente si el usuario no ha establecido una preferencia manual
      const hasManualPreference = localStorage.getItem('hipocrates-ui-settings');
      if (!hasManualPreference) {
        setTheme(e.matches ? 'dark' : 'light');
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [setTheme]);

  return <>{children}</>;
};

export default ThemeProvider;
