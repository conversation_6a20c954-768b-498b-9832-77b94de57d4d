import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCheck,
  faClock,
  faTimes,
  faExclamationTriangle,
  faSpinner,
  faEye,
  faPause,
  faPlay,
  faStop,
  faCheckCircle,
  faTimesCircle,
  faExclamationCircle,
  faInfoCircle
} from '@fortawesome/free-solid-svg-icons';

// Tipos de estados comunes en el sistema
export type EstadoGenerico = 
  | 'ACTIVO' | 'INACTIVO' | 'PENDIENTE' | 'COMPLETADO' | 'CANCELADO' | 'VENCIDO'
  | 'PAGADO' | 'IMPAGO' | 'ANULADO' | 'BORRADOR' | 'ENVIADO' | 'PROCESANDO'
  | 'APROBADO' | 'RECHAZADO' | 'EN_REVISION' | 'PAUSADO' | 'EN_PROGRESO'
  | 'DISPONIBLE' | 'OCUPADO' | 'MANTENIMIENTO' | 'FUERA_SERVICIO'
  | 'PROGRAMADO' | 'EN_CURSO' | 'FINALIZADO' | 'SUSPENDIDO';

// Configuración de colores y estilos para cada estado
const estadoConfig: Record<EstadoGenerico, {
  color: string;
  bgColor: string;
  icon: any;
  label: string;
}> = {
  // Estados generales
  ACTIVO: {
    color: 'text-green-700',
    bgColor: 'bg-green-100 border-green-200',
    icon: faCheckCircle,
    label: 'Activo'
  },
  INACTIVO: {
    color: 'text-gray-700',
    bgColor: 'bg-gray-100 border-gray-200',
    icon: faPause,
    label: 'Inactivo'
  },
  PENDIENTE: {
    color: 'text-yellow-700',
    bgColor: 'bg-yellow-100 border-yellow-200',
    icon: faClock,
    label: 'Pendiente'
  },
  COMPLETADO: {
    color: 'text-green-700',
    bgColor: 'bg-green-100 border-green-200',
    icon: faCheck,
    label: 'Completado'
  },
  CANCELADO: {
    color: 'text-red-700',
    bgColor: 'bg-red-100 border-red-200',
    icon: faTimes,
    label: 'Cancelado'
  },
  VENCIDO: {
    color: 'text-orange-700',
    bgColor: 'bg-orange-100 border-orange-200',
    icon: faExclamationTriangle,
    label: 'Vencido'
  },

  // Estados financieros
  PAGADO: {
    color: 'text-green-700',
    bgColor: 'bg-green-100 border-green-200',
    icon: faCheckCircle,
    label: 'Pagado'
  },
  IMPAGO: {
    color: 'text-red-700',
    bgColor: 'bg-red-100 border-red-200',
    icon: faTimesCircle,
    label: 'Impago'
  },
  ANULADO: {
    color: 'text-gray-700',
    bgColor: 'bg-gray-100 border-gray-200',
    icon: faTimesCircle,
    label: 'Anulado'
  },

  // Estados de documentos
  BORRADOR: {
    color: 'text-gray-700',
    bgColor: 'bg-gray-100 border-gray-200',
    icon: faEye,
    label: 'Borrador'
  },
  ENVIADO: {
    color: 'text-blue-700',
    bgColor: 'bg-blue-100 border-blue-200',
    icon: faCheckCircle,
    label: 'Enviado'
  },
  PROCESANDO: {
    color: 'text-blue-700',
    bgColor: 'bg-blue-100 border-blue-200',
    icon: faSpinner,
    label: 'Procesando'
  },

  // Estados de aprobación
  APROBADO: {
    color: 'text-green-700',
    bgColor: 'bg-green-100 border-green-200',
    icon: faCheckCircle,
    label: 'Aprobado'
  },
  RECHAZADO: {
    color: 'text-red-700',
    bgColor: 'bg-red-100 border-red-200',
    icon: faTimesCircle,
    label: 'Rechazado'
  },
  EN_REVISION: {
    color: 'text-yellow-700',
    bgColor: 'bg-yellow-100 border-yellow-200',
    icon: faExclamationCircle,
    label: 'En Revisión'
  },

  // Estados de proceso
  PAUSADO: {
    color: 'text-orange-700',
    bgColor: 'bg-orange-100 border-orange-200',
    icon: faPause,
    label: 'Pausado'
  },
  EN_PROGRESO: {
    color: 'text-blue-700',
    bgColor: 'bg-blue-100 border-blue-200',
    icon: faPlay,
    label: 'En Progreso'
  },

  // Estados de disponibilidad
  DISPONIBLE: {
    color: 'text-green-700',
    bgColor: 'bg-green-100 border-green-200',
    icon: faCheckCircle,
    label: 'Disponible'
  },
  OCUPADO: {
    color: 'text-red-700',
    bgColor: 'bg-red-100 border-red-200',
    icon: faTimesCircle,
    label: 'Ocupado'
  },
  MANTENIMIENTO: {
    color: 'text-yellow-700',
    bgColor: 'bg-yellow-100 border-yellow-200',
    icon: faExclamationTriangle,
    label: 'Mantenimiento'
  },
  FUERA_SERVICIO: {
    color: 'text-gray-700',
    bgColor: 'bg-gray-100 border-gray-200',
    icon: faStop,
    label: 'Fuera de Servicio'
  },

  // Estados de programación
  PROGRAMADO: {
    color: 'text-blue-700',
    bgColor: 'bg-blue-100 border-blue-200',
    icon: faClock,
    label: 'Programado'
  },
  EN_CURSO: {
    color: 'text-green-700',
    bgColor: 'bg-green-100 border-green-200',
    icon: faPlay,
    label: 'En Curso'
  },
  FINALIZADO: {
    color: 'text-green-700',
    bgColor: 'bg-green-100 border-green-200',
    icon: faCheck,
    label: 'Finalizado'
  },
  SUSPENDIDO: {
    color: 'text-orange-700',
    bgColor: 'bg-orange-100 border-orange-200',
    icon: faPause,
    label: 'Suspendido'
  }
};

export interface EstadoBadgeProps {
  estado: EstadoGenerico | string;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  customLabel?: string;
  className?: string;
}

export const EstadoBadge: React.FC<EstadoBadgeProps> = ({
  estado,
  size = 'md',
  showIcon = true,
  customLabel,
  className = ''
}) => {
  // Obtener configuración del estado o usar valores por defecto
  const config = estadoConfig[estado as EstadoGenerico] || {
    color: 'text-gray-700',
    bgColor: 'bg-gray-100 border-gray-200',
    icon: faInfoCircle,
    label: estado
  };

  // Clases de tamaño
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base'
  };

  // Clases de icono según tamaño
  const iconSizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  return (
    <span
      className={`
        inline-flex items-center gap-1.5 rounded-full border font-medium
        ${config.color} ${config.bgColor} ${sizeClasses[size]} ${className}
      `}
    >
      {showIcon && (
        <FontAwesomeIcon 
          icon={config.icon} 
          className={iconSizeClasses[size]}
        />
      )}
      {customLabel || config.label}
    </span>
  );
};

// Componente para mostrar múltiples estados
export interface MultipleEstadosProps {
  estados: Array<{
    estado: EstadoGenerico | string;
    label?: string;
  }>;
  size?: 'sm' | 'md' | 'lg';
  showIcons?: boolean;
  className?: string;
}

export const MultipleEstados: React.FC<MultipleEstadosProps> = ({
  estados,
  size = 'md',
  showIcons = true,
  className = ''
}) => {
  return (
    <div className={`flex flex-wrap gap-1 ${className}`}>
      {estados.map((item, index) => (
        <EstadoBadge
          key={index}
          estado={item.estado}
          size={size}
          showIcon={showIcons}
          customLabel={item.label}
        />
      ))}
    </div>
  );
};

// Hook para obtener configuración de estado
export const useEstadoConfig = (estado: EstadoGenerico | string) => {
  return estadoConfig[estado as EstadoGenerico] || {
    color: 'text-gray-700',
    bgColor: 'bg-gray-100 border-gray-200',
    icon: faInfoCircle,
    label: estado
  };
};

export default EstadoBadge;
