import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faExclamationTriangle,
  faTrash,
  faCheck,
  faTimes,
  faQuestion,
  faInfo,
  faExclamationCircle
} from '@fortawesome/free-solid-svg-icons';
import { Button } from './Button';
import { Input } from './Input';

export type TipoConfirmacion = 'eliminar' | 'anular' | 'aprobar' | 'rechazar' | 'confirmar' | 'advertencia' | 'info';

interface ModalConfirmacionProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (motivo?: string) => void;
  tipo: TipoConfirmacion;
  titulo?: string;
  mensaje?: string;
  requiereMotivo?: boolean;
  placeholderMotivo?: string;
  textoConfirmar?: string;
  textoCancelar?: string;
  isLoading?: boolean;
  children?: React.ReactNode;
}

const configuracionTipos: Record<TipoConfirmacion, {
  icon: any;
  iconColor: string;
  titulo: string;
  mensaje: string;
  textoConfirmar: string;
  colorBoton: 'primary' | 'danger' | 'warning' | 'success' | 'secondary';
}> = {
  eliminar: {
    icon: faTrash,
    iconColor: 'text-red-500',
    titulo: 'Confirmar Eliminación',
    mensaje: '¿Está seguro de que desea eliminar este elemento? Esta acción no se puede deshacer.',
    textoConfirmar: 'Eliminar',
    colorBoton: 'danger'
  },
  anular: {
    icon: faTimes,
    iconColor: 'text-orange-500',
    titulo: 'Confirmar Anulación',
    mensaje: '¿Está seguro de que desea anular este elemento?',
    textoConfirmar: 'Anular',
    colorBoton: 'warning'
  },
  aprobar: {
    icon: faCheck,
    iconColor: 'text-green-500',
    titulo: 'Confirmar Aprobación',
    mensaje: '¿Está seguro de que desea aprobar este elemento?',
    textoConfirmar: 'Aprobar',
    colorBoton: 'success'
  },
  rechazar: {
    icon: faTimes,
    iconColor: 'text-red-500',
    titulo: 'Confirmar Rechazo',
    mensaje: '¿Está seguro de que desea rechazar este elemento?',
    textoConfirmar: 'Rechazar',
    colorBoton: 'danger'
  },
  confirmar: {
    icon: faQuestion,
    iconColor: 'text-blue-500',
    titulo: 'Confirmar Acción',
    mensaje: '¿Está seguro de que desea realizar esta acción?',
    textoConfirmar: 'Confirmar',
    colorBoton: 'primary'
  },
  advertencia: {
    icon: faExclamationTriangle,
    iconColor: 'text-yellow-500',
    titulo: 'Advertencia',
    mensaje: 'Esta acción requiere su confirmación.',
    textoConfirmar: 'Continuar',
    colorBoton: 'warning'
  },
  info: {
    icon: faInfo,
    iconColor: 'text-blue-500',
    titulo: 'Información',
    mensaje: 'Por favor confirme para continuar.',
    textoConfirmar: 'Entendido',
    colorBoton: 'primary'
  }
};

export const ModalConfirmacion: React.FC<ModalConfirmacionProps> = ({
  isOpen,
  onClose,
  onConfirm,
  tipo,
  titulo,
  mensaje,
  requiereMotivo = false,
  placeholderMotivo = 'Ingrese el motivo (opcional)',
  textoConfirmar,
  textoCancelar = 'Cancelar',
  isLoading = false,
  children
}) => {
  const [motivo, setMotivo] = useState('');
  const [error, setError] = useState('');

  const config = configuracionTipos[tipo];

  const handleConfirm = () => {
    // Validar motivo si es requerido
    if (requiereMotivo && !motivo.trim()) {
      setError('El motivo es requerido');
      return;
    }

    setError('');
    onConfirm(motivo.trim() || undefined);
  };

  const handleClose = () => {
    setMotivo('');
    setError('');
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Overlay */}
      <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" onClick={handleClose} />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-auto">
          {/* Header */}
          <div className="flex items-center p-6 pb-4">
            <div className={`flex-shrink-0 w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-4`}>
              <FontAwesomeIcon icon={config.icon} className={`w-5 h-5 ${config.iconColor}`} />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">
                {titulo || config.titulo}
              </h3>
            </div>
          </div>

          {/* Content */}
          <div className="px-6 pb-4">
            <p className="text-gray-600 mb-4">
              {mensaje || config.mensaje}
            </p>

            {children && (
              <div className="mb-4">
                {children}
              </div>
            )}

            {requiereMotivo && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Motivo {requiereMotivo && <span className="text-red-500">*</span>}
                </label>
                <textarea
                  value={motivo}
                  onChange={(e) => {
                    setMotivo(e.target.value);
                    if (error) setError('');
                  }}
                  placeholder={placeholderMotivo}
                  rows={3}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    error ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {error && (
                  <p className="mt-1 text-sm text-red-600">{error}</p>
                )}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-end space-x-3 px-6 py-4 bg-gray-50 rounded-b-lg">
            <Button
              variant="secondary"
              onClick={handleClose}
              disabled={isLoading}
            >
              {textoCancelar}
            </Button>
            <Button
              variant={config.colorBoton}
              onClick={handleConfirm}
              disabled={isLoading}
              className="min-w-[100px]"
            >
              {isLoading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Procesando...
                </div>
              ) : (
                textoConfirmar || config.textoConfirmar
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Hook para usar el modal de confirmación
export const useModalConfirmacion = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [config, setConfig] = useState<Partial<ModalConfirmacionProps>>({});

  const mostrar = (configuracion: Partial<ModalConfirmacionProps>) => {
    setConfig(configuracion);
    setIsOpen(true);
  };

  const cerrar = () => {
    setIsOpen(false);
    setConfig({});
  };

  const confirmar = (motivo?: string) => {
    if (config.onConfirm) {
      config.onConfirm(motivo);
    }
    cerrar();
  };

  return {
    isOpen,
    mostrar,
    cerrar,
    confirmar,
    ModalConfirmacion: (props: Partial<ModalConfirmacionProps>) => (
      <ModalConfirmacion
        {...config}
        {...props}
        isOpen={isOpen}
        onClose={cerrar}
        onConfirm={confirmar}
      />
    )
  };
};

export default ModalConfirmacion;
