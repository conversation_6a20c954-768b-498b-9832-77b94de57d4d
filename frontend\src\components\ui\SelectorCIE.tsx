import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Input } from './Input';
import { Select } from './Select';
import { Button } from './Button';
import { Modal } from './Modal';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch,
  faExchangeAlt,
  faInfoCircle,
  faExclamationTriangle,
  faCheckCircle,
  faHistory,
  faArrowRight
} from '@fortawesome/free-solid-svg-icons';

// Tipos para la transición CIE-10 a CIE-11
interface CodigoCIE {
  codigo: string;
  descripcion: string;
  version: 'CIE-10' | 'CIE-11';
  categoria?: string;
  subcategoria?: string;
  activo: boolean;
  fecha_vigencia?: string;
  fecha_deprecacion?: string;
}

interface MapeoTransicion {
  codigo_cie10: string;
  codigo_cie11: string;
  tipo_mapeo: 'DIRECTO' | 'APROXIMADO' | 'MULTIPLE' | 'SIN_EQUIVALENCIA';
  confianza: number; // 0-100
  notas?: string;
  requiere_revision: boolean;
}

interface SelectorCIEProps {
  value?: string;
  onChange: (codigo: string, version: 'CIE-10' | 'CIE-11') => void;
  versionPreferida?: 'CIE-10' | 'CIE-11' | 'AMBAS';
  mostrarTransicion?: boolean;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
}

export const SelectorCIE: React.FC<SelectorCIEProps> = ({
  value = '',
  onChange,
  versionPreferida = 'AMBAS',
  mostrarTransicion = true,
  placeholder = 'Buscar código CIE...',
  required = false,
  disabled = false,
  className = ''
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [versionActiva, setVersionActiva] = useState<'CIE-10' | 'CIE-11'>('CIE-11');
  const [mostrarModal, setMostrarModal] = useState(false);
  const [codigoSeleccionado, setCodigoSeleccionado] = useState<CodigoCIE | null>(null);
  const [mostrarMapeo, setMostrarMapeo] = useState(false);

  // Query para buscar códigos CIE
  const { data: resultados, isLoading } = useQuery({
    queryKey: ['buscar-cie', searchTerm, versionActiva],
    queryFn: async () => {
      if (!searchTerm || searchTerm.length < 2) return [];
      
      // Simulación de búsqueda - en producción sería una llamada a la API
      const mockResultados: CodigoCIE[] = [
        {
          codigo: 'E11.9',
          descripcion: 'Diabetes mellitus tipo 2 sin complicaciones',
          version: 'CIE-10',
          categoria: 'Enfermedades endocrinas',
          activo: true,
          fecha_deprecacion: '2025-01-01'
        },
        {
          codigo: '5A11.0',
          descripcion: 'Diabetes mellitus tipo 2 sin complicaciones',
          version: 'CIE-11',
          categoria: 'Enfermedades endocrinas',
          activo: true,
          fecha_vigencia: '2024-01-01'
        },
        {
          codigo: 'I10',
          descripcion: 'Hipertensión esencial',
          version: 'CIE-10',
          categoria: 'Enfermedades del sistema circulatorio',
          activo: true,
          fecha_deprecacion: '2025-01-01'
        },
        {
          codigo: 'BA00',
          descripcion: 'Hipertensión esencial',
          version: 'CIE-11',
          categoria: 'Enfermedades del sistema circulatorio',
          activo: true,
          fecha_vigencia: '2024-01-01'
        }
      ];

      return mockResultados.filter(codigo => 
        (versionActiva === 'CIE-10' || versionActiva === 'CIE-11' ? codigo.version === versionActiva : true) &&
        (codigo.codigo.toLowerCase().includes(searchTerm.toLowerCase()) ||
         codigo.descripcion.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    },
    enabled: searchTerm.length >= 2
  });

  // Query para obtener mapeo de transición
  const { data: mapeoTransicion } = useQuery({
    queryKey: ['mapeo-transicion', codigoSeleccionado?.codigo],
    queryFn: async () => {
      if (!codigoSeleccionado) return null;
      
      // Simulación de mapeo - en producción sería una llamada a la API
      const mockMapeos: MapeoTransicion[] = [
        {
          codigo_cie10: 'E11.9',
          codigo_cie11: '5A11.0',
          tipo_mapeo: 'DIRECTO',
          confianza: 95,
          requiere_revision: false
        },
        {
          codigo_cie10: 'I10',
          codigo_cie11: 'BA00',
          tipo_mapeo: 'DIRECTO',
          confianza: 90,
          requiere_revision: false
        }
      ];

      return mockMapeos.find(mapeo => 
        mapeo.codigo_cie10 === codigoSeleccionado.codigo || 
        mapeo.codigo_cie11 === codigoSeleccionado.codigo
      );
    },
    enabled: !!codigoSeleccionado && mostrarTransicion
  });

  // Manejar selección de código
  const handleSeleccionarCodigo = (codigo: CodigoCIE) => {
    setCodigoSeleccionado(codigo);
    onChange(codigo.codigo, codigo.version);
    setSearchTerm(`${codigo.codigo} - ${codigo.descripcion}`);
    setMostrarModal(false);
  };

  // Obtener estado de transición
  const getEstadoTransicion = (codigo: CodigoCIE) => {
    if (codigo.version === 'CIE-10' && codigo.fecha_deprecacion) {
      const fechaDeprecacion = new Date(codigo.fecha_deprecacion);
      const hoy = new Date();
      const diasRestantes = Math.ceil((fechaDeprecacion.getTime() - hoy.getTime()) / (1000 * 60 * 60 * 24));
      
      if (diasRestantes <= 90) {
        return { tipo: 'URGENTE', mensaje: `Se depreca en ${diasRestantes} días`, color: 'text-red-600' };
      } else if (diasRestantes <= 365) {
        return { tipo: 'ADVERTENCIA', mensaje: `Se depreca en ${Math.ceil(diasRestantes / 30)} meses`, color: 'text-yellow-600' };
      }
    }
    
    if (codigo.version === 'CIE-11' && codigo.fecha_vigencia) {
      return { tipo: 'NUEVO', mensaje: 'Código CIE-11 recomendado', color: 'text-green-600' };
    }
    
    return null;
  };

  // Obtener icono de mapeo
  const getIconoMapeo = (tipoMapeo: string) => {
    switch (tipoMapeo) {
      case 'DIRECTO':
        return { icon: faCheckCircle, color: 'text-green-600' };
      case 'APROXIMADO':
        return { icon: faExclamationTriangle, color: 'text-yellow-600' };
      case 'MULTIPLE':
        return { icon: faExchangeAlt, color: 'text-blue-600' };
      case 'SIN_EQUIVALENCIA':
        return { icon: faExclamationTriangle, color: 'text-red-600' };
      default:
        return { icon: faInfoCircle, color: 'text-gray-600' };
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Selector de versión */}
      {versionPreferida === 'AMBAS' && (
        <div className="flex mb-2 space-x-2">
          <Button
            type="button"
            variant={versionActiva === 'CIE-10' ? 'primary' : 'secondary'}
            size="sm"
            onClick={() => setVersionActiva('CIE-10')}
            className="flex-1"
          >
            CIE-10
            {versionActiva === 'CIE-10' && (
              <FontAwesomeIcon icon={faExclamationTriangle} className="ml-2 text-yellow-500" title="En transición" />
            )}
          </Button>
          <Button
            type="button"
            variant={versionActiva === 'CIE-11' ? 'primary' : 'secondary'}
            size="sm"
            onClick={() => setVersionActiva('CIE-11')}
            className="flex-1"
          >
            CIE-11
            <FontAwesomeIcon icon={faCheckCircle} className="ml-2 text-green-500" title="Recomendado" />
          </Button>
        </div>
      )}

      {/* Campo de búsqueda */}
      <div className="relative">
        <Input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          className="pr-10"
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => setMostrarModal(true)}
          className="absolute right-2 top-1/2 transform -translate-y-1/2"
          disabled={disabled}
        >
          <FontAwesomeIcon icon={faSearch} />
        </Button>
      </div>

      {/* Información de transición */}
      {codigoSeleccionado && mostrarTransicion && (
        <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <FontAwesomeIcon icon={faInfoCircle} className="text-blue-600 mr-2" />
              <span className="text-sm font-medium text-blue-800">
                Código {codigoSeleccionado.version}: {codigoSeleccionado.codigo}
              </span>
            </div>
            {mapeoTransicion && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setMostrarMapeo(true)}
                className="text-blue-600 hover:text-blue-700"
              >
                Ver mapeo
                <FontAwesomeIcon icon={faArrowRight} className="ml-1" />
              </Button>
            )}
          </div>
          
          {/* Estado de transición */}
          {(() => {
            const estado = getEstadoTransicion(codigoSeleccionado);
            return estado && (
              <div className={`mt-1 text-sm ${estado.color}`}>
                <FontAwesomeIcon icon={faHistory} className="mr-1" />
                {estado.mensaje}
              </div>
            );
          })()}
        </div>
      )}

      {/* Modal de búsqueda */}
      <Modal
        isOpen={mostrarModal}
        onClose={() => setMostrarModal(false)}
        title={`Buscar códigos ${versionActiva}`}
        size="lg"
      >
        <div className="space-y-4">
          <div className="relative">
            <Input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Buscar por código o descripción..."
              className="pl-10"
            />
            <FontAwesomeIcon 
              icon={faSearch} 
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" 
            />
          </div>

          {isLoading && (
            <div className="text-center py-4">
              <p className="text-gray-600">Buscando códigos...</p>
            </div>
          )}

          {resultados && resultados.length > 0 && (
            <div className="max-h-96 overflow-y-auto">
              {resultados.map((codigo) => {
                const estado = getEstadoTransicion(codigo);
                return (
                  <div
                    key={`${codigo.codigo}-${codigo.version}`}
                    className="p-3 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => handleSeleccionarCodigo(codigo)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <span className="font-mono font-medium text-blue-600">
                            {codigo.codigo}
                          </span>
                          <span className={`ml-2 px-2 py-1 text-xs rounded ${
                            codigo.version === 'CIE-11' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-yellow-100 text-yellow-800'
                          }`}>
                            {codigo.version}
                          </span>
                        </div>
                        <p className="text-gray-900 mt-1">{codigo.descripcion}</p>
                        {codigo.categoria && (
                          <p className="text-sm text-gray-600 mt-1">{codigo.categoria}</p>
                        )}
                      </div>
                      {estado && (
                        <div className={`text-sm ${estado.color} ml-2`}>
                          <FontAwesomeIcon icon={faHistory} className="mr-1" />
                          {estado.tipo}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {searchTerm.length >= 2 && resultados && resultados.length === 0 && (
            <div className="text-center py-4">
              <p className="text-gray-600">No se encontraron códigos que coincidan con la búsqueda.</p>
            </div>
          )}
        </div>
      </Modal>

      {/* Modal de mapeo de transición */}
      <Modal
        isOpen={mostrarMapeo}
        onClose={() => setMostrarMapeo(false)}
        title="Mapeo de Transición CIE-10 ↔ CIE-11"
      >
        {mapeoTransicion && (
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-medium text-gray-900">Mapeo de Códigos</h3>
                <div className="flex items-center">
                  {(() => {
                    const iconoMapeo = getIconoMapeo(mapeoTransicion.tipo_mapeo);
                    return (
                      <FontAwesomeIcon 
                        icon={iconoMapeo.icon} 
                        className={`mr-2 ${iconoMapeo.color}`} 
                      />
                    );
                  })()}
                  <span className="text-sm font-medium">{mapeoTransicion.tipo_mapeo}</span>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-center">
                <div className="text-center">
                  <div className="bg-yellow-100 p-3 rounded-md">
                    <p className="text-sm text-yellow-800 font-medium">CIE-10</p>
                    <p className="font-mono text-lg">{mapeoTransicion.codigo_cie10}</p>
                  </div>
                </div>
                
                <div className="text-center">
                  <FontAwesomeIcon icon={faArrowRight} className="text-gray-400 text-2xl" />
                </div>
                
                <div className="text-center">
                  <div className="bg-green-100 p-3 rounded-md">
                    <p className="text-sm text-green-800 font-medium">CIE-11</p>
                    <p className="font-mono text-lg">{mapeoTransicion.codigo_cie11}</p>
                  </div>
                </div>
              </div>
              
              <div className="mt-4 flex items-center justify-between">
                <div className="flex items-center">
                  <span className="text-sm text-gray-600">Confianza del mapeo:</span>
                  <div className="ml-2 bg-gray-200 rounded-full h-2 w-20">
                    <div 
                      className={`h-2 rounded-full ${
                        mapeoTransicion.confianza >= 90 ? 'bg-green-500' :
                        mapeoTransicion.confianza >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${mapeoTransicion.confianza}%` }}
                    />
                  </div>
                  <span className="ml-2 text-sm font-medium">{mapeoTransicion.confianza}%</span>
                </div>
                
                {mapeoTransicion.requiere_revision && (
                  <span className="text-sm text-red-600 font-medium">
                    <FontAwesomeIcon icon={faExclamationTriangle} className="mr-1" />
                    Requiere revisión
                  </span>
                )}
              </div>
              
              {mapeoTransicion.notas && (
                <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="text-sm text-blue-800">
                    <FontAwesomeIcon icon={faInfoCircle} className="mr-2" />
                    {mapeoTransicion.notas}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};
