import React from 'react';
// import { <PERSON>, Sun, Monitor } from 'lucide-react';
import { useTheme } from '../../hooks/useTheme';

// Iconos SVG temporales hasta que lucide-react funcione
const SunIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
  </svg>
);

const MoonIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
  </svg>
);

const MonitorIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
  </svg>
);

interface ThemeToggleProps {
  variant?: 'button' | 'switch';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  variant = 'button',
  size = 'md',
  showLabel = false,
  className = ''
}) => {
  const { theme, toggleTheme, isDark } = useTheme();

  const sizeClasses = {
    sm: 'h-8 w-8 text-sm',
    md: 'h-10 w-10 text-base',
    lg: 'h-12 w-12 text-lg'
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  if (variant === 'switch') {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        {showLabel && (
          <span className="text-sm font-medium text-secondary">
            {isDark ? 'Modo Oscuro' : 'Modo Claro'}
          </span>
        )}
        <button
          onClick={toggleTheme}
          className={`
            relative inline-flex ${sizeClasses[size]} items-center justify-center
            rounded-full transition-all duration-300 ease-in-out
            focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2
            ${isDark 
              ? 'bg-primary text-white' 
              : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
            }
          `}
          aria-label={`Cambiar a modo ${isDark ? 'claro' : 'oscuro'}`}
        >
          <div className="relative">
            <SunIcon
              className={`
                ${iconSizes[size]} absolute transition-all duration-300
                ${isDark ? 'rotate-90 scale-0 opacity-0' : 'rotate-0 scale-100 opacity-100'}
              `}
            />
            <MoonIcon
              className={`
                ${iconSizes[size]} transition-all duration-300
                ${isDark ? 'rotate-0 scale-100 opacity-100' : '-rotate-90 scale-0 opacity-0'}
              `}
            />
          </div>
        </button>
      </div>
    );
  }



  // Variant: button (default)
  return (
    <button
      onClick={toggleTheme}
      className={`
        relative ${sizeClasses[size]} rounded-lg transition-all duration-300
        bg-glass border border-glass-border backdrop-blur-glass
        hover:bg-glass/80 focus:outline-none focus:ring-2 focus:ring-primary
        flex items-center justify-center group
        ${className}
      `}
      aria-label={`Cambiar a modo ${isDark ? 'claro' : 'oscuro'}`}
      title={`Cambiar a modo ${isDark ? 'claro' : 'oscuro'}`}
    >
      <div className="relative">
        <SunIcon
          className={`
            ${iconSizes[size]} absolute transition-all duration-300 ease-in-out
            ${isDark
              ? 'rotate-90 scale-0 opacity-0'
              : 'rotate-0 scale-100 opacity-100'
            }
          `}
        />
        <MoonIcon
          className={`
            ${iconSizes[size]} transition-all duration-300 ease-in-out
            ${isDark
              ? 'rotate-0 scale-100 opacity-100'
              : '-rotate-90 scale-0 opacity-0'
            }
          `}
        />
      </div>
      
      {showLabel && (
        <span className="ml-2 text-sm font-medium">
          {isDark ? 'Oscuro' : 'Claro'}
        </span>
      )}
      
      {/* Tooltip */}
      <div className="
        absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2
        px-2 py-1 text-xs bg-card border border-color rounded
        opacity-0 group-hover:opacity-100 transition-opacity duration-200
        pointer-events-none whitespace-nowrap z-50
      ">
        {isDark ? 'Cambiar a modo claro' : 'Cambiar a modo oscuro'}
      </div>
    </button>
  );
};



export default ThemeToggle;
