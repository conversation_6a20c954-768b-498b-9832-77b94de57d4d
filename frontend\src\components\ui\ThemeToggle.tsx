import React from 'react';
import { <PERSON>, <PERSON>, Monitor } from 'lucide-react';
import { useTheme } from '../../hooks/useTheme';

interface ThemeToggleProps {
  variant?: 'button' | 'dropdown' | 'switch';
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  variant = 'button',
  size = 'md',
  showLabel = false,
  className = ''
}) => {
  const { theme, toggleTheme, isDark } = useTheme();

  const sizeClasses = {
    sm: 'h-8 w-8 text-sm',
    md: 'h-10 w-10 text-base',
    lg: 'h-12 w-12 text-lg'
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  if (variant === 'switch') {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        {showLabel && (
          <span className="text-sm font-medium text-secondary">
            {isDark ? 'Modo Oscuro' : 'Modo Claro'}
          </span>
        )}
        <button
          onClick={toggleTheme}
          className={`
            relative inline-flex ${sizeClasses[size]} items-center justify-center
            rounded-full transition-all duration-300 ease-in-out
            focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2
            ${isDark 
              ? 'bg-primary text-white' 
              : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
            }
          `}
          aria-label={`Cambiar a modo ${isDark ? 'claro' : 'oscuro'}`}
        >
          <div className="relative">
            <Sun 
              className={`
                ${iconSizes[size]} absolute transition-all duration-300
                ${isDark ? 'rotate-90 scale-0 opacity-0' : 'rotate-0 scale-100 opacity-100'}
              `} 
            />
            <Moon 
              className={`
                ${iconSizes[size]} transition-all duration-300
                ${isDark ? 'rotate-0 scale-100 opacity-100' : '-rotate-90 scale-0 opacity-0'}
              `} 
            />
          </div>
        </button>
      </div>
    );
  }

  if (variant === 'dropdown') {
    return (
      <div className={`relative ${className}`}>
        <select
          value={theme}
          onChange={(e) => toggleTheme()}
          className="
            appearance-none bg-card border border-color rounded-lg px-3 py-2 pr-8
            text-primary focus:outline-none focus:ring-2 focus:ring-primary
            transition-all duration-200
          "
        >
          <option value="light">🌞 Claro</option>
          <option value="dark">🌙 Oscuro</option>
        </select>
        <Monitor className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted pointer-events-none" />
      </div>
    );
  }

  // Variant: button (default)
  return (
    <button
      onClick={toggleTheme}
      className={`
        relative ${sizeClasses[size]} rounded-lg transition-all duration-300
        bg-glass border border-glass-border backdrop-blur-glass
        hover:bg-glass/80 focus:outline-none focus:ring-2 focus:ring-primary
        flex items-center justify-center group
        ${className}
      `}
      aria-label={`Cambiar a modo ${isDark ? 'claro' : 'oscuro'}`}
      title={`Cambiar a modo ${isDark ? 'claro' : 'oscuro'}`}
    >
      <div className="relative">
        <Sun 
          className={`
            ${iconSizes[size]} absolute transition-all duration-300 ease-in-out
            ${isDark 
              ? 'rotate-90 scale-0 opacity-0' 
              : 'rotate-0 scale-100 opacity-100'
            }
          `} 
        />
        <Moon 
          className={`
            ${iconSizes[size]} transition-all duration-300 ease-in-out
            ${isDark 
              ? 'rotate-0 scale-100 opacity-100' 
              : '-rotate-90 scale-0 opacity-0'
            }
          `} 
        />
      </div>
      
      {showLabel && (
        <span className="ml-2 text-sm font-medium">
          {isDark ? 'Oscuro' : 'Claro'}
        </span>
      )}
      
      {/* Tooltip */}
      <div className="
        absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2
        px-2 py-1 text-xs bg-card border border-color rounded
        opacity-0 group-hover:opacity-100 transition-opacity duration-200
        pointer-events-none whitespace-nowrap z-50
      ">
        {isDark ? 'Cambiar a modo claro' : 'Cambiar a modo oscuro'}
      </div>
    </button>
  );
};

// Componente de configuración avanzada de tema
export const ThemeSettings: React.FC = () => {
  const { 
    theme, 
    setTheme, 
    highContrast, 
    reducedMotion, 
    toggleHighContrast, 
    toggleReducedMotion 
  } = useTheme();

  return (
    <div className="space-y-4 p-4 bg-card rounded-lg border border-color">
      <h3 className="text-lg font-semibold text-primary">Configuración de Tema</h3>
      
      {/* Selector de tema */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-secondary">Tema</label>
        <div className="flex space-x-2">
          <button
            onClick={() => setTheme('light')}
            className={`
              flex items-center space-x-2 px-3 py-2 rounded-lg border transition-all
              ${theme === 'light' 
                ? 'bg-primary text-white border-primary' 
                : 'bg-card border-color hover:border-primary'
              }
            `}
          >
            <Sun className="h-4 w-4" />
            <span>Claro</span>
          </button>
          <button
            onClick={() => setTheme('dark')}
            className={`
              flex items-center space-x-2 px-3 py-2 rounded-lg border transition-all
              ${theme === 'dark' 
                ? 'bg-primary text-white border-primary' 
                : 'bg-card border-color hover:border-primary'
              }
            `}
          >
            <Moon className="h-4 w-4" />
            <span>Oscuro</span>
          </button>
        </div>
      </div>

      {/* Configuraciones de accesibilidad */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-secondary">Accesibilidad</h4>
        
        <label className="flex items-center space-x-3 cursor-pointer">
          <input
            type="checkbox"
            checked={highContrast}
            onChange={toggleHighContrast}
            className="rounded border-color focus:ring-primary"
          />
          <span className="text-sm">Alto contraste</span>
        </label>
        
        <label className="flex items-center space-x-3 cursor-pointer">
          <input
            type="checkbox"
            checked={reducedMotion}
            onChange={toggleReducedMotion}
            className="rounded border-color focus:ring-primary"
          />
          <span className="text-sm">Reducir animaciones</span>
        </label>
      </div>
    </div>
  );
};

export default ThemeToggle;
