# 🚨 PLAN ETAPA 2C: MÓDULO DE URGENCIAS CON SISTEMA DE TRIAJE

## 📋 **ANÁLISIS DEL ESTADO ACTUAL**

### **✅ Componentes Existentes:**
- `Urgencias.tsx` - Lista principal ✅
- `UrgenciaDetalle.tsx` - Vista detallada ✅
- `UrgenciaEditar.tsx` - Formulario de edición ✅
- `UrgenciaNueva.tsx` - Wrapper para nueva urgencia ✅

### **⚠️ Componentes Pendientes (TO DO):**
- `TriajeUrgencia.tsx` - Sistema de triaje ❌
- `AtenderUrgencia.tsx` - Atención médica ❌
- `ObservacionUrgencia.tsx` - Seguimiento ❌

### **✅ Servicios Implementados:**
- `urgenciasService.ts` - CRUD completo ✅
- Métodos de triaje y alta ✅
- Estadísticas y filtros ✅

### **✅ Tipos de Datos:**
- `EstadoUrgencia` - 9 estados definidos ✅
- `NivelTriage` - 5 niveles (Manchester) ✅
- Interfaces completas para UI ✅

## 🎯 **OBJETIVOS DE LA ETAPA 2C**

### **1. 🏥 Sistema de Triaje Profesional**
- **Clasificación Manchester** (5 niveles)
- **Evaluación de signos vitales**
- **Asignación automática de prioridad**
- **Tiempos de espera calculados**

### **2. 📊 Dashboard de Urgencias**
- **Estadísticas en tiempo real**
- **Indicadores de rendimiento**
- **Alertas de tiempo de espera**
- **Distribución por niveles de triaje**

### **3. 🔄 Flujo de Atención Completo**
- **Ingreso → Triaje → Atención → Observación → Alta**
- **Seguimiento de tiempos**
- **Cambios de estado automáticos**
- **Notificaciones de alertas**

### **4. 📱 Interfaz Optimizada**
- **Vista de tablero de urgencias**
- **Códigos de colores por prioridad**
- **Filtros avanzados**
- **Acciones rápidas**

## 🛠️ **PLAN DE IMPLEMENTACIÓN**

### **FASE 1: Sistema de Triaje (Prioridad Alta)**

#### **1.1 Componente TriajeUrgencia.tsx**
```tsx
// Funcionalidades principales:
- Formulario de evaluación inicial
- Cálculo automático de nivel de triaje
- Registro de signos vitales
- Asignación de profesional
- Estimación de tiempo de espera
```

#### **1.2 Algoritmo de Triaje Manchester**
```typescript
// Criterios de clasificación:
- NIVEL 1 (Rojo): Resucitación - Inmediato
- NIVEL 2 (Naranja): Emergencia - <15 min
- NIVEL 3 (Amarillo): Urgencia - <30 min
- NIVEL 4 (Verde): Urgencia menor - <60 min
- NIVEL 5 (Azul): No urgente - <120 min
```

#### **1.3 Evaluación de Signos Vitales**
```typescript
// Parámetros críticos:
- Presión arterial
- Frecuencia cardíaca
- Frecuencia respiratoria
- Saturación de oxígeno
- Temperatura
- Escala de dolor (1-10)
- Escala de Glasgow
```

### **FASE 2: Dashboard de Urgencias (Prioridad Alta)**

#### **2.1 Componente DashboardUrgencias.tsx**
```tsx
// Widgets principales:
- Resumen de pacientes por estado
- Distribución por nivel de triaje
- Tiempos promedio de espera
- Alertas de tiempo excedido
- Gráficos de tendencias
```

#### **2.2 Indicadores Clave (KPIs)**
```typescript
// Métricas importantes:
- Tiempo promedio de triaje
- Tiempo promedio de atención
- Pacientes en espera por nivel
- Porcentaje de cumplimiento de tiempos
- Ocupación de camas de observación
```

### **FASE 3: Flujo de Atención (Prioridad Media)**

#### **3.1 Componente AtenderUrgencia.tsx**
```tsx
// Funcionalidades:
- Historia clínica rápida
- Examen físico
- Diagnósticos CIE-11
- Órdenes médicas
- Medicamentos administrados
- Procedimientos realizados
```

#### **3.2 Componente ObservacionUrgencia.tsx**
```tsx
// Seguimiento:
- Evoluciones médicas
- Control de signos vitales
- Medicamentos administrados
- Cambios de estado
- Preparación para alta/hospitalización
```

### **FASE 4: Mejoras de UI/UX (Prioridad Media)**

#### **4.1 Tablero Visual**
```tsx
// Elementos visuales:
- Códigos de colores por prioridad
- Iconos intuitivos por estado
- Cronómetros de tiempo de espera
- Alertas visuales y sonoras
- Drag & drop para cambios de estado
```

#### **4.2 Filtros y Búsquedas**
```tsx
// Filtros avanzados:
- Por nivel de triaje
- Por estado de atención
- Por tiempo de espera
- Por profesional asignado
- Por rango de fechas
```

## 📊 **ESTRUCTURA DE DATOS MEJORADA**

### **Campos Adicionales para Triaje:**
```typescript
interface TriageData {
  motivo_consulta: string;
  sintomas_principales: string[];
  tiempo_inicio_sintomas: string;
  dolor_escala: number; // 1-10
  glasgow_escala: number; // 3-15
  criterios_manchester: {
    via_aerea: boolean;
    respiracion: boolean;
    circulacion: boolean;
    discapacidad: boolean;
    exposicion: boolean;
  };
  factores_riesgo: string[];
  alergias_conocidas: string[];
  medicamentos_actuales: string[];
}
```

### **Tiempos de Seguimiento:**
```typescript
interface TiemposUrgencia {
  hora_llegada: string;
  hora_inicio_triaje: string;
  hora_fin_triaje: string;
  hora_asignacion_medico: string;
  hora_inicio_atencion: string;
  hora_fin_atencion: string;
  tiempo_total_estancia: number; // minutos
  cumple_tiempo_objetivo: boolean;
}
```

## 🎨 **DISEÑO Y COLORES**

### **Códigos de Color por Nivel de Triaje:**
```css
.nivel-1 { background: #dc2626; color: white; } /* Rojo - Crítico */
.nivel-2 { background: #ea580c; color: white; } /* Naranja - Emergencia */
.nivel-3 { background: #ca8a04; color: white; } /* Amarillo - Urgencia */
.nivel-4 { background: #16a34a; color: white; } /* Verde - Menor */
.nivel-5 { background: #2563eb; color: white; } /* Azul - No urgente */
```

### **Estados de Atención:**
```css
.espera-triage { background: #f3f4f6; color: #374151; }
.en-triage { background: #fef3c7; color: #92400e; }
.espera-atencion { background: #dbeafe; color: #1e40af; }
.en-atencion { background: #dcfce7; color: #166534; }
.en-observacion { background: #e0e7ff; color: #3730a3; }
```

## 📈 **MÉTRICAS Y ALERTAS**

### **Alertas Automáticas:**
```typescript
interface AlertaUrgencia {
  tipo: 'TIEMPO_EXCEDIDO' | 'DETERIORO_CLINICO' | 'ALTA_PRIORIDAD';
  paciente_id: string;
  mensaje: string;
  nivel_urgencia: 'BAJA' | 'MEDIA' | 'ALTA' | 'CRITICA';
  timestamp: string;
  requiere_accion: boolean;
}
```

### **Indicadores de Rendimiento:**
```typescript
interface KPIsUrgencias {
  pacientes_total: number;
  tiempo_promedio_triaje: number;
  tiempo_promedio_atencion: number;
  porcentaje_cumplimiento_tiempos: number;
  distribucion_por_nivel: Record<string, number>;
  alertas_activas: number;
  camas_ocupadas: number;
  camas_disponibles: number;
}
```

## 🔄 **FLUJO DE TRABAJO OPTIMIZADO**

### **1. Ingreso del Paciente:**
```
Llegada → Registro → Triaje Inicial → Asignación de Prioridad
```

### **2. Proceso de Triaje:**
```
Evaluación → Signos Vitales → Clasificación → Asignación de Tiempo
```

### **3. Atención Médica:**
```
Llamada → Evaluación → Diagnóstico → Tratamiento → Decisión
```

### **4. Seguimiento:**
```
Observación → Evolución → Reevaluación → Alta/Hospitalización
```

## 🎯 **CRONOGRAMA DE IMPLEMENTACIÓN**

### **Semana 1: Sistema de Triaje**
- Día 1-2: Componente TriajeUrgencia
- Día 3-4: Algoritmo de clasificación
- Día 5: Integración y pruebas

### **Semana 2: Dashboard y Flujo**
- Día 1-2: Dashboard de urgencias
- Día 3-4: Componentes de atención
- Día 5: Optimización de UI

### **Semana 3: Refinamiento**
- Día 1-2: Alertas y notificaciones
- Día 3-4: Métricas y reportes
- Día 5: Testing y documentación

## 🎉 **RESULTADOS ESPERADOS**

### **✅ Sistema Profesional:**
- **Triaje estandarizado** según protocolo Manchester
- **Flujo de atención** optimizado y eficiente
- **Seguimiento completo** de tiempos y estados
- **Alertas inteligentes** para casos críticos

### **✅ Experiencia de Usuario:**
- **Interfaz intuitiva** con códigos de colores
- **Acciones rápidas** para cambios de estado
- **Información clara** de prioridades y tiempos
- **Dashboard informativo** para gestión

### **✅ Cumplimiento Normativo:**
- **Protocolos médicos** implementados
- **Trazabilidad completa** de la atención
- **Indicadores de calidad** monitoreados
- **Documentación médica** completa

---

**🚀 ¡Listo para implementar un sistema de urgencias de nivel hospitalario profesional!**
