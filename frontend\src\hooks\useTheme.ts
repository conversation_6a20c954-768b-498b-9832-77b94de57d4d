import { useEffect } from 'react';
import { useSettingsStore, Theme } from '../store/useSettingsStore';

export const useTheme = () => {
  const { 
    theme, 
    toggleTheme, 
    setTheme,
    highContrast,
    reducedMotion,
    toggleHighContrast,
    toggleReducedMotion
  } = useSettingsStore();

  // Escuchar cambios en la preferencia del sistema
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleChange = (e: MediaQueryListEvent) => {
      // Solo cambiar automáticamente si el usuario no ha establecido una preferencia manual
      const hasManualPreference = localStorage.getItem('hipocrates-ui-settings');
      if (!hasManualPreference) {
        setTheme(e.matches ? 'dark' : 'light');
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [setTheme]);

  // Aplicar configuraciones de accesibilidad al DOM
  useEffect(() => {
    const root = document.documentElement;
    
    // Aplicar clase de alto contraste
    if (highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // Aplicar clase de movimiento reducido
    if (reducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }
  }, [highContrast, reducedMotion]);

  // Función para obtener colores del tema actual
  const getThemeColors = () => {
    const isDark = theme === 'dark';
    
    return {
      // Colores principales
      primary: isDark ? '#60A5FA' : '#3B82F6',
      primaryDark: isDark ? '#3B82F6' : '#1E40AF',
      secondary: isDark ? '#34D399' : '#10B981',
      accent: isDark ? '#FBBF24' : '#F59E0B',
      
      // Fondos
      bgPrimary: isDark ? '#0F172A' : '#FFFFFF',
      bgSecondary: isDark ? '#1E293B' : '#F8FAFC',
      bgCard: isDark ? '#334155' : '#FFFFFF',
      
      // Textos
      textPrimary: isDark ? '#F8FAFC' : '#1F2937',
      textSecondary: isDark ? '#CBD5E1' : '#6B7280',
      textMuted: isDark ? '#94A3B8' : '#9CA3AF',
      
      // Bordes
      border: isDark ? '#475569' : '#E5E7EB',
      borderLight: isDark ? '#334155' : '#F3F4F6',
      
      // Glassmorphism
      glassBg: isDark ? 'rgba(15, 23, 42, 0.25)' : 'rgba(255, 255, 255, 0.25)',
      glassBorder: 'rgba(255, 255, 255, 0.18)',
      glassBackdrop: 'blur(10px)',
    };
  };

  // Función para obtener estilos CSS personalizados
  const getThemeStyles = () => {
    const colors = getThemeColors();
    
    return {
      '--primary': colors.primary,
      '--primary-dark': colors.primaryDark,
      '--secondary': colors.secondary,
      '--accent': colors.accent,
      '--bg-primary': colors.bgPrimary,
      '--bg-secondary': colors.bgSecondary,
      '--bg-card': colors.bgCard,
      '--text-primary': colors.textPrimary,
      '--text-secondary': colors.textSecondary,
      '--text-muted': colors.textMuted,
      '--border': colors.border,
      '--border-light': colors.borderLight,
      '--glass-bg': colors.glassBg,
      '--glass-border': colors.glassBorder,
      '--glass-backdrop': colors.glassBackdrop,
    } as React.CSSProperties;
  };

  return {
    // Estado del tema
    theme,
    isDark: theme === 'dark',
    isLight: theme === 'light',
    
    // Acciones del tema
    toggleTheme,
    setTheme,
    
    // Accesibilidad
    highContrast,
    reducedMotion,
    toggleHighContrast,
    toggleReducedMotion,
    
    // Utilidades
    getThemeColors,
    getThemeStyles,
  };
};

// Hook para detectar preferencias del sistema
export const useSystemTheme = () => {
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    return mediaQuery.matches ? 'dark' : 'light';
  }, []);
};

// Hook para aplicar estilos de tema dinámicamente
export const useThemeStyles = () => {
  const { getThemeStyles } = useTheme();
  
  useEffect(() => {
    const styles = getThemeStyles();
    const root = document.documentElement;
    
    Object.entries(styles).forEach(([property, value]) => {
      root.style.setProperty(property, value as string);
    });
  }, [getThemeStyles]);
};
