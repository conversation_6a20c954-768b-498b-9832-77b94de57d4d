import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { consultasService } from '../../services/consultasService';
import { pacientesService } from '../../services/pacientesService';
import { useAuth } from '../../services/authService';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { SelectorCIE } from '../../components/ui/SelectorCIE';
import { Layout } from '../../components/layout/Layout';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExclamationTriangle, faInfoCircle } from '@fortawesome/free-solid-svg-icons';

const consultaSchema = z.object({
  especialidad: z.string().min(1, 'La especialidad es obligatoria'),
  fecha: z.string().min(1, 'La fecha es obligatoria'),
  hora_inicio: z.string().min(1, 'La hora de inicio es obligatoria'),
  hora_fin: z.string().min(1, 'La hora de fin es obligatoria'),
  motivo: z.string().min(1, 'El motivo de la consulta es obligatorio'),
  tipo_consulta: z.enum(['PRIMERA_VEZ', 'CONTROL', 'PROCEDIMIENTO']),
  diagnostico_principal: z.string().optional(),
  version_cie: z.enum(['CIE-10', 'CIE-11']).optional(),
  notas_medicas: z.string().optional(),
});

type ConsultaForm = z.infer<typeof consultaSchema>;

export const ConsultaEditar = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const hospitalId = user?.hospital_id || 1;

  // Estados para manejo de diagnóstico CIE
  const [diagnosticoCIE, setDiagnosticoCIE] = useState<string>('');
  const [versionCIE, setVersionCIE] = useState<'CIE-10' | 'CIE-11'>('CIE-11');
  
  // Obtener la consulta
  const { data: consulta, isLoading: isLoadingConsulta } = useQuery({
    queryKey: ['consulta', id, hospitalId],
    queryFn: () => consultasService.getConsultaById(id!, hospitalId),
    enabled: !!id,
  });
  
  // Obtener el paciente
  const { data: paciente, isLoading: isLoadingPaciente } = useQuery({
    queryKey: ['paciente', consulta?.paciente_id, hospitalId],
    queryFn: () => pacientesService.getPacienteById(consulta!.paciente_id, hospitalId),
    enabled: !!consulta?.paciente_id,
  });
  
  // Obtener todas las especialidades
  const { data: especialidades, isLoading: isLoadingEspecialidades } = useQuery({
    queryKey: ['especialidades', hospitalId],
    queryFn: () => consultasService.getEspecialidades(hospitalId),
  });
  
  // Formulario
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ConsultaForm>({
    resolver: zodResolver(consultaSchema),
  });
  
  // Inicializar el formulario cuando se carga la consulta
  useEffect(() => {
    if (consulta) {
      reset({
        especialidad: consulta.especialidad,
        fecha: consulta.fecha,
        hora_inicio: consulta.hora_inicio,
        hora_fin: consulta.hora_fin,
        motivo: consulta.motivo,
        tipo_consulta: consulta.tipo_consulta,
        diagnostico_principal: consulta.diagnostico_principal || '',
        version_cie: consulta.version_cie || 'CIE-11',
        notas_medicas: consulta.notas_medicas || '',
      });

      // Inicializar estados de diagnóstico CIE
      if (consulta.diagnostico_principal) {
        setDiagnosticoCIE(consulta.diagnostico_principal);
      }
      if (consulta.version_cie) {
        setVersionCIE(consulta.version_cie);
      }
    }
  }, [consulta, reset]);
  
  // Mutación para actualizar la consulta
  const updateConsultaMutation = useMutation({
    mutationFn: (data: ConsultaForm) =>
      consultasService.updateConsulta(
        id!,
        {
          ...data,
          diagnostico_principal: diagnosticoCIE,
          version_cie: versionCIE,
          medico_id: user?.id || '',
        },
        hospitalId
      ),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['consulta', id, hospitalId] });
      navigate(`/consultas/${data.id}`);
    },
  });
  
  // Manejar el envío del formulario
  const onSubmit = (data: ConsultaForm) => {
    updateConsultaMutation.mutate(data);
  };
  
  const isLoading = isLoadingConsulta || isLoadingPaciente || isLoadingEspecialidades;
  
  if (isLoading) return <div className="text-center p-6 text-white">Cargando...</div>;
  
  if (!consulta) return <div className="text-center p-6 text-white">Consulta no encontrada</div>;
  
  // Lista de especialidades predefinidas si no hay datos del servidor
  const especialidadesLista = especialidades?.length ? especialidades : [
    'MEDICINA GENERAL',
    'PEDIATRÍA',
    'GINECOLOGÍA',
    'OBSTETRICIA',
    'CARDIOLOGÍA',
    'DERMATOLOGÍA',
    'ENDOCRINOLOGÍA',
    'GASTROENTEROLOGÍA',
    'NEUROLOGÍA',
    'OFTALMOLOGÍA',
    'ORTOPEDIA',
    'OTORRINOLARINGOLOGÍA',
    'PSIQUIATRÍA',
    'UROLOGÍA',
  ];
  
  return (
    <div className="glassmorphism p-6 rounded-lg">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">Editar Consulta</h1>
          <Button variant="secondary" onClick={() => navigate(`/consultas/${id}`)}>
            Cancelar
          </Button>
        </div>
        
        {/* Información del Paciente */}
        <div className="bg-gray-800 bg-opacity-50 p-4 rounded-lg mb-6">
          <h2 className="text-xl font-semibold text-white mb-2">Paciente</h2>
          {paciente ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <p className="text-gray-300">
                <span className="font-medium">Nombre:</span> {paciente.nombre} {paciente.apellido}
              </p>
              <p className="text-gray-300">
                <span className="font-medium">Documento:</span> {paciente.tipo_documento}{' '}
                {paciente.numero_documento}
              </p>
              <p className="text-gray-300">
                <span className="font-medium">Sexo:</span>{' '}
                {paciente.sexo === 'M' ? 'Masculino' : 'Femenino'}
              </p>
            </div>
          ) : (
            <p className="text-gray-300">Paciente no encontrado</p>
          )}
        </div>
        
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-white mb-1">
                Especialidad *
              </label>
              <select
                {...register('especialidad')}
                className="w-full p-2 rounded bg-transparent border border-gray-300 text-white"
              >
                <option value="" className="bg-gray-800">Seleccione una especialidad</option>
                {especialidadesLista.map((especialidad) => (
                  <option key={especialidad} value={especialidad} className="bg-gray-800">
                    {especialidad}
                  </option>
                ))}
              </select>
              {errors.especialidad && (
                <p className="mt-1 text-sm text-red-500">{errors.especialidad.message}</p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-white mb-1">
                Tipo de Consulta *
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center">
                  <input
                    {...register('tipo_consulta')}
                    type="radio"
                    value="PRIMERA_VEZ"
                    id="tipo-primera-vez"
                    className="mr-2"
                  />
                  <label htmlFor="tipo-primera-vez" className="text-white">
                    Primera Vez
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    {...register('tipo_consulta')}
                    type="radio"
                    value="CONTROL"
                    id="tipo-control"
                    className="mr-2"
                  />
                  <label htmlFor="tipo-control" className="text-white">
                    Control
                  </label>
                </div>
                
                <div className="flex items-center">
                  <input
                    {...register('tipo_consulta')}
                    type="radio"
                    value="PROCEDIMIENTO"
                    id="tipo-procedimiento"
                    className="mr-2"
                  />
                  <label htmlFor="tipo-procedimiento" className="text-white">
                    Procedimiento
                  </label>
                </div>
              </div>
              {errors.tipo_consulta && (
                <p className="mt-1 text-sm text-red-500">{errors.tipo_consulta.message}</p>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-white mb-1">
                Fecha *
              </label>
              <Input
                {...register('fecha')}
                type="date"
                error={errors.fecha?.message}
                className="text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-white mb-1">
                Hora de Inicio *
              </label>
              <Input
                {...register('hora_inicio')}
                type="time"
                error={errors.hora_inicio?.message}
                className="text-white"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-white mb-1">
                Hora de Fin *
              </label>
              <Input
                {...register('hora_fin')}
                type="time"
                error={errors.hora_fin?.message}
                className="text-white"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Motivo de la Consulta *
            </label>
            <textarea
              {...register('motivo')}
              rows={3}
              className="w-full p-2 rounded bg-transparent border border-gray-300 text-white"
              placeholder="Describa el motivo de la consulta..."
            ></textarea>
            {errors.motivo && (
              <p className="mt-1 text-sm text-red-500">{errors.motivo.message}</p>
            )}
          </div>

          {/* Sección de Diagnóstico CIE con Transición */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <FontAwesomeIcon icon={faInfoCircle} className="text-blue-600 mr-2" />
              <h3 className="text-lg font-medium text-blue-900">Diagnóstico Principal</h3>
              <div className="ml-auto flex items-center text-sm text-blue-700">
                <FontAwesomeIcon icon={faExclamationTriangle} className="mr-1" />
                Transición CIE-10 → CIE-11
              </div>
            </div>

            <div className="mb-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <div className="flex items-start">
                <FontAwesomeIcon icon={faExclamationTriangle} className="text-yellow-600 mr-2 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium">Importante: Transición a CIE-11</p>
                  <p>El sistema está en proceso de transición de CIE-10 a CIE-11. Se recomienda usar códigos CIE-11 para nuevos diagnósticos. Los códigos CIE-10 serán deprecados gradualmente.</p>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Código de Diagnóstico (CIE-10/CIE-11)
              </label>
              <SelectorCIE
                value={diagnosticoCIE}
                onChange={(codigo, version) => {
                  setDiagnosticoCIE(codigo);
                  setVersionCIE(version);
                }}
                versionPreferida="AMBAS"
                mostrarTransicion={true}
                placeholder="Buscar código de diagnóstico..."
                className="w-full"
              />

              {diagnosticoCIE && (
                <div className="mt-2 p-2 bg-gray-50 border border-gray-200 rounded-md">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Código seleccionado:</span>
                    <div className="flex items-center">
                      <span className="font-mono font-medium text-blue-600 mr-2">{diagnosticoCIE}</span>
                      <span className={`px-2 py-1 text-xs rounded ${
                        versionCIE === 'CIE-11'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {versionCIE}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Notas Médicas (Opcional)
            </label>
            <textarea
              {...register('notas_medicas')}
              rows={4}
              className="w-full p-2 rounded bg-transparent border border-gray-300 text-white"
              placeholder="Notas adicionales para el médico..."
            ></textarea>
          </div>
          
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => navigate(`/consultas/${id}`)}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={updateConsultaMutation.isPending}
            >
              {updateConsultaMutation.isPending ? 'Guardando...' : 'Actualizar Consulta'}
            </Button>
          </div>
        </form>
      </div>
  );
};

