import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { consultasService, ConsultaFiltros } from '../../services/consultasService';
import { pacientesService } from '../../services/pacientesService';
import { useAuth } from '../../services/authService';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';


import { Consulta, Paciente } from '../../types';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

export const Consultas = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const hospitalId = user?.hospital_id || 1;

  // Estados para filtros
  const [filtros, setFiltros] = useState<ConsultaFiltros>({});
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [fechaSeleccionada, setFechaSeleccionada] = useState<string>('');

  // Obtener todas las consultas
  const { data: consultas, isLoading: isLoadingConsultas, refetch } = useQuery({
    queryKey: ['consultas', hospitalId, filtros],
    queryFn: () => consultasService.getConsultas(hospitalId, filtros),
  });

  // Obtener todos los pacientes para mostrar nombres
  const { data: pacientes, isLoading: isLoadingPacientes } = useQuery({
    queryKey: ['pacientes', hospitalId],
    queryFn: () => pacientesService.getPacientes(hospitalId),
  });

  // Obtener todas las especialidades para el filtro
  const { data: especialidades, isLoading: isLoadingEspecialidades } = useQuery({
    queryKey: ['especialidades', hospitalId],
    queryFn: () => consultasService.getEspecialidades(hospitalId),
  });

  // Efecto para aplicar filtro de fecha
  useEffect(() => {
    if (fechaSeleccionada) {
      setFiltros(prev => ({ ...prev, fecha: fechaSeleccionada }));
    } else {
      const { fecha, ...restFiltros } = filtros;
      setFiltros(restFiltros);
    }
  }, [fechaSeleccionada]);

  // Filtrar consultas por término de búsqueda
  const filteredConsultas = Array.isArray(consultas)
    ? consultas.filter((consulta) => {
        const paciente = pacientes?.find((p) => p.id === consulta.paciente_id);

        if (!searchTerm) return true;

        return (
          paciente?.nombre?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          consulta.especialidad?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          consulta.motivo?.toLowerCase().includes(searchTerm.toLowerCase())
        );
      })
    : [];

  // Formatear fecha
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP', { locale: es });
    } catch (error) {
      return dateString;
    }
  };

  // Formatear hora
  const formatTime = (timeString: string) => {
    return timeString;
  };

  // Obtener nombre del paciente
  const getPacienteName = (pacienteId: string) => {
    const paciente = pacientes?.find((p) => p.id === pacienteId);
    return paciente ? paciente.nombre : 'Paciente no encontrado';
  };

  // Obtener color según estado
  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'PROGRAMADA':
        return 'bg-blue-500 bg-opacity-20 text-blue-300';
      case 'EN_CURSO':
        return 'bg-green-500 bg-opacity-20 text-green-300';
      case 'COMPLETADA':
        return 'bg-purple-500 bg-opacity-20 text-purple-300';
      case 'CANCELADA':
        return 'bg-red-500 bg-opacity-20 text-red-300';
      case 'NO_ASISTIO':
        return 'bg-yellow-500 bg-opacity-20 text-yellow-300';
      default:
        return 'bg-gray-500 bg-opacity-20 text-gray-300';
    }
  };

  // Navegar a la vista de detalle
  const handleViewConsulta = (id: string) => {
    navigate(`/consultas/${id}`);
  };

  // Navegar a la vista de creación
  const handleCreateConsulta = () => {
    navigate('/consultas/nuevo');
  };

  // Limpiar filtros
  const handleClearFilters = () => {
    setFiltros({});
    setFechaSeleccionada('');
    setSearchTerm('');
  };

  const isLoading = isLoadingConsultas || isLoadingPacientes || isLoadingEspecialidades;

  if (isLoading) return <div className="text-center p-6 text-white">Cargando...</div>;

  return (
    <div>
      <div className="glassmorphism p-6 rounded-lg">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">Consultas Médicas</h1>
          <Button
            onClick={handleCreateConsulta}
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-6 py-2 rounded-lg shadow-lg transition-all duration-200 hover:shadow-xl focus:ring-4 focus:ring-blue-300"
          >
            + Nueva Consulta
          </Button>
        </div>

        {/* Filtros */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Buscar
            </label>
            <Input
              type="text"
              placeholder="Buscar por paciente, especialidad o motivo..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Especialidad
            </label>
            <select
              value={filtros.especialidad || ''}
              onChange={(e) => setFiltros({ ...filtros, especialidad: e.target.value || undefined })}
              className="w-full p-2 rounded bg-transparent border border-gray-300 text-white"
            >
              <option value="" className="bg-gray-800">Todas las especialidades</option>
              {Array.isArray(especialidades)
                ? especialidades.map((especialidad) => (
                    <option key={especialidad} value={especialidad} className="bg-gray-800">
                      {especialidad}
                    </option>
                  ))
                : null}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Estado
            </label>
            <select
              value={filtros.estado || ''}
              onChange={(e) => setFiltros({ ...filtros, estado: e.target.value as any || undefined })}
              className="w-full p-2 rounded bg-transparent border border-gray-300 text-white"
            >
              <option value="" className="bg-gray-800">Todos los estados</option>
              <option value="PROGRAMADA" className="bg-gray-800">Programada</option>
              <option value="EN_CURSO" className="bg-gray-800">En curso</option>
              <option value="COMPLETADA" className="bg-gray-800">Completada</option>
              <option value="CANCELADA" className="bg-gray-800">Cancelada</option>
              <option value="NO_ASISTIO" className="bg-gray-800">No asistió</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Fecha
            </label>
            <Input
              type="date"
              value={fechaSeleccionada}
              onChange={(e) => setFechaSeleccionada(e.target.value)}
              className="text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-white mb-1">
              Tipo de Consulta
            </label>
            <select
              value={filtros.tipo_consulta || ''}
              onChange={(e) => setFiltros({ ...filtros, tipo_consulta: e.target.value as any || undefined })}
              className="w-full p-2 rounded bg-transparent border border-gray-300 text-white"
            >
              <option value="" className="bg-gray-800">Todos los tipos</option>
              <option value="PRIMERA_VEZ" className="bg-gray-800">Primera vez</option>
              <option value="CONTROL" className="bg-gray-800">Control</option>
              <option value="PROCEDIMIENTO" className="bg-gray-800">Procedimiento</option>
            </select>
          </div>

          <div className="flex items-end">
            <Button variant="secondary" onClick={handleClearFilters} className="w-full">
              Limpiar Filtros
            </Button>
          </div>
        </div>

        {!Array.isArray(filteredConsultas) || filteredConsultas.length === 0 ? (
          <div className="text-center p-6 text-white bg-gray-800 bg-opacity-50 rounded-lg">
            No se encontraron consultas con los filtros seleccionados.
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredConsultas.map((consulta) => (
              <div
                key={consulta.id}
                className="bg-gray-800 bg-opacity-50 p-4 rounded-lg cursor-pointer hover:bg-opacity-70 transition-all"
                onClick={() => handleViewConsulta(consulta.id)}
              >
                <div className="flex justify-between items-start mb-2">
                  <h3 className="text-lg font-semibold text-white">
                    {getPacienteName(consulta.paciente_id)}
                  </h3>
                  <span
                    className={`px-2 py-1 rounded text-xs font-medium ${getEstadoColor(consulta.estado)}`}
                  >
                    {consulta.estado?.replace('_', ' ') || 'No definido'}
                  </span>
                </div>

                <p className="text-gray-400 text-sm mb-2">
                  {formatDate(consulta.fecha)} • {formatTime(consulta.hora_inicio)} - {formatTime(consulta.hora_fin)}
                </p>

                <p className="text-white mb-2">
                  <span className="font-medium">Especialidad:</span> {consulta.especialidad}
                </p>

                <p className="text-white mb-2 line-clamp-2">
                  <span className="font-medium">Motivo:</span> {consulta.motivo}
                </p>

                <p className="text-white text-sm">
                  <span className="font-medium">Tipo:</span>{' '}
                  {consulta.tipo_consulta === 'PRIMERA_VEZ' ? 'Primera vez' :
                   consulta.tipo_consulta === 'CONTROL' ? 'Control' : 'Procedimiento'}
                </p>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Consultas;
