import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { consultasService } from '../../services/consultasService';
import { Consulta } from '../../types';
import { pacientesService } from '../../services/pacientesService';
import { medicosService } from '../../services/medicosService';
import { DatosClinicosConsulta } from '../../types/signosVitales';
// Importar componentes y servicios para CIE-11
import { DiagnosticoSelector } from './components/DiagnosticoSelector';
import { cie11Service } from '../../services/cie11Service';
// Importar los servicios correctos o crear los que faltan
import { useForm, useFieldArray } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth } from '../../services/authService';
// Usar componentes UI disponibles en el proyecto

// Mensaje temporal para reemplazar toast
const notificar = (mensaje: string, tipo: 'success' | 'error') => {
  console.log(`${tipo.toUpperCase()}: ${mensaje}`);
  alert(`${mensaje}`);
};

// Esquema de validación para el formulario de consulta completo
const consultaSchema = z.object({
  // Información básica
  paciente_id: z.string().min(1, 'El paciente es requerido'),
  medico_id: z.string().min(1, 'El médico es requerido'),
  especialidad: z.string().min(1, 'La especialidad es requerida'),
  fecha: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Formato de fecha inválido (YYYY-MM-DD)'),
  hora_inicio: z.string().regex(/^\d{2}:\d{2}$/, 'Formato de hora inválido (HH:MM)'),
  hora_fin: z.string().regex(/^\d{2}:\d{2}$/, 'Formato de hora inválido (HH:MM)').optional(),
  motivo: z.string().min(1, 'El motivo es requerido'),
  estado: z.enum(['PROGRAMADA', 'EN_CURSO', 'COMPLETADA', 'CANCELADA', 'NO_ASISTIO']).default('PROGRAMADA'), // Actualizado: FINALIZADA → COMPLETADA
  
  // Campos RIPS obligatorios
  finalidad_consulta: z.enum(['01', '02', '03', '04', '05', '06', '07', '08', '09', '10']),
  causa_externa: z.enum(['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15']),
  tipo_diagnostico: z.enum(['IMPRESION_DIAGNOSTICA', 'CONFIRMADO_NUEVO', 'CONFIRMADO_REPETIDO']),
  codigo_consulta: z.string().min(1, 'El código CUPS es requerido'),
  
  // Diagnósticos
  diagnostico_principal: z.string().min(1, 'El diagnóstico principal es requerido'),
  diagnosticos_secundarios: z.array(
    z.object({
      codigo: z.string().min(1, 'El código CIE10 es requerido'),
      descripcion: z.string().min(1, 'La descripción es requerida')
    })
  ).optional(),
  
  // Medicamentos y procedimientos
  medicamentos: z.array(
    z.object({
      codigo: z.string().min(1, 'El código del medicamento es requerido'),
      nombre: z.string().min(1, 'El nombre del medicamento es requerido'),
      dosis: z.string().min(1, 'La dosis es requerida'),
      via: z.string().min(1, 'La vía de administración es requerida'),
      frecuencia: z.string().min(1, 'La frecuencia es requerida'),
      duracion: z.string().min(1, 'La duración es requerida'),
      indicaciones: z.string().optional()
    })
  ).optional(),
  
  procedimientos: z.array(
    z.object({
      codigo: z.string().min(1, 'El código CUPS es requerido'),
      nombre: z.string().min(1, 'El nombre del procedimiento es requerido'),
      indicaciones: z.string().optional()
    })
  ).optional(),
  
  // Observaciones y plan
  observaciones: z.string().optional(),
  proxima_cita: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Formato de fecha inválido (YYYY-MM-DD)').optional(),
  
  // Campos administrativos
  valor_consulta: z.number().optional(),
  autorizacion_id: z.string().optional(),
  numero_autorizacion: z.string().optional(),
  
  // Datos clínicos
  datos_clinicos: z.object({
    signos_vitales: z.object({
      peso: z.number().optional(),
      estatura: z.number().optional(),
      imc: z.number().optional(),
      presion_arterial_sistolica: z.number().optional(),
      presion_arterial_diastolica: z.number().optional(),
      frecuencia_cardiaca: z.number().optional(),
      frecuencia_respiratoria: z.number().optional(),
      temperatura: z.number().optional(),
      saturacion_oxigeno: z.number().optional(),
      glucemia: z.number().optional(),
      perimetro_abdominal: z.number().optional(),
      perimetro_cefalico: z.number().optional()
    }).optional(),
    hallazgos_examen_fisico: z.string().optional(),
    sintomas_reportados: z.string().optional()
  }).optional()
});

// Interfaces para alinear con el tipo Consulta existente

// Tipo para el payload que se envía al backend
interface ConsultaBackendPayload {
  paciente_id: string;
  medico_id: string;
  especialidad: string;
  fecha: string;
  hora_inicio: string;
  hora_fin?: string;
  motivo: string;
  estado: 'PROGRAMADA' | 'EN_CURSO' | 'COMPLETADA' | 'CANCELADA' | 'NO_ASISTIO'; // Actualizado: FINALIZADA → COMPLETADA
  tipo_diagnostico: 'IMPRESION_DIAGNOSTICA' | 'CONFIRMADO_NUEVO' | 'CONFIRMADO_REPETIDO';
  codigo_consulta: string;
  diagnostico_principal: string;
  observaciones?: string;
  proxima_cita?: string;
  valor_consulta?: number;
  autorizacion_id?: string;
  numero_autorizacion?: string;
  datos_clinicos?: DatosClinicosConsulta;
  
  // Campos transformados
  diagnosticos_secundarios: string[];
  medicamentos_formulados: string[];
  procedimientos_solicitados: string[];
  finalidad_consulta: "01" | "02" | "03" | "04" | "05" | "06" | "07" | "08" | "09" | "10";
  causa_externa: "01" | "02" | "03" | "04" | "05" | "06" | "07" | "08" | "09" | "10" | "11" | "12" | "13" | "14" | "15";
  notas?: string;
}
interface DiagnosticoSecundario {
  codigo: string;
  descripcion: string;
}

interface Medicamento {
  codigo: string;
  nombre: string;
  dosis: string;
  via: string;
  frecuencia: string;
  duracion: string;
  indicaciones?: string;
}

interface Procedimiento {
  codigo: string;
  nombre: string;
  indicaciones?: string;
}

// Tipo completo para el formulario de consulta, con todas las propiedades necesarias
interface ConsultaFormData {
  paciente_id: string;
  medico_id: string;
  especialidad: string;
  fecha: string;
  hora_inicio: string;
  hora_fin?: string;
  motivo: string;
  estado: 'PROGRAMADA' | 'EN_CURSO' | 'COMPLETADA' | 'CANCELADA' | 'NO_ASISTIO'; // Actualizado: FINALIZADA → COMPLETADA
  finalidad_consulta: "01" | "02" | "03" | "04" | "05" | "06" | "07" | "08" | "09" | "10";
  causa_externa: "01" | "02" | "03" | "04" | "05" | "06" | "07" | "08" | "09" | "10" | "11" | "12" | "13" | "14" | "15";
  tipo_diagnostico: 'IMPRESION_DIAGNOSTICA' | 'CONFIRMADO_NUEVO' | 'CONFIRMADO_REPETIDO';
  codigo_consulta: string;
  diagnostico_principal: string;
  diagnosticos_secundarios?: DiagnosticoSecundario[];
  medicamentos?: Medicamento[];
  procedimientos?: Procedimiento[];
  observaciones?: string;
  proxima_cita?: string;
  valor_consulta?: number;
  autorizacion_id?: string;
  numero_autorizacion?: string;
  datos_clinicos?: DatosClinicosConsulta;
}

// Opciones para los campos de selección
const opcionesFinalidadConsulta = [
  { value: '01', label: '01 - Atención del parto' },
  { value: '02', label: '02 - Atención del recién nacido' },
  { value: '03', label: '03 - Atención de planificación familiar' },
  { value: '04', label: '04 - Detección alteraciones crecimiento y desarrollo' },
  { value: '05', label: '05 - Detección alteración desarrollo joven' },
  { value: '06', label: '06 - Detección alteración embarazo' },
  { value: '07', label: '07 - Detección alteración adulto' },
  { value: '08', label: '08 - Detección alteración agudeza visual' },
  { value: '09', label: '09 - Detección enfermedad profesional' },
  { value: '10', label: '10 - No aplica' },
];

const opcionesCausaExterna = [
  { value: '01', label: '01 - Accidente de trabajo' },
  { value: '02', label: '02 - Accidente de tránsito' },
  { value: '03', label: '03 - Accidente rábico' },
  { value: '04', label: '04 - Accidente ofídico' },
  { value: '05', label: '05 - Otro tipo de accidente' },
  { value: '06', label: '06 - Evento catastrófico' },
  { value: '07', label: '07 - Lesión por agresión' },
  { value: '08', label: '08 - Lesión autoinfligida' },
  { value: '09', label: '09 - Sospecha maltrato físico' },
  { value: '10', label: '10 - Sospecha abuso sexual' },
  { value: '11', label: '11 - Sospecha violencia sexual' },
  { value: '12', label: '12 - Sospecha maltrato emocional' },
  { value: '13', label: '13 - Enfermedad general' },
  { value: '14', label: '14 - Enfermedad profesional' },
  { value: '15', label: '15 - Otra' },
];

const opcionesTipoDiagnostico = [
  { value: 'IMPRESION_DIAGNOSTICA', label: 'Impresión diagnóstica' },
  { value: 'CONFIRMADO_NUEVO', label: 'Confirmado nuevo' },
  { value: 'CONFIRMADO_REPETIDO', label: 'Confirmado repetido' },
];

const opcionesViasAdministracion = [
  { value: 'ORAL', label: 'Oral' },
  { value: 'INTRAVENOSA', label: 'Intravenosa' },
  { value: 'INTRAMUSCULAR', label: 'Intramuscular' },
  { value: 'SUBCUTANEA', label: 'Subcutánea' },
  { value: 'TOPICA', label: 'Tópica' },
  { value: 'INHALATORIA', label: 'Inhalatoria' },
  { value: 'RECTAL', label: 'Rectal' },
  { value: 'OFTALMOLOGICA', label: 'Oftalmológica' },
  { value: 'OTICA', label: 'Ótica' },
];

/**
 * Formulario completo para consultas médicas con funcionalidad CRUD
 */
export const FormularioConsulta = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const hospitalId = user?.hospital_id || 1;
  
  // Determinar si estamos en modo edición basado en la presencia del ID
  const isEditing = !!id;
  
  // Estado local para la pestaña activa
  const [activeTab, setActiveTab] = useState<'informacion' | 'signos_vitales' | 'diagnosticos' | 'medicamentos' | 'procedimientos' | 'plan'>('informacion');
  
  // Estado para calcular IMC automáticamente
  const [peso, setPeso] = useState<number | undefined>(undefined);
  const [estatura, setEstatura] = useState<number | undefined>(undefined);
  const [imc, setImc] = useState<number | undefined>(undefined);
  
  // Calcular IMC cuando cambia peso o estatura
  useEffect(() => {
    if (peso && estatura && estatura > 0) {
      // Estatura en metros (convertir de cm a m)
      const estaturaEnMetros = estatura / 100;
      // Fórmula IMC = peso (kg) / (estatura (m))²
      const imcCalculado = peso / (estaturaEnMetros * estaturaEnMetros);
      // Redondear a 2 decimales
      setImc(Math.round(imcCalculado * 100) / 100);
    } else {
      setImc(undefined);
    }
  }, [peso, estatura]);
  
  // Consultas para cargar datos
  const { data: consulta, isLoading: isLoadingConsulta } = useQuery({
    queryKey: ['consulta', id, hospitalId],
    queryFn: () => consultasService.getConsultaById(id!, hospitalId),
    enabled: !!id && !!hospitalId,
  });
  
  const { data: pacientes } = useQuery({
    queryKey: ['pacientes', hospitalId],
    queryFn: () => pacientesService.getPacientes(hospitalId),
  });
  
  const { data: medicos } = useQuery({
    queryKey: ['medicos', hospitalId],
    queryFn: () => medicosService.getMedicos(hospitalId),
  });
  
  // Aquí normalmente cargaríamos las especialidades de un servicio
  const especialidades = [{ id: '1', nombre: 'Medicina General' }, { id: '2', nombre: 'Pediatría' }];
  
  // Cargar códigos CUPS para procedimientos (datos de ejemplo)
  const codigosCUPS = [
    { codigo: '890301', descripcion: 'Consulta de primera vez por medicina general' },
    { codigo: '890701', descripcion: 'Consulta de control por medicina general' },
    { codigo: '890304', descripcion: 'Consulta de primera vez por medicina especializada' },
    { codigo: '893812', descripcion: 'Electrocardiograma de ritmo' },
    { codigo: '902213', descripcion: 'Hemograma' }
  ];
  
  // Formulario principal con react-hook-form
  const {
    register,
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
    reset
  } = useForm<ConsultaFormData>({
    resolver: zodResolver(consultaSchema),
    defaultValues: {
      estado: 'PROGRAMADA',
      finalidad_consulta: '10', // No aplica por defecto
      causa_externa: '13', // Enfermedad general por defecto
      tipo_diagnostico: 'IMPRESION_DIAGNOSTICA',
      diagnosticos_secundarios: [],
      medicamentos: [],
      procedimientos: []
    },
  });
  
  // Field arrays para componentes dinámicos
  const { fields: diagnosticosFields, append: appendDiagnostico, remove: removeDiagnostico } = 
    useFieldArray({ control, name: 'diagnosticos_secundarios' });
    
  const { fields: medicamentosFields, append: appendMedicamento, remove: removeMedicamento } = 
    useFieldArray({ control, name: 'medicamentos' });
    
  const { fields: procedimientosFields, append: appendProcedimiento, remove: removeProcedimiento } = 
    useFieldArray({ control, name: 'procedimientos' });
  
  // Mutaciones para operaciones CRUD
  const createMutation = useMutation({
    mutationFn: (data: ConsultaFormData) => {
      // Convertir los datos para adaptarse al backend creando un nuevo objeto con las propiedades requeridas
      const datosParaBackend: ConsultaBackendPayload = {
        // Datos básicos que copiamos directamente
        paciente_id: data.paciente_id,
        medico_id: data.medico_id,
        especialidad: data.especialidad,
        fecha: data.fecha,
        hora_inicio: data.hora_inicio,
        hora_fin: data.hora_fin,
        motivo: data.motivo,
        estado: data.estado,
        tipo_diagnostico: data.tipo_diagnostico,
        codigo_consulta: data.codigo_consulta,
        diagnostico_principal: data.diagnostico_principal,
        observaciones: data.observaciones,
        proxima_cita: data.proxima_cita,
        valor_consulta: data.valor_consulta,
        autorizacion_id: data.autorizacion_id,
        numero_autorizacion: data.numero_autorizacion,
        datos_clinicos: data.datos_clinicos,
        
        // Campos con tipos específicos
        finalidad_consulta: data.finalidad_consulta as "01" | "02" | "03" | "04" | "05" | "06" | "07" | "08" | "09" | "10",
        causa_externa: data.causa_externa as "01" | "02" | "03" | "04" | "05" | "06" | "07" | "08" | "09" | "10" | "11" | "12" | "13" | "14" | "15",
        
        // Arrays transformados de objetos a strings
        diagnosticos_secundarios: data.diagnosticos_secundarios?.map((d: DiagnosticoSecundario) => d.codigo) || [],
        medicamentos_formulados: data.medicamentos?.map((m: Medicamento) => m.codigo) || [],
        procedimientos_solicitados: data.procedimientos?.map((p: Procedimiento) => p.codigo) || []
      };
      return consultasService.createConsulta(datosParaBackend, hospitalId);
    },
    onSuccess: () => {
      notificar('Consulta creada exitosamente', 'success');
      queryClient.invalidateQueries({ queryKey: ['consultas'] });
      navigate('/consultas');
    },
    onError: (error: Error) => {
      notificar(`Error al crear la consulta: ${error.message}`, 'error');
    }
  });
  
  const updateMutation = useMutation({
    mutationFn: (data: ConsultaFormData) => {
      console.log('Actualizando consulta con datos:', data);
      
      // Convertir los datos para adaptarse al backend creando un nuevo objeto con las propiedades requeridas
      const datosParaBackend: ConsultaBackendPayload = {
        // Datos básicos que copiamos directamente
        paciente_id: data.paciente_id,
        medico_id: data.medico_id,
        especialidad: data.especialidad,
        fecha: data.fecha,
        hora_inicio: data.hora_inicio,
        hora_fin: data.hora_fin,
        motivo: data.motivo,
        estado: data.estado,
        tipo_diagnostico: data.tipo_diagnostico,
        codigo_consulta: data.codigo_consulta,
        diagnostico_principal: data.diagnostico_principal,
        observaciones: data.observaciones,
        proxima_cita: data.proxima_cita,
        valor_consulta: data.valor_consulta,
        autorizacion_id: data.autorizacion_id,
        numero_autorizacion: data.numero_autorizacion,
        datos_clinicos: data.datos_clinicos,
        
        // Campos con tipos específicos
        finalidad_consulta: data.finalidad_consulta as "01" | "02" | "03" | "04" | "05" | "06" | "07" | "08" | "09" | "10",
        causa_externa: data.causa_externa as "01" | "02" | "03" | "04" | "05" | "06" | "07" | "08" | "09" | "10" | "11" | "12" | "13" | "14" | "15",
        
        // Arrays transformados de objetos a strings
        diagnosticos_secundarios: data.diagnosticos_secundarios?.map((d: DiagnosticoSecundario) => d.codigo) || [],
        medicamentos_formulados: data.medicamentos?.map((m: Medicamento) => m.codigo) || [],
        procedimientos_solicitados: data.procedimientos?.map((p: Procedimiento) => p.codigo) || [],
        
        // Convertir datos clínicos a formato JSON en el campo notas si existen
        notas: data.datos_clinicos ? JSON.stringify({
          datos_clinicos: data.datos_clinicos,
          notas_adicionales: data.observaciones
        }) : data.observaciones
      };
      
      console.log('Datos transformados para backend:', datosParaBackend);
      
      // Agregar una pausa para mostrar el mensaje antes de la navegación
      return consultasService.updateConsulta(id!, datosParaBackend, hospitalId)
        .then((result: Consulta) => {
          console.log('Respuesta del servidor:', result);
          return result;
        });
    },
    onSuccess: (data) => {
      console.log('Actualización exitosa:', data);
      // Mostrar mensaje de éxito
      notificar('Consulta actualizada exitosamente', 'success');
      
      // Invalidar consultas en caché
      queryClient.invalidateQueries({ queryKey: ['consulta', id] });
      queryClient.invalidateQueries({ queryKey: ['consultas'] });
      
      // Esperar un momento antes de navegar para que el usuario vea el mensaje
      setTimeout(() => {
        navigate(`/consultas/${id}`);
      }, 1500);
    },
    onError: (error: Error) => {
      console.error('Error al actualizar:', error);
      notificar(`Error al actualizar la consulta: ${error.message}`, 'error');
    }
  });
  
  const deleteMutation = useMutation({
    mutationFn: () => consultasService.deleteConsulta(id!),
    onSuccess: () => {
      notificar('Consulta eliminada exitosamente', 'success');
      queryClient.invalidateQueries({ queryKey: ['consultas'] });
      navigate('/consultas');
    },
    onError: (error: Error) => {
      notificar(`Error al eliminar la consulta: ${error.message}`, 'error');
    }
  });
  
  // Cargar datos si estamos editando
  useEffect(() => {
    if (consulta && id) {
      // Convertir las listas de objetos a los formatos esperados por el formulario
      const diagnosticosSecundarios: DiagnosticoSecundario[] = consulta.diagnosticos_secundarios ? 
        consulta.diagnosticos_secundarios.map((diag: string | { codigo: string; descripcion: string }) => {
          // Si es solo un código, necesitamos obtener la descripción
          if (typeof diag === 'string') {
            return { codigo: diag, descripcion: 'Pendiente descripción' };
          }
          // Si ya es un objeto
          return diag;
        }) : [];
      
      const medicamentos: Medicamento[] = consulta.medicamentos_formulados ? 
        consulta.medicamentos_formulados.map((med: string | Medicamento) => {
          // Si es solo un código o string
          if (typeof med === 'string') {
            return {
              codigo: med,
              nombre: 'Pendiente nombre',
              dosis: 'Pendiente dosis',
              via: 'ORAL',
              frecuencia: 'Pendiente frecuencia',
              duracion: 'Pendiente duración',
              indicaciones: ''
            };
          }
          // Si ya es un objeto
          return med;
        }) : [];
      
      const procedimientos: Procedimiento[] = consulta.procedimientos_solicitados ? 
        consulta.procedimientos_solicitados.map((proc: string | Procedimiento) => {
          // Si es solo un código o string
          if (typeof proc === 'string') {
            return {
              codigo: proc,
              nombre: 'Pendiente nombre',
              indicaciones: ''
            };
          }
          // Si ya es un objeto
          return proc;
        }) : [];
      
      // Valor por defecto para campos que podrían faltar
      const finalidadConsulta = consulta.finalidad_consulta || '10'; // No aplica por defecto
      const causaExterna = consulta.causa_externa || '13'; // Enfermedad general por defecto
      const tipoDiagnostico = consulta.tipo_diagnostico || 'IMPRESION_DIAGNOSTICA';
      
      // No necesitamos configurar selectores ya que estamos usando los elementos nativos de HTML
      
      // Resetear el formulario con los datos
      reset({
        paciente_id: consulta.paciente_id.toString(),
        medico_id: consulta.medico_id.toString(),
        especialidad: consulta.especialidad,
        fecha: new Date(consulta.fecha).toISOString().split('T')[0],
        hora_inicio: consulta.hora_inicio,
        hora_fin: consulta.hora_fin || '',
        motivo: consulta.motivo,
        estado: consulta.estado,
        finalidad_consulta: finalidadConsulta,
        causa_externa: causaExterna,
        tipo_diagnostico: tipoDiagnostico,
        codigo_consulta: consulta.codigo_consulta || '',
        diagnostico_principal: consulta.diagnostico_principal || '',
        diagnosticos_secundarios: diagnosticosSecundarios,
        medicamentos: medicamentos,
        procedimientos: procedimientos,
        observaciones: consulta.observaciones || '',
        proxima_cita: consulta.proxima_cita ? new Date(consulta.proxima_cita).toISOString().split('T')[0] : '',
        valor_consulta: consulta.valor_consulta || 0,
        autorizacion_id: consulta.autorizacion_id || '',
        numero_autorizacion: consulta.numero_autorizacion || '',
        datos_clinicos: consulta.datos_clinicos
      });
    }
  }, [consulta, id, reset, pacientes, medicos, especialidades]);
  
  // Función para manejar el envío del formulario
  const onSubmit = (data: ConsultaFormData) => {
    console.log('Formulario enviado con datos:', data);
    
    try {
      if (id) {
        console.log('Modo edición detectado, id:', id);
        updateMutation.mutate(data);
      } else {
        console.log('Modo creación detectado');
        createMutation.mutate(data);
      }
    } catch (error) {
      console.error('Error en onSubmit:', error);
      notificar('Error al procesar el formulario', 'error');
    }
  };
  
  // Función para eliminar una consulta
  const handleDelete = () => {
    if (window.confirm('¿Está seguro que desea eliminar esta consulta?')) {
      deleteMutation.mutate();
    }
  };
  

  
  // Si está cargando la consulta, mostrar un indicador de carga
  if (isLoadingConsulta) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  // Renderizado del formulario
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="mb-6 flex justify-between items-center">
          <h1 className="text-2xl font-semibold text-gray-800">
            {id ? 'Editar Consulta' : 'Nueva Consulta'}
          </h1>
          {id && (
            <button
              type="button"
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md"
            >
              Eliminar
            </button>
          )}
        </div>
        
        {/* Pestañas de navegación */}
        <div className="mb-6 border-b border-gray-200">
          <ul className="flex flex-wrap -mb-px text-sm font-medium text-center">
            <li className="mr-2">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${activeTab === 'informacion' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'}`}
                onClick={() => setActiveTab('informacion')}
              >
                Información Básica
              </button>
            </li>
            <li className="mr-2">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${activeTab === 'signos_vitales' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'}`}
                onClick={() => setActiveTab('signos_vitales')}
              >
                Signos Vitales
              </button>
            </li>
            <li className="mr-2">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${activeTab === 'diagnosticos' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'}`}
                onClick={() => setActiveTab('diagnosticos')}
              >
                Diagnósticos
              </button>
            </li>
            <li className="mr-2">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${activeTab === 'medicamentos' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'}`}
                onClick={() => setActiveTab('medicamentos')}
              >
                Medicamentos
              </button>
            </li>
            <li className="mr-2">
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${activeTab === 'procedimientos' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'}`}
                onClick={() => setActiveTab('procedimientos')}
              >
                Procedimientos
              </button>
            </li>
            <li>
              <button
                className={`inline-block p-4 border-b-2 rounded-t-lg ${activeTab === 'plan' ? 'border-blue-600 text-blue-600' : 'border-transparent hover:text-gray-600 hover:border-gray-300'}`}
                onClick={() => setActiveTab('plan')}
              >
                Plan y Observaciones
              </button>
            </li>
          </ul>
        </div>
  
        {/* Formulario */}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Contenido de las pestañas */}
          {activeTab === 'informacion' && (
            <div className="space-y-4">
              {/* Información básica de la consulta */}
              <h2 className="text-xl font-semibold text-gray-700 pb-2 border-b">Información de la Consulta</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Paciente */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Paciente *</label>
                  <select
                    {...register('paciente_id')}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    <option value="">Seleccione un paciente</option>
                    {pacientes?.map((paciente: any) => (
                      <option key={paciente.id} value={paciente.id}>
                        {paciente.nombre_completo}
                      </option>
                    ))}
                  </select>
                  {errors.paciente_id && (
                    <p className="text-red-500 text-xs italic">{errors.paciente_id.message}</p>
                  )}
                </div>
                
                {/* Médico */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Médico *</label>
                  <select
                    {...register('medico_id')}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    <option value="">Seleccione un médico</option>
                    {medicos?.map((medico) => (
                      <option key={medico.id} value={medico.id}>
                        {medico.nombre_completo}
                      </option>
                    ))}
                  </select>
                  {errors.medico_id && (
                    <p className="text-red-500 text-xs italic">{errors.medico_id.message}</p>
                  )}
                </div>
                
                {/* Especialidad */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Especialidad *</label>
                  <input
                    {...register('especialidad')}
                    type="text"
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    placeholder="Especialidad médica"
                  />
                  {errors.especialidad && (
                    <p className="text-red-500 text-xs italic">{errors.especialidad.message}</p>
                  )}
                </div>
                
                {/* Fecha */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Fecha *</label>
                  <input
                    {...register('fecha')}
                    type="date"
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  />
                  {errors.fecha && (
                    <p className="text-red-500 text-xs italic">{errors.fecha.message}</p>
                  )}
                </div>
                
                {/* Hora inicio */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Hora inicio *</label>
                  <input
                    {...register('hora_inicio')}
                    type="time"
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  />
                  {errors.hora_inicio && (
                    <p className="text-red-500 text-xs italic">{errors.hora_inicio.message}</p>
                  )}
                </div>
                
                {/* Hora fin */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Hora fin</label>
                  <input
                    {...register('hora_fin')}
                    type="time"
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  />
                  {errors.hora_fin && (
                    <p className="text-red-500 text-xs italic">{errors.hora_fin.message}</p>
                  )}
                </div>
                
                {/* Estado */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Estado *</label>
                  <select
                    {...register('estado')}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    <option value="PROGRAMADA">Programada</option>
                    <option value="EN_CURSO">En curso</option>
                    <option value="COMPLETADA">Completada</option>
                    <option value="CANCELADA">Cancelada</option>
                    <option value="NO_ASISTIO">No asistió</option>
                  </select>
                  {errors.estado && (
                    <p className="text-red-500 text-xs italic">{errors.estado.message}</p>
                  )}
                </div>
              </div>
              
              {/* Motivo de consulta */}
              <div className="space-y-2 mt-4">
                <label className="block text-sm font-medium text-gray-700">Motivo de consulta *</label>
                <textarea
                  {...register('motivo')}
                  rows={3}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  placeholder="Describa el motivo de la consulta"
                ></textarea>
                {errors.motivo && (
                  <p className="text-red-500 text-xs italic">{errors.motivo.message}</p>
                )}
              </div>
              
              {/* Campos RIPS obligatorios */}
              <h3 className="text-lg font-medium text-gray-700 mt-6 mb-3">Información RIPS</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Finalidad de la consulta */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Finalidad de la consulta *</label>
                  <select
                    {...register('finalidad_consulta')}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    {opcionesFinalidadConsulta.map((opcion) => (
                      <option key={opcion.value} value={opcion.value}>
                        {opcion.label}
                      </option>
                    ))}
                  </select>
                  {errors.finalidad_consulta && (
                    <p className="text-red-500 text-xs italic">{errors.finalidad_consulta.message}</p>
                  )}
                </div>
                
                {/* Causa externa */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Causa externa *</label>
                  <select
                    {...register('causa_externa')}
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  >
                    {opcionesCausaExterna.map((opcion) => (
                      <option key={opcion.value} value={opcion.value}>
                        {opcion.label}
                      </option>
                    ))}
                  </select>
                  {errors.causa_externa && (
                    <p className="text-red-500 text-xs italic">{errors.causa_externa.message}</p>
                  )}
                </div>
                
                {/* Código CUPS de la consulta */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Código CUPS *</label>
                  <input
                    {...register('codigo_consulta')}
                    type="text"
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                    placeholder="Código CUPS"
                  />
                  {errors.codigo_consulta && (
                    <p className="text-red-500 text-xs italic">{errors.codigo_consulta.message}</p>
                  )}
                </div>
              </div>
            </div>
          )}
          
          {activeTab === 'signos_vitales' && (
            <div className="space-y-6">
              <h3 className="text-lg font-medium leading-6 text-gray-900">Signos Vitales y Antropometría</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Peso */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Peso (kg)</label>
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    {...register('datos_clinicos.signos_vitales.peso', {
                      valueAsNumber: true,
                      onChange: (e) => setPeso(parseFloat(e.target.value) || undefined)
                    })}
                    className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                {/* Estatura */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Estatura (cm)</label>
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    {...register('datos_clinicos.signos_vitales.estatura', {
                      valueAsNumber: true,
                      onChange: (e) => setEstatura(parseFloat(e.target.value) || undefined)
                    })}
                    className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                {/* IMC calculado automáticamente */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">IMC</label>
                  <input
                    type="text"
                    readOnly
                    value={imc !== undefined ? imc.toString() : ''}
                    {...register('datos_clinicos.signos_vitales.imc', { value: imc })}
                    className="mt-1 block w-full p-2 bg-gray-100 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  {imc !== undefined && (
                    <div className="text-xs">
                      {imc < 18.5 && <span className="text-yellow-600">Bajo peso</span>}
                      {imc >= 18.5 && imc < 25 && <span className="text-green-600">Peso normal</span>}
                      {imc >= 25 && imc < 30 && <span className="text-yellow-600">Sobrepeso</span>}
                      {imc >= 30 && <span className="text-red-600">Obesidad</span>}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Presión arterial */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Presión arterial (mmHg)</label>
                  <div className="flex space-x-2">
                    <input
                      type="number"
                      min="0"
                      placeholder="Sistólica"
                      {...register('datos_clinicos.signos_vitales.presion_arterial_sistolica', { valueAsNumber: true })}
                      className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                    <span className="flex items-center">/</span>
                    <input
                      type="number"
                      min="0"
                      placeholder="Diastólica"
                      {...register('datos_clinicos.signos_vitales.presion_arterial_diastolica', { valueAsNumber: true })}
                      className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                
                {/* Frecuencia cardíaca */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Frecuencia cardíaca (lpm)</label>
                  <input
                    type="number"
                    min="0"
                    {...register('datos_clinicos.signos_vitales.frecuencia_cardiaca', { valueAsNumber: true })}
                    className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                {/* Frecuencia respiratoria */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Frecuencia respiratoria (rpm)</label>
                  <input
                    type="number"
                    min="0"
                    {...register('datos_clinicos.signos_vitales.frecuencia_respiratoria', { valueAsNumber: true })}
                    className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Temperatura */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Temperatura (°C)</label>
                  <input
                    type="number"
                    step="0.1"
                    min="30"
                    max="45"
                    {...register('datos_clinicos.signos_vitales.temperatura', { valueAsNumber: true })}
                    className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                {/* Saturación de oxígeno */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Saturación O₂ (%)</label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    {...register('datos_clinicos.signos_vitales.saturacion_oxigeno', { valueAsNumber: true })}
                    className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                {/* Glucemia */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Glucemia (mg/dL)</label>
                  <input
                    type="number"
                    min="0"
                    {...register('datos_clinicos.signos_vitales.glucemia', { valueAsNumber: true })}
                    className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Perímetro abdominal */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Perímetro abdominal (cm)</label>
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    {...register('datos_clinicos.signos_vitales.perimetro_abdominal', { valueAsNumber: true })}
                    className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                {/* Perímetro cefálico (sólo para pacientes pediátricos) */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Perímetro cefálico (cm)</label>
                  <input
                    type="number"
                    step="0.1"
                    min="0"
                    {...register('datos_clinicos.signos_vitales.perimetro_cefalico', { valueAsNumber: true })}
                    className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <span className="text-xs text-gray-500">Solo para pacientes pediátricos</span>
                </div>
              </div>
              
              {/* Hallazgos del examen físico */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Hallazgos del Examen Físico</label>
                <textarea
                  rows={4}
                  {...register('datos_clinicos.hallazgos_examen_fisico')}
                  className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Describa los hallazgos relevantes del examen físico..."
                />
              </div>
              
              {/* Síntomas reportados por el paciente */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Síntomas Reportados por el Paciente</label>
                <textarea
                  rows={4}
                  {...register('datos_clinicos.sintomas_reportados')}
                  className="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Describa los síntomas que reporta el paciente..."
                />
              </div>
            </div>
          )}
            
          {activeTab === 'diagnosticos' && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold text-gray-700 pb-2 border-b">Diagnósticos</h2>
              
              {/* Tipo de diagnóstico */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Tipo de diagnóstico *</label>
                <select
                  {...register('tipo_diagnostico')}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  {opcionesTipoDiagnostico.map((opcion) => (
                    <option key={opcion.value} value={opcion.value}>
                      {opcion.label}
                    </option>
                  ))}
                </select>
                {errors.tipo_diagnostico && (
                  <p className="text-red-500 text-xs italic">{errors.tipo_diagnostico.message}</p>
                )}
              </div>
              
              {/* Diagnóstico principal (CIE-11) */}
              <div className="space-y-2">
                <DiagnosticoSelector
                  onSelectDiagnostico={(codigo) => {
                    setValue('diagnostico_principal', codigo);
                    // Registrar el uso del diagnóstico
                    if (codigo) {
                      const medico_id = watch('medico_id');
                      const paciente_id = watch('paciente_id');
                      const especialidad = watch('especialidad');
                      
                      if (medico_id && paciente_id && especialidad) {
                        cie11Service.registrarUsoDiagnostico(codigo, hospitalId, {
                          medico_id,
                          paciente_id,
                          especialidad
                        }).catch(error => {
                          console.error("Error al registrar uso del diagnóstico:", error);
                        });
                      }
                    }
                  }}
                  especialidadId={Number(watch('especialidad'))}
                  initialCodigo={watch('diagnostico_principal')}
                />
                {errors.diagnostico_principal && (
                  <p className="text-red-500 text-xs italic">{errors.diagnostico_principal.message}</p>
                )}
              </div>
              
              {/* Diagnósticos secundarios */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Diagnósticos secundarios</label>
                <div className="space-y-4">
                  {diagnosticosFields.map((field, index) => (
                    <div key={field.id} className="border border-gray-200 rounded-md p-3 bg-gray-50">
                      <div className="flex justify-between items-center mb-2">
                        <h4 className="text-sm font-medium">Diagnóstico secundario #{index + 1}</h4>
                        <button
                          type="button"
                          className="text-red-500 hover:text-red-700"
                          onClick={() => removeDiagnostico(index)}
                        >
                          &times;
                        </button>
                      </div>
                      
                      <DiagnosticoSelector
                        onSelectDiagnostico={(codigo) => {
                          // Actualizamos el código y podemos usar el componente directamente para obtener la descripción
                          setValue(`diagnosticos_secundarios.${index}.codigo`, codigo);
                          // Si necesitáramos establecer la descripción, podríamos hacerlo de otra manera
                          // o desde el backend al guardar la consulta
                          
                          // Registrar el uso del diagnóstico
                          if (codigo) {
                            const medico_id = watch('medico_id');
                            const paciente_id = watch('paciente_id');
                            const especialidad = watch('especialidad');
                            
                            if (medico_id && paciente_id && especialidad) {
                              cie11Service.registrarUsoDiagnostico(codigo, hospitalId, {
                                medico_id,
                                paciente_id,
                                especialidad
                              }).catch(error => {
                                console.error("Error al registrar uso del diagnóstico:", error);
                              });
                            }
                          }
                        }}
                        especialidadId={Number(watch('especialidad'))}
                        initialCodigo={watch(`diagnosticos_secundarios.${index}.codigo`)}
                        initialDescripcion={watch(`diagnosticos_secundarios.${index}.descripcion`)}
                      />
                      
                      {/* Campos ocultos para mantener la compatibilidad con el formulario */}
                      <input
                        {...register(`diagnosticos_secundarios.${index}.codigo` as const)}
                        type="hidden"
                      />
                      <input
                        {...register(`diagnosticos_secundarios.${index}.descripcion` as const)}
                        type="hidden"
                      />
                    </div>
                  ))}
                </div>
                <button
                  type="button"
                  className="mt-2 bg-blue-100 hover:bg-blue-200 text-blue-800 font-medium py-2 px-4 rounded-md"
                  onClick={() => appendDiagnostico({ codigo: '', descripcion: '' })}
                >
                  + Agregar diagnóstico secundario
                </button>
              </div>
            </div>
          )}
          
          {activeTab === 'medicamentos' && (
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-700 pb-2 border-b">Medicamentos</h2>
              
              {/* Lista de medicamentos */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Medicamentos formulados</label>
                
                {medicamentosFields.length === 0 && (
                  <p className="text-sm text-gray-500 italic">No hay medicamentos formulados aún</p>
                )}
                
                <div className="space-y-4">
                  {medicamentosFields.map((field, index) => (
                    <div key={field.id} className="border border-gray-200 rounded-md p-4 bg-gray-50">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="text-md font-medium">Medicamento #{index + 1}</h3>
                        <button
                          type="button"
                          className="text-red-500 hover:text-red-700"
                          onClick={() => removeMedicamento(index)}
                        >
                          Eliminar
                        </button>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Código y nombre */}
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">Código *</label>
                          <input
                            {...register(`medicamentos.${index}.codigo` as const)}
                            type="text"
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                            placeholder="Código del medicamento"
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">Nombre *</label>
                          <input
                            {...register(`medicamentos.${index}.nombre` as const)}
                            type="text"
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                            placeholder="Nombre del medicamento"
                          />
                        </div>
                        
                        {/* Dosis y vía */}
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">Dosis *</label>
                          <input
                            {...register(`medicamentos.${index}.dosis` as const)}
                            type="text"
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                            placeholder="Ej. 500mg"
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">Vía de administración *</label>
                          <select
                            {...register(`medicamentos.${index}.via` as const)}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                          >
                            {opcionesViasAdministracion.map((opcion) => (
                              <option key={opcion.value} value={opcion.value}>
                                {opcion.label}
                              </option>
                            ))}
                          </select>
                        </div>
                        
                        {/* Frecuencia y duración */}
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">Frecuencia *</label>
                          <input
                            {...register(`medicamentos.${index}.frecuencia` as const)}
                            type="text"
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                            placeholder="Ej. Cada 8 horas"
                          />
                        </div>
                        
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">Duración *</label>
                          <input
                            {...register(`medicamentos.${index}.duracion` as const)}
                            type="text"
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                            placeholder="Ej. 7 días"
                          />
                        </div>
                      </div>
                      
                      {/* Indicaciones */}
                      <div className="space-y-2 mt-4">
                        <label className="block text-sm font-medium text-gray-700">Indicaciones adicionales</label>
                        <textarea
                          {...register(`medicamentos.${index}.indicaciones` as const)}
                          rows={2}
                          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                          placeholder="Indicaciones especiales para el paciente"
                        ></textarea>
                      </div>
                    </div>
                  ))}
                </div>
                
                <button
                  type="button"
                  className="mt-4 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded-md"
                  onClick={() => appendMedicamento({ 
                    codigo: '', 
                    nombre: '', 
                    dosis: '', 
                    via: 'ORAL', 
                    frecuencia: '', 
                    duracion: '',
                    indicaciones: ''
                  })}
                >
                  + Agregar medicamento
                </button>
              </div>
            </div>
          )}
          
          {activeTab === 'procedimientos' && (
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-700 pb-2 border-b">Procedimientos</h2>
              
              {/* Código CUPS */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Código CUPS de la consulta *</label>
                <select
                  {...register('codigo_consulta')}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  <option value="">Seleccione un código CUPS</option>
                  {codigosCUPS.map((codigo) => (
                    <option key={codigo.codigo} value={codigo.codigo}>
                      {codigo.codigo} - {codigo.descripcion}
                    </option>
                  ))}
                </select>
                {errors.codigo_consulta && (
                  <p className="text-red-500 text-xs italic">{errors.codigo_consulta.message}</p>
                )}
              </div>
              
              {/* Procedimientos solicitados */}
              <div className="space-y-2 mt-4">
                <label className="block text-sm font-medium text-gray-700">Procedimientos adicionales solicitados</label>
                
                {procedimientosFields.length === 0 && (
                  <p className="text-sm text-gray-500 italic">No hay procedimientos adicionales solicitados</p>
                )}
                
                <div className="space-y-4">
                  {procedimientosFields.map((field, index) => (
                    <div key={field.id} className="border border-gray-200 rounded-md p-4 bg-gray-50">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="text-md font-medium">Procedimiento #{index + 1}</h3>
                        <button
                          type="button"
                          className="text-red-500 hover:text-red-700"
                          onClick={() => removeProcedimiento(index)}
                        >
                          Eliminar
                        </button>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {/* Código y nombre */}
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">Código CUPS *</label>
                          <select
                            {...register(`procedimientos.${index}.codigo` as const)}
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                          >
                            <option value="">Seleccione un código CUPS</option>
                            {codigosCUPS.map((codigo) => (
                              <option key={codigo.codigo} value={codigo.codigo}>
                                {codigo.codigo} - {codigo.descripcion}
                              </option>
                            ))}
                          </select>
                        </div>
                        
                        <div className="space-y-2">
                          <label className="block text-sm font-medium text-gray-700">Nombre *</label>
                          <input
                            {...register(`procedimientos.${index}.nombre` as const)}
                            type="text"
                            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                            placeholder="Nombre del procedimiento"
                          />
                        </div>
                      </div>
                      
                      {/* Indicaciones */}
                      <div className="space-y-2 mt-4">
                        <label className="block text-sm font-medium text-gray-700">Indicaciones adicionales</label>
                        <textarea
                          {...register(`procedimientos.${index}.indicaciones` as const)}
                          rows={2}
                          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                          placeholder="Indicaciones especiales para el procedimiento"
                        ></textarea>
                      </div>
                    </div>
                  ))}
                </div>
                
                <button
                  type="button"
                  className="mt-4 bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded-md"
                  onClick={() => appendProcedimiento({ 
                    codigo: '', 
                    nombre: '',
                    indicaciones: ''
                  })}
                >
                  + Agregar procedimiento
                </button>
              </div>
            </div>
          )}
          
          {activeTab === 'plan' && (
            <div className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-700 pb-2 border-b">Plan y Observaciones</h2>
              
              {/* Observaciones */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Observaciones clínicas</label>
                <textarea
                  {...register('observaciones')}
                  rows={4}
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  placeholder="Observaciones clínicas adicionales"
                ></textarea>
                {errors.observaciones && (
                  <p className="text-red-500 text-xs italic">{errors.observaciones.message}</p>
                )}
              </div>
              
              {/* Próxima cita */}
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Próxima cita</label>
                <input
                  {...register('proxima_cita')}
                  type="date"
                  className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                />
                {errors.proxima_cita && (
                  <p className="text-red-500 text-xs italic">{errors.proxima_cita.message}</p>
                )}
              </div>
              
              {/* Datos administrativos */}
              <h3 className="text-lg font-medium text-gray-700 mt-6 mb-3">Datos administrativos</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Valor de la consulta */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Valor de la consulta</label>
                  <input
                    {...register('valor_consulta', { valueAsNumber: true })}
                    type="number"
                    step="0.01"
                    min="0"
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  />
                  {errors.valor_consulta && (
                    <p className="text-red-500 text-xs italic">{errors.valor_consulta.message}</p>
                  )}
                </div>
                
                {/* Número de autorización */}
                <div className="space-y-2">
                  <label className="block text-sm font-medium text-gray-700">Número de autorización</label>
                  <input
                    {...register('numero_autorizacion')}
                    type="text"
                    className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                  />
                  {errors.numero_autorizacion && (
                    <p className="text-red-500 text-xs italic">{errors.numero_autorizacion.message}</p>
                  )}
                </div>
              </div>
            </div>
          )}
          
          {/* Botones de acción */}
          <div className="flex justify-end space-x-4 pt-4">
            <button
              type="button"
              onClick={() => navigate('/consultas')}
              className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md disabled:opacity-50"
            >
              {isSubmitting ? 'Guardando...' : isEditing ? 'Actualizar' : 'Guardar'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
