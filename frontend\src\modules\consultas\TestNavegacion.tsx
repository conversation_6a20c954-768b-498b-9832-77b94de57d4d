import { useNavigate } from 'react-router-dom';
import { Button } from '../../components/ui/Button';

export const TestNavegacion = () => {
  const navigate = useNavigate();

  const testNavigation = () => {
    console.log('Navegando a /consultas/nuevo...');
    navigate('/consultas/nuevo');
  };

  return (
    <div className="p-4 bg-blue-100 rounded-lg">
      <h3 className="text-lg font-semibold mb-2">Test de Navegación</h3>
      <p className="mb-4">Prueba la navegación al formulario de nueva consulta:</p>
      <Button onClick={testNavigation}>
        Ir a Nueva Consulta (Test)
      </Button>
    </div>
  );
};
