import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../context/AuthContext';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Select } from '../../components/ui/Select';
import { Modal } from '../../components/ui/Modal';
import { EstadoBadge } from '../../components/ui/EstadosBadges';
import { ModalConfirmacion } from '../../components/ui/ModalConfirmacion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faSearch,
  faCreditCard,
  faMoneyBillWave,
  faCalendarAlt,
  faUser,
  faFileInvoice,
  faCheck,
  faTimes,
  faEye,
  faEdit
} from '@fortawesome/free-solid-svg-icons';

// Tipos para pagos
interface Pago {
  id: string;
  factura_id: string;
  monto: number;
  metodo_pago: 'EFECTIVO' | 'TARJETA_CREDITO' | 'TARJETA_DEBITO' | 'TRANSFERENCIA' | 'CHEQUE' | 'PSE';
  referencia_pago?: string;
  fecha_pago: string;
  estado: 'PENDIENTE' | 'COMPLETADO' | 'FALLIDO' | 'REVERTIDO';
  observaciones?: string;
  usuario_id: string;
  hospital_id: number;
  // Datos relacionados
  factura?: {
    numero_factura: string;
    monto_total: number;
    paciente?: {
      nombre_completo: string;
    };
  };
  usuario?: {
    nombre: string;
  };
}

interface FormPago {
  factura_id: string;
  monto: number;
  metodo_pago: 'EFECTIVO' | 'TARJETA_CREDITO' | 'TARJETA_DEBITO' | 'TRANSFERENCIA' | 'CHEQUE' | 'PSE';
  referencia_pago?: string;
  observaciones?: string;
}

const GestorPagos: React.FC = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const hospitalId = user?.hospital_id || 1;

  // Estados
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [selectedPago, setSelectedPago] = useState<Pago | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [estadoFilter, setEstadoFilter] = useState<string>('');
  const [metodoFilter, setMetodoFilter] = useState<string>('');
  const [fechaInicio, setFechaInicio] = useState('');
  const [fechaFin, setFechaFin] = useState('');

  // Formulario
  const [formData, setFormData] = useState<FormPago>({
    factura_id: '',
    monto: 0,
    metodo_pago: 'EFECTIVO',
    referencia_pago: '',
    observaciones: ''
  });

  // Query para obtener pagos
  const { data: pagos, isLoading } = useQuery<Pago[]>({
    queryKey: ['pagos', hospitalId, estadoFilter, metodoFilter, fechaInicio, fechaFin],
    queryFn: async () => {
      // Simulación de datos - en producción sería una llamada a la API
      return [
        {
          id: '1',
          factura_id: '1',
          monto: 150000,
          metodo_pago: 'TARJETA_CREDITO',
          referencia_pago: 'TXN123456789',
          fecha_pago: '2024-01-15T10:30:00Z',
          estado: 'COMPLETADO',
          observaciones: 'Pago procesado exitosamente',
          usuario_id: '1',
          hospital_id: hospitalId,
          factura: {
            numero_factura: 'FAC-2024-001',
            monto_total: 150000,
            paciente: {
              nombre_completo: 'Juan Pérez García'
            }
          },
          usuario: {
            nombre: 'Cajero Principal'
          }
        },
        {
          id: '2',
          factura_id: '2',
          monto: 75000,
          metodo_pago: 'EFECTIVO',
          fecha_pago: '2024-01-15T14:20:00Z',
          estado: 'COMPLETADO',
          usuario_id: '2',
          hospital_id: hospitalId,
          factura: {
            numero_factura: 'FAC-2024-002',
            monto_total: 75000,
            paciente: {
              nombre_completo: 'María González López'
            }
          },
          usuario: {
            nombre: 'Cajero Auxiliar'
          }
        },
        {
          id: '3',
          factura_id: '3',
          monto: 200000,
          metodo_pago: 'TRANSFERENCIA',
          referencia_pago: 'TRANS987654321',
          fecha_pago: '2024-01-16T09:15:00Z',
          estado: 'PENDIENTE',
          observaciones: 'Esperando confirmación bancaria',
          usuario_id: '1',
          hospital_id: hospitalId,
          factura: {
            numero_factura: 'FAC-2024-003',
            monto_total: 200000,
            paciente: {
              nombre_completo: 'Carlos Rodríguez Martín'
            }
          },
          usuario: {
            nombre: 'Cajero Principal'
          }
        }
      ];
    }
  });

  // Query para obtener facturas pendientes de pago
  const { data: facturasPendientes } = useQuery({
    queryKey: ['facturas-pendientes', hospitalId],
    queryFn: async () => {
      return [
        { id: '1', numero_factura: 'FAC-2024-001', monto_total: 150000, paciente: 'Juan Pérez García' },
        { id: '2', numero_factura: 'FAC-2024-002', monto_total: 75000, paciente: 'María González López' },
        { id: '3', numero_factura: 'FAC-2024-003', monto_total: 200000, paciente: 'Carlos Rodríguez Martín' },
        { id: '4', numero_factura: 'FAC-2024-004', monto_total: 120000, paciente: 'Ana Martínez Silva' }
      ];
    }
  });

  // Mutación para crear pago
  const createPagoMutation = useMutation({
    mutationFn: async (data: FormPago) => {
      console.log('Creando pago:', data);
      return { id: Date.now().toString(), ...data };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pagos'] });
      queryClient.invalidateQueries({ queryKey: ['facturas'] });
      setIsModalOpen(false);
      resetForm();
    }
  });

  // Mutación para confirmar pago
  const confirmarPagoMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log('Confirmando pago:', id);
      return { id };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pagos'] });
      setIsConfirmModalOpen(false);
    }
  });

  // Mutación para revertir pago
  const revertirPagoMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log('Revirtiendo pago:', id);
      return { id };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['pagos'] });
    }
  });

  // Filtrar pagos
  const filteredPagos = pagos?.filter(pago => {
    const matchesSearch = 
      pago.factura?.numero_factura.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pago.factura?.paciente?.nombre_completo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pago.referencia_pago?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesEstado = estadoFilter ? pago.estado === estadoFilter : true;
    const matchesMetodo = metodoFilter ? pago.metodo_pago === metodoFilter : true;

    const matchesFecha = (!fechaInicio || pago.fecha_pago >= fechaInicio) && 
                        (!fechaFin || pago.fecha_pago <= fechaFin);

    return matchesSearch && matchesEstado && matchesMetodo && matchesFecha;
  }) || [];

  // Manejar envío del formulario
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.factura_id && formData.monto > 0) {
      createPagoMutation.mutate(formData);
    }
  };

  // Resetear formulario
  const resetForm = () => {
    setFormData({
      factura_id: '',
      monto: 0,
      metodo_pago: 'EFECTIVO',
      referencia_pago: '',
      observaciones: ''
    });
  };

  // Formatear fecha
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('es-ES', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Formatear moneda
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Obtener etiqueta del método de pago
  const getMetodoPagoLabel = (metodo: string) => {
    const labels: Record<string, string> = {
      'EFECTIVO': 'Efectivo',
      'TARJETA_CREDITO': 'Tarjeta de Crédito',
      'TARJETA_DEBITO': 'Tarjeta de Débito',
      'TRANSFERENCIA': 'Transferencia',
      'CHEQUE': 'Cheque',
      'PSE': 'PSE'
    };
    return labels[metodo] || metodo;
  };

  return (
    <div className="p-6 bg-white min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Gestión de Pagos</h1>
        <Button onClick={() => setIsModalOpen(true)} className="bg-blue-600 hover:bg-blue-700 text-white">
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Registrar Pago
        </Button>
      </div>

      {/* Filtros */}
      <div className="bg-white border border-gray-200 p-4 mb-6 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Filtros</h2>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label className="block text-gray-700 mb-1">Búsqueda</label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Buscar por factura o paciente"
                className="pl-10 border-gray-300"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-gray-700 mb-1">Estado</label>
            <Select
              value={estadoFilter}
              onChange={(e) => setEstadoFilter(e.target.value)}
              className="border-gray-300"
            >
              <option value="">Todos</option>
              <option value="PENDIENTE">Pendiente</option>
              <option value="COMPLETADO">Completado</option>
              <option value="FALLIDO">Fallido</option>
              <option value="REVERTIDO">Revertido</option>
            </Select>
          </div>
          
          <div>
            <label className="block text-gray-700 mb-1">Método de Pago</label>
            <Select
              value={metodoFilter}
              onChange={(e) => setMetodoFilter(e.target.value)}
              className="border-gray-300"
            >
              <option value="">Todos</option>
              <option value="EFECTIVO">Efectivo</option>
              <option value="TARJETA_CREDITO">Tarjeta de Crédito</option>
              <option value="TARJETA_DEBITO">Tarjeta de Débito</option>
              <option value="TRANSFERENCIA">Transferencia</option>
              <option value="CHEQUE">Cheque</option>
              <option value="PSE">PSE</option>
            </Select>
          </div>
          
          <div>
            <label className="block text-gray-700 mb-1">Fecha Inicio</label>
            <Input
              type="date"
              value={fechaInicio}
              onChange={(e) => setFechaInicio(e.target.value)}
              className="border-gray-300"
            />
          </div>
          
          <div>
            <label className="block text-gray-700 mb-1">Fecha Fin</label>
            <Input
              type="date"
              value={fechaFin}
              onChange={(e) => setFechaFin(e.target.value)}
              className="border-gray-300"
            />
          </div>
        </div>
      </div>

      {/* Lista de pagos */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
        {isLoading ? (
          <p className="text-gray-700 text-center py-4">Cargando pagos...</p>
        ) : filteredPagos.length > 0 ? (
          <table className="min-w-full table-auto">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Fecha
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Factura
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Paciente
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Monto
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Método
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredPagos.map((pago) => (
                <tr key={pago.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-gray-500" />
                    {formatDate(pago.fecha_pago)}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    <FontAwesomeIcon icon={faFileInvoice} className="mr-2 text-gray-500" />
                    {pago.factura?.numero_factura}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    <FontAwesomeIcon icon={faUser} className="mr-2 text-gray-500" />
                    {pago.factura?.paciente?.nombre_completo}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-medium">
                    <FontAwesomeIcon icon={faMoneyBillWave} className="mr-2 text-gray-500" />
                    {formatCurrency(pago.monto)}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    <FontAwesomeIcon icon={faCreditCard} className="mr-2 text-gray-500" />
                    {getMetodoPagoLabel(pago.metodo_pago)}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    <EstadoBadge estado={pago.estado} size="sm" />
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                    <div className="flex justify-end space-x-1">
                      {pago.estado === 'PENDIENTE' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedPago(pago);
                            setIsConfirmModalOpen(true);
                          }}
                          className="text-green-600 hover:text-green-700"
                        >
                          <FontAwesomeIcon icon={faCheck} />
                        </Button>
                      )}
                      {pago.estado === 'COMPLETADO' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => revertirPagoMutation.mutate(pago.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <FontAwesomeIcon icon={faTimes} />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-blue-600 hover:text-blue-700"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p className="text-gray-700 text-center py-4">
            No se encontraron pagos.
          </p>
        )}
      </div>

      {/* Modal para nuevo pago */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Registrar Nuevo Pago"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-gray-700 mb-1">Factura</label>
            <Select
              value={formData.factura_id}
              onChange={(e) => {
                const facturaId = e.target.value;
                const factura = facturasPendientes?.find(f => f.id === facturaId);
                setFormData({
                  ...formData, 
                  factura_id: facturaId,
                  monto: factura?.monto_total || 0
                });
              }}
              required
              className="border-gray-300"
            >
              <option value="">Seleccionar factura</option>
              {facturasPendientes?.map(factura => (
                <option key={factura.id} value={factura.id}>
                  {factura.numero_factura} - {factura.paciente} - {formatCurrency(factura.monto_total)}
                </option>
              ))}
            </Select>
          </div>

          <div>
            <label className="block text-gray-700 mb-1">Monto</label>
            <Input
              type="number"
              min="0"
              step="0.01"
              value={formData.monto}
              onChange={(e) => setFormData({...formData, monto: parseFloat(e.target.value) || 0})}
              required
              className="border-gray-300"
            />
          </div>

          <div>
            <label className="block text-gray-700 mb-1">Método de Pago</label>
            <Select
              value={formData.metodo_pago}
              onChange={(e) => setFormData({...formData, metodo_pago: e.target.value as any})}
              required
              className="border-gray-300"
            >
              <option value="EFECTIVO">Efectivo</option>
              <option value="TARJETA_CREDITO">Tarjeta de Crédito</option>
              <option value="TARJETA_DEBITO">Tarjeta de Débito</option>
              <option value="TRANSFERENCIA">Transferencia</option>
              <option value="CHEQUE">Cheque</option>
              <option value="PSE">PSE</option>
            </Select>
          </div>

          {formData.metodo_pago !== 'EFECTIVO' && (
            <div>
              <label className="block text-gray-700 mb-1">Referencia de Pago</label>
              <Input
                type="text"
                value={formData.referencia_pago}
                onChange={(e) => setFormData({...formData, referencia_pago: e.target.value})}
                placeholder="Número de transacción, cheque, etc."
                className="border-gray-300"
              />
            </div>
          )}

          <div>
            <label className="block text-gray-700 mb-1">Observaciones</label>
            <textarea
              className="w-full p-2 rounded border border-gray-300 text-gray-900"
              rows={3}
              value={formData.observaciones}
              onChange={(e) => setFormData({...formData, observaciones: e.target.value})}
              placeholder="Observaciones adicionales (opcional)"
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsModalOpen(false)}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={createPagoMutation.isPending}>
              {createPagoMutation.isPending ? 'Guardando...' : 'Registrar Pago'}
            </Button>
          </div>
        </form>
      </Modal>

      {/* Modal de confirmación */}
      <ModalConfirmacion
        isOpen={isConfirmModalOpen}
        onClose={() => setIsConfirmModalOpen(false)}
        onConfirm={() => selectedPago && confirmarPagoMutation.mutate(selectedPago.id)}
        tipo="confirmar"
        titulo="Confirmar Pago"
        mensaje={`¿Está seguro de confirmar el pago de ${selectedPago ? formatCurrency(selectedPago.monto) : ''} para la factura ${selectedPago?.factura?.numero_factura}?`}
        isLoading={confirmarPagoMutation.isPending}
      />
    </div>
  );
};

export default GestorPagos;
