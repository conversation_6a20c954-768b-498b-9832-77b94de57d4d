import React, { useState } from 'react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { Card } from '../../../components/ui/Card';
import { Button } from '../../../components/ui/Button';
import { DiagnosticoSelector } from '../../consultas/components/DiagnosticoSelector';
import { DiagnosticoBusquedaSelector, DiagnosticoCIE } from '../../pacientes/components/DiagnosticoBusquedaSelector';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExclamationTriangle, faInfoCircle, faStethoscope } from '@fortawesome/free-solid-svg-icons';
import { notificationService } from '../../../services/notificationService';

interface Diagnostico {
  id: string;
  codigo: string;
  descripcion: string;
  version: 'CIE-10' | 'CIE-11';
  tipo: 'PRINCIPAL' | 'SECUNDARIO';
  fecha_diagnostico: string;
  fecha_fin?: string;
  estado: 'ACTIVO' | 'RESUELTO' | 'CRONICO';
  profesional_id?: string;
  profesional_nombre?: string;
  notas?: string;
}

interface DiagnosticosTabProps {
  diagnosticos: Diagnostico[];
  onUpdate: (diagnosticos: Diagnostico[]) => void;
}

export const DiagnosticosTab: React.FC<DiagnosticosTabProps> = ({ diagnosticos, onUpdate }) => {
  const [filtroEstado, setFiltroEstado] = useState<string | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [diagnosticoEdicion, setDiagnosticoEdicion] = useState<Diagnostico | null>(null);
  
  // Nuevo diagnóstico
  const [nuevoDiagnostico, setNuevoDiagnostico] = useState<Partial<Diagnostico>>({
    codigo: '',
    descripcion: '',
    version: 'CIE-11',
    tipo: 'PRINCIPAL',
    fecha_diagnostico: new Date().toISOString().split('T')[0],
    estado: 'ACTIVO',
    profesional_nombre: '',
    notas: ''
  });

  // Estado para manejo de diagnóstico seleccionado
  const [diagnosticoSeleccionado, setDiagnosticoSeleccionado] = useState<DiagnosticoCIE | null>(null);
  
  // Filtrar diagnósticos por estado
  const diagnosticosFiltrados = filtroEstado 
    ? diagnosticos.filter(d => d.estado === filtroEstado) 
    : diagnosticos;
  
  // Ordenar diagnósticos por fecha, más reciente primero
  const diagnosticosOrdenados = [...diagnosticosFiltrados].sort(
    (a, b) => new Date(b.fecha_diagnostico).getTime() - new Date(a.fecha_diagnostico).getTime()
  );

  // Manejar selección de diagnóstico
  const handleSelectDiagnostico = (diagnostico: DiagnosticoCIE) => {
    setDiagnosticoSeleccionado(diagnostico);
    setNuevoDiagnostico(prev => ({
      ...prev,
      codigo: diagnostico.codigo,
      descripcion: diagnostico.descripcion,
      version: diagnostico.version
    }));

    // Mostrar notificación según la versión
    if (diagnostico.version === 'CIE-11') {
      notificationService.success(
        `Diagnóstico CIE-11 seleccionado: ${diagnostico.codigo}`,
        { duration: 3000 }
      );
    } else {
      notificationService.warning(
        `Código CIE-10 seleccionado: ${diagnostico.codigo}. Se recomienda migrar a CIE-11.`,
        { duration: 5000 }
      );
    }
  };
  
  // Función para añadir un nuevo diagnóstico
  const handleAddDiagnostico = () => {
    if (!nuevoDiagnostico.codigo || !nuevoDiagnostico.descripcion) {
      alert('Por favor, complete los campos obligatorios');
      return;
    }
    
    const updatedDiagnosticos = [...diagnosticos];
    
    if (diagnosticoEdicion) {
      // Editar diagnóstico existente
      const index = updatedDiagnosticos.findIndex(d => d.id === diagnosticoEdicion.id);
      if (index !== -1) {
        updatedDiagnosticos[index] = {
          ...diagnosticoEdicion,
          ...nuevoDiagnostico,
          id: diagnosticoEdicion.id
        } as Diagnostico;
      }
    } else {
      // Agregar nuevo diagnóstico
      updatedDiagnosticos.push({
        ...nuevoDiagnostico,
        id: `diag-${Date.now()}`
      } as Diagnostico);
    }
    
    onUpdate(updatedDiagnosticos);
    setShowModal(false);
    setDiagnosticoEdicion(null);
    setNuevoDiagnostico({
      codigo: '',
      descripcion: '',
      version: 'CIE-11',
      tipo: 'PRINCIPAL',
      fecha_diagnostico: new Date().toISOString().split('T')[0],
      estado: 'ACTIVO',
      profesional_nombre: '',
      notas: ''
    });
    setDiagnosticoSeleccionado(null);
  };
  
  // Editar diagnóstico
  const editarDiagnostico = (diagnostico: Diagnostico) => {
    setDiagnosticoEdicion(diagnostico);
    setNuevoDiagnostico({ ...diagnostico });
    setShowModal(true);
  };
  
  // Cambiar estado de diagnóstico
  const cambiarEstadoDiagnostico = (id: string, nuevoEstado: 'ACTIVO' | 'RESUELTO' | 'CRONICO') => {
    const updatedDiagnosticos = [...diagnosticos];
    const index = updatedDiagnosticos.findIndex(d => d.id === id);
    
    if (index !== -1) {
      updatedDiagnosticos[index] = {
        ...updatedDiagnosticos[index],
        estado: nuevoEstado,
        fecha_fin: nuevoEstado === 'RESUELTO' ? new Date().toISOString().split('T')[0] : undefined
      };
      
      onUpdate(updatedDiagnosticos);
    }
  };
  
  // Eliminar diagnóstico
  const eliminarDiagnostico = (id: string) => {
    if (confirm('¿Está seguro de eliminar este diagnóstico?')) {
      const updatedDiagnosticos = diagnosticos.filter(d => d.id !== id);
      onUpdate(updatedDiagnosticos);
    }
  };
  
  return (
    <div className="space-y-6">
      {/* Modal para agregar/editar diagnóstico */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-lg w-full p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <FontAwesomeIcon icon={faStethoscope} className="mr-2 text-blue-600" />
              {diagnosticoEdicion ? 'Editar Diagnóstico' : 'Nuevo Diagnóstico'}
            </h3>

            {/* Mensaje de transición CIE */}
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-start">
                <FontAwesomeIcon icon={faInfoCircle} className="text-blue-600 mr-2 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <p className="font-medium">Transición CIE-10 → CIE-11</p>
                  <p>El sistema está en proceso de transición. Se recomienda usar códigos CIE-11 para nuevos diagnósticos en historias clínicas.</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              {/* Selector de diagnóstico con transición */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Diagnóstico (CIE-10/CIE-11) *
                </label>
                <DiagnosticoBusquedaSelector
                  version="AMBAS"
                  onSelectDiagnostico={handleSelectDiagnostico}
                  placeholder="Buscar diagnóstico..."
                  label=""
                  mostrarTransicion={true}
                />

                {/* Mostrar diagnóstico seleccionado */}
                {diagnosticoSeleccionado && (
                  <div className="mt-2 p-3 bg-gray-50 border border-gray-200 rounded-md">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className="font-mono text-blue-600 mr-2">
                          {diagnosticoSeleccionado.codigo}
                        </span>
                        <span className={`px-2 py-1 text-xs rounded mr-2 ${
                          diagnosticoSeleccionado.version === 'CIE-11'
                            ? 'bg-green-100 text-green-800 border border-green-200'
                            : 'bg-yellow-100 text-yellow-800 border border-yellow-200'
                        }`}>
                          {diagnosticoSeleccionado.version}
                        </span>
                      </div>
                      {diagnosticoSeleccionado.version === 'CIE-10' && (
                        <span className="text-xs text-yellow-600">
                          <FontAwesomeIcon icon={faExclamationTriangle} className="mr-1" />
                          En transición
                        </span>
                      )}
                    </div>
                    <p className="text-gray-700 text-sm mt-1">{diagnosticoSeleccionado.descripcion}</p>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tipo</label>
                  <select
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    value={nuevoDiagnostico.tipo}
                    onChange={(e) => setNuevoDiagnostico({ ...nuevoDiagnostico, tipo: e.target.value as any })}
                  >
                    <option value="PRINCIPAL">Principal</option>
                    <option value="SECUNDARIO">Secundario</option>
                  </select>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Fecha del diagnóstico *</label>
                  <input 
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    value={nuevoDiagnostico.fecha_diagnostico}
                    onChange={(e) => setNuevoDiagnostico({ ...nuevoDiagnostico, fecha_diagnostico: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Estado</label>
                  <select 
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    value={nuevoDiagnostico.estado}
                    onChange={(e) => setNuevoDiagnostico({ ...nuevoDiagnostico, estado: e.target.value as any })}
                  >
                    <option value="ACTIVO">Activo</option>
                    <option value="RESUELTO">Resuelto</option>
                    <option value="CRONICO">Crónico</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Profesional</label>
                <input 
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  value={nuevoDiagnostico.profesional_nombre}
                  onChange={(e) => setNuevoDiagnostico({ ...nuevoDiagnostico, profesional_nombre: e.target.value })}
                  placeholder="Dr. Carlos Rodríguez"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Notas clínicas</label>
                <textarea 
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  rows={3}
                  value={nuevoDiagnostico.notas}
                  onChange={(e) => setNuevoDiagnostico({ ...nuevoDiagnostico, notas: e.target.value })}
                  placeholder="Detalles relevantes sobre el diagnóstico..."
                ></textarea>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 mt-6">
              <button 
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                onClick={() => {
                  setShowModal(false);
                  setDiagnosticoEdicion(null);
                }}
              >
                Cancelar
              </button>
              <button 
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                onClick={handleAddDiagnostico}
              >
                {diagnosticoEdicion ? 'Actualizar' : 'Agregar'}
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Diagnósticos */}
      <Card className="p-4 bg-white bg-opacity-80 backdrop-filter backdrop-blur-md shadow-lg rounded-lg">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-800">Diagnósticos</h3>
          <Button 
            onClick={() => {
              setDiagnosticoEdicion(null);
              setNuevoDiagnostico({
                codigo: '',
                descripcion: '',
                version: 'CIE-11',
                tipo: 'PRINCIPAL',
                fecha_diagnostico: new Date().toISOString().split('T')[0],
                estado: 'ACTIVO',
                profesional_nombre: '',
                notas: ''
              });
              setDiagnosticoSeleccionado(null);
              setShowModal(true);
            }}
            className="bg-blue-600 text-white hover:bg-blue-700"
          >
            + Nuevo Diagnóstico
          </Button>
        </div>
        
        {/* Filtros */}
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            onClick={() => setFiltroEstado(null)}
            className={`px-3 py-1 rounded-full text-sm ${!filtroEstado ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
          >
            Todos
          </button>
          <button
            onClick={() => setFiltroEstado('ACTIVO')}
            className={`px-3 py-1 rounded-full text-sm ${filtroEstado === 'ACTIVO' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
          >
            Activos
          </button>
          <button
            onClick={() => setFiltroEstado('RESUELTO')}
            className={`px-3 py-1 rounded-full text-sm ${filtroEstado === 'RESUELTO' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
          >
            Resueltos
          </button>
          <button
            onClick={() => setFiltroEstado('CRONICO')}
            className={`px-3 py-1 rounded-full text-sm ${filtroEstado === 'CRONICO' ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
          >
            Crónicos
          </button>
        </div>
        
        {/* Lista de Diagnósticos */}
        {diagnosticosOrdenados.length > 0 ? (
          <div className="space-y-3">
            {diagnosticosOrdenados.map((diagnostico) => (
              <div 
                key={diagnostico.id} 
                className={`p-3 rounded-md ${
                  diagnostico.estado === 'ACTIVO' ? 'bg-blue-50' : 
                  diagnostico.estado === 'RESUELTO' ? 'bg-green-50' : 
                  'bg-amber-50'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs px-2 py-0.5 bg-white bg-opacity-60 rounded-full font-medium">
                        {diagnostico.codigo}
                      </span>
                      <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${
                        (diagnostico.version || (diagnostico.codigo.length > 6 ? 'CIE-11' : 'CIE-10')) === 'CIE-11'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {diagnostico.version || (diagnostico.codigo.length > 6 ? 'CIE-11' : 'CIE-10')}
                      </span>
                      <h4 className="font-semibold">{diagnostico.descripcion}</h4>
                      {diagnostico.tipo === 'PRINCIPAL' && (
                        <span className="text-xs px-2 py-0.5 bg-blue-100 text-blue-800 rounded-full font-medium">
                          Principal
                        </span>
                      )}
                      {(diagnostico.version || (diagnostico.codigo.length <= 6 ? 'CIE-10' : 'CIE-11')) === 'CIE-10' && (
                        <span className="text-xs text-yellow-600">
                          <FontAwesomeIcon icon={faExclamationTriangle} className="mr-1" />
                          En transición
                        </span>
                      )}
                    </div>
                    <div className="mt-1 flex flex-wrap gap-x-4 text-xs text-gray-600">
                      <p>Fecha: {format(new Date(diagnostico.fecha_diagnostico), 'dd/MM/yyyy', { locale: es })}</p>
                      {diagnostico.fecha_fin && (
                        <p>Resuelto: {format(new Date(diagnostico.fecha_fin), 'dd/MM/yyyy', { locale: es })}</p>
                      )}
                      {diagnostico.profesional_nombre && (
                        <p>Por: {diagnostico.profesional_nombre}</p>
                      )}
                      <p className={`font-medium ${
                        diagnostico.estado === 'ACTIVO' ? 'text-blue-700' :
                        diagnostico.estado === 'RESUELTO' ? 'text-green-700' :
                        'text-amber-700'
                      }`}>
                        {diagnostico.estado}
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    {/* Botón de cambio de estado */}
                    <div className="relative group">
                      <button className="text-gray-600 hover:text-blue-600 border border-gray-300 rounded-md p-1">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>
                      <div className="absolute right-0 mt-1 hidden group-hover:block z-10 bg-white shadow-md rounded-md py-1 min-w-[120px]">
                        {diagnostico.estado !== 'ACTIVO' && (
                          <button 
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => cambiarEstadoDiagnostico(diagnostico.id, 'ACTIVO')}
                          >
                            Marcar como Activo
                          </button>
                        )}
                        {diagnostico.estado !== 'RESUELTO' && (
                          <button 
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => cambiarEstadoDiagnostico(diagnostico.id, 'RESUELTO')}
                          >
                            Marcar como Resuelto
                          </button>
                        )}
                        {diagnostico.estado !== 'CRONICO' && (
                          <button 
                            className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                            onClick={() => cambiarEstadoDiagnostico(diagnostico.id, 'CRONICO')}
                          >
                            Marcar como Crónico
                          </button>
                        )}
                      </div>
                    </div>
                    
                    {/* Botón de edición */}
                    <button 
                      className="text-gray-600 hover:text-blue-600"
                      onClick={() => editarDiagnostico(diagnostico)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                      </svg>
                    </button>
                    
                    {/* Botón de eliminación */}
                    <button 
                      className="text-gray-600 hover:text-red-600"
                      onClick={() => eliminarDiagnostico(diagnostico.id)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>
                {diagnostico.notas && (
                  <div className="mt-2 bg-white bg-opacity-50 p-2 rounded">
                    <p className="text-sm">{diagnostico.notas}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <p className="text-center text-gray-500 py-4">No hay diagnósticos registrados.</p>
        )}
      </Card>
    </div>
  );
};
