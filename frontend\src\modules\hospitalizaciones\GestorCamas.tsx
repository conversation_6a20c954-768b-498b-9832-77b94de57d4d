import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../context/AuthContext';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Select } from '../../components/ui/Select';
import { Modal } from '../../components/ui/Modal';
import { EstadoBadge } from '../../components/ui/EstadosBadges';
import { ModalConfirmacion, useModalConfirmacion } from '../../components/ui/ModalConfirmacion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faSearch,
  faBed,
  faHospital,
  faUser,
  faCalendarAlt,
  faEdit,
  faTrash,
  faEye,
  faUserMinus,
  faUserPlus,
  faExchangeAlt,
  faTools
} from '@fortawesome/free-solid-svg-icons';

// Tipos para camas
interface Cama {
  id: string;
  numero: string;
  servicio: string;
  piso: number;
  habitacion: string;
  estado: 'DISPONIBLE' | 'OCUPADA' | 'MANTENIMIENTO' | 'FUERA_SERVICIO';
  tipo_cama: 'GENERAL' | 'UCI' | 'PEDIATRIA' | 'MATERNIDAD' | 'CIRUGIA';
  observaciones?: string;
  hospital_id: number;
  // Datos de ocupación actual
  hospitalizacion_actual?: {
    id: string;
    paciente: {
      nombre_completo: string;
      numero_documento: string;
    };
    fecha_ingreso: string;
    motivo: string;
  };
}

interface FormCama {
  numero: string;
  servicio: string;
  piso: number;
  habitacion: string;
  tipo_cama: 'GENERAL' | 'UCI' | 'PEDIATRIA' | 'MATERNIDAD' | 'CIRUGIA';
  observaciones?: string;
}

const GestorCamas: React.FC = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const hospitalId = user?.hospital_id || 1;

  // Estados
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCama, setEditingCama] = useState<Cama | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [estadoFilter, setEstadoFilter] = useState<string>('');
  const [servicioFilter, setServicioFilter] = useState<string>('');
  const [pisoFilter, setPisoFilter] = useState<string>('');

  // Modal de confirmación
  const { mostrar: mostrarConfirmacion, ModalConfirmacion: ModalConfirmacionComponent } = useModalConfirmacion();

  // Formulario
  const [formData, setFormData] = useState<FormCama>({
    numero: '',
    servicio: '',
    piso: 1,
    habitacion: '',
    tipo_cama: 'GENERAL',
    observaciones: ''
  });

  // Query para obtener camas
  const { data: camas, isLoading } = useQuery<Cama[]>({
    queryKey: ['camas', hospitalId, estadoFilter, servicioFilter, pisoFilter],
    queryFn: async () => {
      // Simulación de datos - en producción sería una llamada a la API
      return [
        {
          id: '1',
          numero: '101',
          servicio: 'Medicina Interna',
          piso: 1,
          habitacion: '101A',
          estado: 'OCUPADA',
          tipo_cama: 'GENERAL',
          observaciones: 'Cama con monitor cardíaco',
          hospital_id: hospitalId,
          hospitalizacion_actual: {
            id: 'h1',
            paciente: {
              nombre_completo: 'Juan Pérez García',
              numero_documento: '12345678'
            },
            fecha_ingreso: '2024-01-15T08:00:00Z',
            motivo: 'Neumonía'
          }
        },
        {
          id: '2',
          numero: '102',
          servicio: 'Medicina Interna',
          piso: 1,
          habitacion: '101B',
          estado: 'DISPONIBLE',
          tipo_cama: 'GENERAL',
          hospital_id: hospitalId
        },
        {
          id: '3',
          numero: '201',
          servicio: 'UCI',
          piso: 2,
          habitacion: '201A',
          estado: 'OCUPADA',
          tipo_cama: 'UCI',
          observaciones: 'Ventilador mecánico disponible',
          hospital_id: hospitalId,
          hospitalizacion_actual: {
            id: 'h2',
            paciente: {
              nombre_completo: 'María González López',
              numero_documento: '87654321'
            },
            fecha_ingreso: '2024-01-14T15:30:00Z',
            motivo: 'Post-operatorio cirugía cardíaca'
          }
        },
        {
          id: '4',
          numero: '301',
          servicio: 'Pediatría',
          piso: 3,
          habitacion: '301A',
          estado: 'MANTENIMIENTO',
          tipo_cama: 'PEDIATRIA',
          observaciones: 'Reparación de sistema eléctrico',
          hospital_id: hospitalId
        },
        {
          id: '5',
          numero: '401',
          servicio: 'Maternidad',
          piso: 4,
          habitacion: '401A',
          estado: 'DISPONIBLE',
          tipo_cama: 'MATERNIDAD',
          hospital_id: hospitalId
        }
      ];
    }
  });

  // Query para obtener servicios disponibles
  const { data: servicios } = useQuery({
    queryKey: ['servicios-hospital', hospitalId],
    queryFn: async () => {
      return [
        'Medicina Interna',
        'UCI',
        'Pediatría',
        'Maternidad',
        'Cirugía',
        'Urgencias',
        'Cardiología',
        'Neurología'
      ];
    }
  });

  // Mutación para crear/actualizar cama
  const saveCamaMutation = useMutation({
    mutationFn: async (data: FormCama) => {
      console.log(editingCama ? 'Actualizando cama:' : 'Creando cama:', data);
      return { id: editingCama?.id || Date.now().toString(), ...data };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['camas'] });
      setIsModalOpen(false);
      setEditingCama(null);
      resetForm();
    }
  });

  // Mutación para cambiar estado de cama
  const cambiarEstadoCamaMutation = useMutation({
    mutationFn: async ({ id, estado }: { id: string; estado: string }) => {
      console.log('Cambiando estado de cama:', id, 'a', estado);
      return { id, estado };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['camas'] });
    }
  });

  // Mutación para eliminar cama
  const deleteCamaMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log('Eliminando cama:', id);
      return { id };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['camas'] });
    }
  });

  // Filtrar camas
  const filteredCamas = camas?.filter(cama => {
    const matchesSearch = 
      cama.numero.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cama.servicio.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cama.habitacion.toLowerCase().includes(searchTerm.toLowerCase()) ||
      cama.hospitalizacion_actual?.paciente.nombre_completo.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesEstado = estadoFilter ? cama.estado === estadoFilter : true;
    const matchesServicio = servicioFilter ? cama.servicio === servicioFilter : true;
    const matchesPiso = pisoFilter ? cama.piso.toString() === pisoFilter : true;

    return matchesSearch && matchesEstado && matchesServicio && matchesPiso;
  }) || [];

  // Manejar envío del formulario
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.numero && formData.servicio && formData.habitacion) {
      saveCamaMutation.mutate(formData);
    }
  };

  // Resetear formulario
  const resetForm = () => {
    setFormData({
      numero: '',
      servicio: '',
      piso: 1,
      habitacion: '',
      tipo_cama: 'GENERAL',
      observaciones: ''
    });
  };

  // Abrir modal para editar
  const handleEdit = (cama: Cama) => {
    setEditingCama(cama);
    setFormData({
      numero: cama.numero,
      servicio: cama.servicio,
      piso: cama.piso,
      habitacion: cama.habitacion,
      tipo_cama: cama.tipo_cama,
      observaciones: cama.observaciones || ''
    });
    setIsModalOpen(true);
  };

  // Abrir modal para nueva cama
  const handleNew = () => {
    setEditingCama(null);
    resetForm();
    setIsModalOpen(true);
  };

  // Manejar eliminación
  const handleDelete = (cama: Cama) => {
    if (cama.estado === 'OCUPADA') {
      mostrarConfirmacion({
        tipo: 'advertencia',
        titulo: 'Cama Ocupada',
        mensaje: 'No se puede eliminar una cama que está ocupada. Primero debe dar de alta al paciente.',
        textoConfirmar: 'Entendido'
      });
      return;
    }

    mostrarConfirmacion({
      tipo: 'eliminar',
      titulo: 'Eliminar Cama',
      mensaje: `¿Está seguro de eliminar la cama ${cama.numero} del servicio ${cama.servicio}?`,
      onConfirm: () => {
        deleteCamaMutation.mutate(cama.id);
      }
    });
  };

  // Manejar cambio de estado
  const handleCambiarEstado = (cama: Cama, nuevoEstado: string) => {
    const mensajes = {
      'DISPONIBLE': 'disponible',
      'MANTENIMIENTO': 'en mantenimiento',
      'FUERA_SERVICIO': 'fuera de servicio'
    };

    mostrarConfirmacion({
      tipo: 'confirmar',
      titulo: 'Cambiar Estado de Cama',
      mensaje: `¿Está seguro de cambiar el estado de la cama ${cama.numero} a ${mensajes[nuevoEstado as keyof typeof mensajes]}?`,
      onConfirm: () => {
        cambiarEstadoCamaMutation.mutate({ id: cama.id, estado: nuevoEstado });
      }
    });
  };

  // Formatear fecha
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('es-ES', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Calcular días de hospitalización
  const calcularDiasHospitalizacion = (fechaIngreso: string) => {
    const fecha = new Date(fechaIngreso);
    const hoy = new Date();
    const diferencia = hoy.getTime() - fecha.getTime();
    return Math.floor(diferencia / (1000 * 60 * 60 * 24));
  };

  // Obtener estadísticas de camas
  const estadisticas = {
    total: camas?.length || 0,
    disponibles: camas?.filter(c => c.estado === 'DISPONIBLE').length || 0,
    ocupadas: camas?.filter(c => c.estado === 'OCUPADA').length || 0,
    mantenimiento: camas?.filter(c => c.estado === 'MANTENIMIENTO').length || 0,
    fueraServicio: camas?.filter(c => c.estado === 'FUERA_SERVICIO').length || 0
  };

  const porcentajeOcupacion = estadisticas.total > 0 
    ? Math.round((estadisticas.ocupadas / (estadisticas.total - estadisticas.fueraServicio - estadisticas.mantenimiento)) * 100)
    : 0;

  return (
    <div className="p-6 bg-white min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Gestión de Camas</h1>
        <Button
          onClick={handleNew}
          className="bg-blue-600 hover:bg-blue-700 text-white focus:ring-4 focus:ring-blue-300 focus:ring-offset-2 focus:outline-none focus:scale-105 transition-all duration-300 focus:shadow-xl focus:shadow-blue-500/50 focus:bg-blue-800"
        >
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Nueva Cama
        </Button>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faBed} className="text-blue-600 text-2xl mr-3" />
            <div>
              <p className="text-sm text-blue-600 font-medium">Total Camas</p>
              <p className="text-2xl font-bold text-blue-800">{estadisticas.total}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faUserMinus} className="text-green-600 text-2xl mr-3" />
            <div>
              <p className="text-sm text-green-600 font-medium">Disponibles</p>
              <p className="text-2xl font-bold text-green-800">{estadisticas.disponibles}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faUserPlus} className="text-red-600 text-2xl mr-3" />
            <div>
              <p className="text-sm text-red-600 font-medium">Ocupadas</p>
              <p className="text-2xl font-bold text-red-800">{estadisticas.ocupadas}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faTools} className="text-yellow-600 text-2xl mr-3" />
            <div>
              <p className="text-sm text-yellow-600 font-medium">Mantenimiento</p>
              <p className="text-2xl font-bold text-yellow-800">{estadisticas.mantenimiento}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faHospital} className="text-purple-600 text-2xl mr-3" />
            <div>
              <p className="text-sm text-purple-600 font-medium">Ocupación</p>
              <p className="text-2xl font-bold text-purple-800">{porcentajeOcupacion}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filtros */}
      <div className="bg-white border border-gray-200 p-4 mb-6 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Filtros</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-gray-700 mb-1">Búsqueda</label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Buscar por número, servicio o paciente"
                className="pl-10 border-gray-300"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-gray-700 mb-1">Estado</label>
            <Select
              value={estadoFilter}
              onChange={(e) => setEstadoFilter(e.target.value)}
              className="border-gray-300"
            >
              <option value="">Todos</option>
              <option value="DISPONIBLE">Disponible</option>
              <option value="OCUPADA">Ocupada</option>
              <option value="MANTENIMIENTO">Mantenimiento</option>
              <option value="FUERA_SERVICIO">Fuera de Servicio</option>
            </Select>
          </div>
          
          <div>
            <label className="block text-gray-700 mb-1">Servicio</label>
            <Select
              value={servicioFilter}
              onChange={(e) => setServicioFilter(e.target.value)}
              className="border-gray-300"
            >
              <option value="">Todos</option>
              {servicios?.map(servicio => (
                <option key={servicio} value={servicio}>{servicio}</option>
              ))}
            </Select>
          </div>
          
          <div>
            <label className="block text-gray-700 mb-1">Piso</label>
            <Select
              value={pisoFilter}
              onChange={(e) => setPisoFilter(e.target.value)}
              className="border-gray-300"
            >
              <option value="">Todos</option>
              <option value="1">Piso 1</option>
              <option value="2">Piso 2</option>
              <option value="3">Piso 3</option>
              <option value="4">Piso 4</option>
              <option value="5">Piso 5</option>
            </Select>
          </div>
        </div>
      </div>

      {/* Lista de camas */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
        {isLoading ? (
          <p className="text-gray-700 text-center py-4">Cargando camas...</p>
        ) : filteredCamas.length > 0 ? (
          <table className="min-w-full table-auto">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Cama
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Servicio
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Ubicación
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Paciente Actual
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Días Hosp.
                </th>
                <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredCamas.map((cama) => (
                <tr key={cama.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faBed} className="mr-2 text-gray-500" />
                      <div>
                        <div className="font-medium">{cama.numero}</div>
                        <div className="text-gray-500 text-xs">{cama.tipo_cama}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    {cama.servicio}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div>Piso {cama.piso}</div>
                      <div className="text-gray-500 text-xs">{cama.habitacion}</div>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    <EstadoBadge estado={cama.estado} size="sm" />
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    {cama.hospitalizacion_actual ? (
                      <div>
                        <div className="font-medium">{cama.hospitalizacion_actual.paciente.nombre_completo}</div>
                        <div className="text-gray-500 text-xs">{cama.hospitalizacion_actual.paciente.numero_documento}</div>
                        <div className="text-gray-500 text-xs">{cama.hospitalizacion_actual.motivo}</div>
                      </div>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    {cama.hospitalizacion_actual ? (
                      <span className="font-medium">
                        {calcularDiasHospitalizacion(cama.hospitalizacion_actual.fecha_ingreso)} días
                      </span>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                    <div className="flex justify-end space-x-1">
                      {cama.estado === 'DISPONIBLE' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCambiarEstado(cama, 'MANTENIMIENTO')}
                          className="text-yellow-700 hover:text-yellow-800 hover:bg-yellow-50 border border-yellow-200 bg-yellow-50/50 focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 focus:outline-none focus:scale-110 transition-all duration-200 focus:shadow-lg focus:shadow-yellow-500/25"
                          title="Poner en mantenimiento"
                        >
                          <FontAwesomeIcon icon={faTools} className="text-yellow-700" />
                        </Button>
                      )}
                      {cama.estado === 'MANTENIMIENTO' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleCambiarEstado(cama, 'DISPONIBLE')}
                          className="text-green-700 hover:text-green-800 hover:bg-green-50 border border-green-200 bg-green-50/50 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:outline-none focus:scale-110 transition-all duration-200 focus:shadow-lg focus:shadow-green-500/25"
                          title="Marcar como disponible"
                        >
                          <FontAwesomeIcon icon={faUserMinus} className="text-green-700" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEdit(cama)}
                        className="text-blue-700 hover:text-blue-800 hover:bg-blue-50 border border-blue-200 bg-blue-50/50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none focus:scale-110 transition-all duration-200 focus:shadow-lg focus:shadow-blue-500/25"
                        title="Editar cama"
                      >
                        <FontAwesomeIcon icon={faEdit} className="text-blue-700" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(cama)}
                        className="text-red-700 hover:text-red-800 hover:bg-red-50 border border-red-200 bg-red-50/50 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-none focus:scale-110 transition-all duration-200 focus:shadow-lg focus:shadow-red-500/25"
                        title="Eliminar cama"
                      >
                        <FontAwesomeIcon icon={faTrash} className="text-red-700" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p className="text-gray-700 text-center py-4">
            No se encontraron camas.
          </p>
        )}
      </div>

      {/* Modal para nueva/editar cama */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={editingCama ? 'Editar Cama' : 'Nueva Cama'}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-700 mb-1">Número de Cama</label>
              <Input
                type="text"
                value={formData.numero}
                onChange={(e) => setFormData({...formData, numero: e.target.value})}
                placeholder="Ej: 101, 201A"
                required
                className="border-gray-300"
              />
            </div>

            <div>
              <label className="block text-gray-700 mb-1">Tipo de Cama</label>
              <Select
                value={formData.tipo_cama}
                onChange={(e) => setFormData({...formData, tipo_cama: e.target.value as any})}
                required
                className="border-gray-300"
              >
                <option value="GENERAL">General</option>
                <option value="UCI">UCI</option>
                <option value="PEDIATRIA">Pediatría</option>
                <option value="MATERNIDAD">Maternidad</option>
                <option value="CIRUGIA">Cirugía</option>
              </Select>
            </div>
          </div>

          <div>
            <label className="block text-gray-700 mb-1">Servicio</label>
            <Select
              value={formData.servicio}
              onChange={(e) => setFormData({...formData, servicio: e.target.value})}
              required
              className="border-gray-300"
            >
              <option value="">Seleccionar servicio</option>
              {servicios?.map(servicio => (
                <option key={servicio} value={servicio}>{servicio}</option>
              ))}
            </Select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-700 mb-1">Piso</label>
              <Select
                value={formData.piso.toString()}
                onChange={(e) => setFormData({...formData, piso: parseInt(e.target.value)})}
                required
                className="border-gray-300"
              >
                <option value="1">Piso 1</option>
                <option value="2">Piso 2</option>
                <option value="3">Piso 3</option>
                <option value="4">Piso 4</option>
                <option value="5">Piso 5</option>
              </Select>
            </div>

            <div>
              <label className="block text-gray-700 mb-1">Habitación</label>
              <Input
                type="text"
                value={formData.habitacion}
                onChange={(e) => setFormData({...formData, habitacion: e.target.value})}
                placeholder="Ej: 101A, 201B"
                required
                className="border-gray-300"
              />
            </div>
          </div>

          <div>
            <label className="block text-gray-700 mb-1">Observaciones</label>
            <textarea
              className="w-full p-2 rounded border border-gray-300 text-gray-900"
              rows={3}
              value={formData.observaciones}
              onChange={(e) => setFormData({...formData, observaciones: e.target.value})}
              placeholder="Observaciones adicionales (opcional)"
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsModalOpen(false)}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={saveCamaMutation.isPending}>
              {saveCamaMutation.isPending ? 'Guardando...' : (editingCama ? 'Actualizar' : 'Crear')}
            </Button>
          </div>
        </form>
      </Modal>

      {/* Modal de confirmación */}
      <ModalConfirmacionComponent />
    </div>
  );
};

export default GestorCamas;
