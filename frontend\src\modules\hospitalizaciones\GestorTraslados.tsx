import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../context/AuthContext';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Select } from '../../components/ui/Select';
import { Modal } from '../../components/ui/Modal';
import { EstadoBadge } from '../../components/ui/EstadosBadges';
import { ModalConfirmacion, useModalConfirmacion } from '../../components/ui/ModalConfirmacion';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faSearch,
  faExchangeAlt,
  faUser,
  faCalendarAlt,
  faHospital,
  faBed,
  faCheck,
  faTimes,
  faEye,
  faFileAlt
} from '@fortawesome/free-solid-svg-icons';

// Tipos para traslados
interface Traslado {
  id: string;
  hospitalizacion_id: string;
  cama_origen_id: string;
  cama_destino_id: string;
  motivo: string;
  estado: 'PENDIENTE' | 'APROBADO' | 'EJECUTADO' | 'CANCELADO';
  fecha_solicitud: string;
  fecha_aprobacion?: string;
  fecha_ejecucion?: string;
  usuario_solicita_id: string;
  usuario_aprueba_id?: string;
  usuario_ejecuta_id?: string;
  observaciones?: string;
  hospital_id: number;
  // Datos relacionados
  hospitalizacion?: {
    paciente: {
      nombre_completo: string;
      numero_documento: string;
    };
    motivo_hospitalizacion: string;
  };
  cama_origen?: {
    numero: string;
    servicio: string;
    habitacion: string;
  };
  cama_destino?: {
    numero: string;
    servicio: string;
    habitacion: string;
  };
  usuario_solicita?: {
    nombre: string;
  };
  usuario_aprueba?: {
    nombre: string;
  };
  usuario_ejecuta?: {
    nombre: string;
  };
}

interface FormTraslado {
  hospitalizacion_id: string;
  cama_destino_id: string;
  motivo: string;
  observaciones?: string;
}

const GestorTraslados: React.FC = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const hospitalId = user?.hospital_id || 1;

  // Estados
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [estadoFilter, setEstadoFilter] = useState<string>('');
  const [servicioFilter, setServicioFilter] = useState<string>('');

  // Modal de confirmación
  const { mostrar: mostrarConfirmacion, ModalConfirmacion: ModalConfirmacionComponent } = useModalConfirmacion();

  // Formulario
  const [formData, setFormData] = useState<FormTraslado>({
    hospitalizacion_id: '',
    cama_destino_id: '',
    motivo: '',
    observaciones: ''
  });

  // Query para obtener traslados
  const { data: traslados, isLoading } = useQuery<Traslado[]>({
    queryKey: ['traslados', hospitalId, estadoFilter, servicioFilter],
    queryFn: async () => {
      // Simulación de datos - en producción sería una llamada a la API
      return [
        {
          id: '1',
          hospitalizacion_id: 'h1',
          cama_origen_id: 'c1',
          cama_destino_id: 'c2',
          motivo: 'Mejoría clínica, traslado a cuidados generales',
          estado: 'PENDIENTE',
          fecha_solicitud: '2024-01-16T10:30:00Z',
          usuario_solicita_id: 'u1',
          hospital_id: hospitalId,
          hospitalizacion: {
            paciente: {
              nombre_completo: 'Juan Pérez García',
              numero_documento: '12345678'
            },
            motivo_hospitalizacion: 'Neumonía'
          },
          cama_origen: {
            numero: '201',
            servicio: 'UCI',
            habitacion: '201A'
          },
          cama_destino: {
            numero: '101',
            servicio: 'Medicina Interna',
            habitacion: '101A'
          },
          usuario_solicita: {
            nombre: 'Dr. Carlos López'
          }
        },
        {
          id: '2',
          hospitalizacion_id: 'h2',
          cama_origen_id: 'c3',
          cama_destino_id: 'c4',
          motivo: 'Complicaciones post-operatorias, requiere UCI',
          estado: 'APROBADO',
          fecha_solicitud: '2024-01-15T14:20:00Z',
          fecha_aprobacion: '2024-01-15T15:00:00Z',
          usuario_solicita_id: 'u2',
          usuario_aprueba_id: 'u3',
          hospital_id: hospitalId,
          hospitalizacion: {
            paciente: {
              nombre_completo: 'María González López',
              numero_documento: '87654321'
            },
            motivo_hospitalizacion: 'Post-operatorio'
          },
          cama_origen: {
            numero: '301',
            servicio: 'Cirugía',
            habitacion: '301A'
          },
          cama_destino: {
            numero: '202',
            servicio: 'UCI',
            habitacion: '202A'
          },
          usuario_solicita: {
            nombre: 'Dr. Ana Martínez'
          },
          usuario_aprueba: {
            nombre: 'Jefe UCI'
          }
        },
        {
          id: '3',
          hospitalizacion_id: 'h3',
          cama_origen_id: 'c5',
          cama_destino_id: 'c6',
          motivo: 'Traslado por disponibilidad de habitación',
          estado: 'EJECUTADO',
          fecha_solicitud: '2024-01-14T09:15:00Z',
          fecha_aprobacion: '2024-01-14T09:30:00Z',
          fecha_ejecucion: '2024-01-14T11:00:00Z',
          usuario_solicita_id: 'u4',
          usuario_aprueba_id: 'u5',
          usuario_ejecuta_id: 'u6',
          hospital_id: hospitalId,
          hospitalizacion: {
            paciente: {
              nombre_completo: 'Carlos Rodríguez Martín',
              numero_documento: '11223344'
            },
            motivo_hospitalizacion: 'Observación'
          },
          cama_origen: {
            numero: '102',
            servicio: 'Medicina Interna',
            habitacion: '102A'
          },
          cama_destino: {
            numero: '103',
            servicio: 'Medicina Interna',
            habitacion: '103A'
          },
          usuario_solicita: {
            nombre: 'Enf. Laura Pérez'
          },
          usuario_aprueba: {
            nombre: 'Jefe Enfermería'
          },
          usuario_ejecuta: {
            nombre: 'Aux. Traslados'
          }
        }
      ];
    }
  });

  // Query para obtener hospitalizaciones activas
  const { data: hospitalizacionesActivas } = useQuery({
    queryKey: ['hospitalizaciones-activas', hospitalId],
    queryFn: async () => {
      return [
        {
          id: 'h1',
          paciente: 'Juan Pérez García - 12345678',
          cama_actual: 'Cama 201 - UCI',
          motivo: 'Neumonía'
        },
        {
          id: 'h2',
          paciente: 'María González López - 87654321',
          cama_actual: 'Cama 301 - Cirugía',
          motivo: 'Post-operatorio'
        },
        {
          id: 'h3',
          paciente: 'Carlos Rodríguez Martín - 11223344',
          cama_actual: 'Cama 102 - Medicina Interna',
          motivo: 'Observación'
        }
      ];
    }
  });

  // Query para obtener camas disponibles
  const { data: camasDisponibles } = useQuery({
    queryKey: ['camas-disponibles', hospitalId],
    queryFn: async () => {
      return [
        { id: 'c1', numero: '101', servicio: 'Medicina Interna', habitacion: '101A' },
        { id: 'c2', numero: '103', servicio: 'Medicina Interna', habitacion: '103A' },
        { id: 'c3', numero: '202', servicio: 'UCI', habitacion: '202A' },
        { id: 'c4', numero: '401', servicio: 'Pediatría', habitacion: '401A' }
      ];
    }
  });

  // Mutación para crear traslado
  const createTrasladoMutation = useMutation({
    mutationFn: async (data: FormTraslado) => {
      console.log('Creando traslado:', data);
      return { id: Date.now().toString(), ...data };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['traslados'] });
      setIsModalOpen(false);
      resetForm();
    }
  });

  // Mutación para aprobar traslado
  const aprobarTrasladoMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log('Aprobando traslado:', id);
      return { id };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['traslados'] });
    }
  });

  // Mutación para ejecutar traslado
  const ejecutarTrasladoMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log('Ejecutando traslado:', id);
      return { id };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['traslados'] });
      queryClient.invalidateQueries({ queryKey: ['camas'] });
      queryClient.invalidateQueries({ queryKey: ['hospitalizaciones'] });
    }
  });

  // Mutación para cancelar traslado
  const cancelarTrasladoMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log('Cancelando traslado:', id);
      return { id };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['traslados'] });
    }
  });

  // Filtrar traslados
  const filteredTraslados = traslados?.filter(traslado => {
    const matchesSearch = 
      traslado.hospitalizacion?.paciente.nombre_completo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      traslado.hospitalizacion?.paciente.numero_documento.includes(searchTerm) ||
      traslado.motivo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      traslado.cama_origen?.numero.includes(searchTerm) ||
      traslado.cama_destino?.numero.includes(searchTerm);

    const matchesEstado = estadoFilter ? traslado.estado === estadoFilter : true;

    const matchesServicio = servicioFilter ? 
      traslado.cama_origen?.servicio === servicioFilter || traslado.cama_destino?.servicio === servicioFilter : true;

    return matchesSearch && matchesEstado && matchesServicio;
  }) || [];

  // Manejar envío del formulario
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.hospitalizacion_id && formData.cama_destino_id && formData.motivo) {
      createTrasladoMutation.mutate(formData);
    }
  };

  // Resetear formulario
  const resetForm = () => {
    setFormData({
      hospitalizacion_id: '',
      cama_destino_id: '',
      motivo: '',
      observaciones: ''
    });
  };

  // Manejar aprobación
  const handleAprobar = (traslado: Traslado) => {
    mostrarConfirmacion({
      tipo: 'aprobar',
      titulo: 'Aprobar Traslado',
      mensaje: `¿Está seguro de aprobar el traslado del paciente ${traslado.hospitalizacion?.paciente.nombre_completo} desde ${traslado.cama_origen?.servicio} hacia ${traslado.cama_destino?.servicio}?`,
      onConfirm: () => {
        aprobarTrasladoMutation.mutate(traslado.id);
      }
    });
  };

  // Manejar ejecución
  const handleEjecutar = (traslado: Traslado) => {
    mostrarConfirmacion({
      tipo: 'confirmar',
      titulo: 'Ejecutar Traslado',
      mensaje: `¿Confirma que el traslado del paciente ${traslado.hospitalizacion?.paciente.nombre_completo} ha sido ejecutado exitosamente?`,
      onConfirm: () => {
        ejecutarTrasladoMutation.mutate(traslado.id);
      }
    });
  };

  // Manejar cancelación
  const handleCancelar = (traslado: Traslado) => {
    mostrarConfirmacion({
      tipo: 'anular',
      titulo: 'Cancelar Traslado',
      mensaje: `¿Está seguro de cancelar el traslado del paciente ${traslado.hospitalizacion?.paciente.nombre_completo}?`,
      requiereMotivo: true,
      placeholderMotivo: 'Ingrese el motivo de la cancelación',
      onConfirm: (motivo) => {
        console.log('Cancelando con motivo:', motivo);
        cancelarTrasladoMutation.mutate(traslado.id);
      }
    });
  };

  // Formatear fecha
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('es-ES', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Obtener servicios únicos
  const servicios = [...new Set([
    ...(traslados?.map(t => t.cama_origen?.servicio) || []),
    ...(traslados?.map(t => t.cama_destino?.servicio) || [])
  ])].filter(Boolean);

  return (
    <div className="p-6 bg-white min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Gestión de Traslados</h1>
        <Button onClick={() => setIsModalOpen(true)} className="bg-blue-600 hover:bg-blue-700 text-white">
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Nuevo Traslado
        </Button>
      </div>

      {/* Filtros */}
      <div className="bg-white border border-gray-200 p-4 mb-6 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Filtros</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-gray-700 mb-1">Búsqueda</label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Buscar por paciente, motivo o cama"
                className="pl-10 border-gray-300"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-gray-700 mb-1">Estado</label>
            <Select
              value={estadoFilter}
              onChange={(e) => setEstadoFilter(e.target.value)}
              className="border-gray-300"
            >
              <option value="">Todos</option>
              <option value="PENDIENTE">Pendiente</option>
              <option value="APROBADO">Aprobado</option>
              <option value="EJECUTADO">Ejecutado</option>
              <option value="CANCELADO">Cancelado</option>
            </Select>
          </div>
          
          <div>
            <label className="block text-gray-700 mb-1">Servicio</label>
            <Select
              value={servicioFilter}
              onChange={(e) => setServicioFilter(e.target.value)}
              className="border-gray-300"
            >
              <option value="">Todos</option>
              {servicios.map(servicio => (
                <option key={servicio} value={servicio}>{servicio}</option>
              ))}
            </Select>
          </div>
        </div>
      </div>

      {/* Lista de traslados */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
        {isLoading ? (
          <p className="text-gray-700 text-center py-4">Cargando traslados...</p>
        ) : filteredTraslados.length > 0 ? (
          <table className="min-w-full table-auto">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Fecha Solicitud
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Paciente
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Traslado
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Motivo
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Solicitante
                </th>
                <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredTraslados.map((traslado) => (
                <tr key={traslado.id} className="hover:bg-gray-50 transition-colors">
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-gray-500" />
                    {formatDate(traslado.fecha_solicitud)}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{traslado.hospitalizacion?.paciente.nombre_completo}</div>
                      <div className="text-gray-500 text-xs">{traslado.hospitalizacion?.paciente.numero_documento}</div>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    <div className="flex items-center">
                      <div className="text-center">
                        <div className="text-blue-600 font-medium">{traslado.cama_origen?.numero}</div>
                        <div className="text-xs text-gray-500">{traslado.cama_origen?.servicio}</div>
                      </div>
                      <FontAwesomeIcon icon={faExchangeAlt} className="mx-2 text-gray-400" />
                      <div className="text-center">
                        <div className="text-green-600 font-medium">{traslado.cama_destino?.numero}</div>
                        <div className="text-xs text-gray-500">{traslado.cama_destino?.servicio}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    <div className="max-w-xs truncate" title={traslado.motivo}>
                      {traslado.motivo}
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    <EstadoBadge estado={traslado.estado} size="sm" />
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    <FontAwesomeIcon icon={faUser} className="mr-2 text-gray-500" />
                    {traslado.usuario_solicita?.nombre}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                    <div className="flex justify-end space-x-1">
                      {traslado.estado === 'PENDIENTE' && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAprobar(traslado)}
                            className="text-green-700 hover:text-green-800 hover:bg-green-50 border border-green-200 bg-green-50/50"
                            title="Aprobar traslado"
                          >
                            <FontAwesomeIcon icon={faCheck} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCancelar(traslado)}
                            className="text-red-700 hover:text-red-800 hover:bg-red-50 border border-red-200 bg-red-50/50"
                            title="Cancelar traslado"
                          >
                            <FontAwesomeIcon icon={faTimes} />
                          </Button>
                        </>
                      )}
                      {traslado.estado === 'APROBADO' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEjecutar(traslado)}
                          className="text-blue-700 hover:text-blue-800 hover:bg-blue-50 border border-blue-200 bg-blue-50/50"
                          title="Ejecutar traslado"
                        >
                          <FontAwesomeIcon icon={faExchangeAlt} />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-700 hover:text-gray-800 hover:bg-gray-50 border border-gray-200 bg-gray-50/50"
                        title="Ver detalles"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p className="text-gray-700 text-center py-4">
            No se encontraron traslados.
          </p>
        )}
      </div>

      {/* Modal para nuevo traslado */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Nuevo Traslado"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-gray-700 mb-1">Hospitalización</label>
            <Select
              value={formData.hospitalizacion_id}
              onChange={(e) => setFormData({...formData, hospitalizacion_id: e.target.value})}
              required
              className="border-gray-300"
            >
              <option value="">Seleccionar paciente hospitalizado</option>
              {hospitalizacionesActivas?.map(hosp => (
                <option key={hosp.id} value={hosp.id}>
                  {hosp.paciente} - {hosp.cama_actual}
                </option>
              ))}
            </Select>
          </div>

          <div>
            <label className="block text-gray-700 mb-1">Cama Destino</label>
            <Select
              value={formData.cama_destino_id}
              onChange={(e) => setFormData({...formData, cama_destino_id: e.target.value})}
              required
              className="border-gray-300"
            >
              <option value="">Seleccionar cama destino</option>
              {camasDisponibles?.map(cama => (
                <option key={cama.id} value={cama.id}>
                  Cama {cama.numero} - {cama.servicio} ({cama.habitacion})
                </option>
              ))}
            </Select>
          </div>

          <div>
            <label className="block text-gray-700 mb-1">Motivo del Traslado</label>
            <textarea
              className="w-full p-2 rounded border border-gray-300 text-gray-900"
              rows={3}
              value={formData.motivo}
              onChange={(e) => setFormData({...formData, motivo: e.target.value})}
              placeholder="Describa el motivo del traslado"
              required
            />
          </div>

          <div>
            <label className="block text-gray-700 mb-1">Observaciones</label>
            <textarea
              className="w-full p-2 rounded border border-gray-300 text-gray-900"
              rows={2}
              value={formData.observaciones}
              onChange={(e) => setFormData({...formData, observaciones: e.target.value})}
              placeholder="Observaciones adicionales (opcional)"
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsModalOpen(false)}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={createTrasladoMutation.isPending}>
              {createTrasladoMutation.isPending ? 'Guardando...' : 'Solicitar Traslado'}
            </Button>
          </div>
        </form>
      </Modal>

      {/* Modal de confirmación */}
      <ModalConfirmacionComponent />
    </div>
  );
};

export default GestorTraslados;
