import { useParams, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { hospitalizacionesService } from "../../services/hospitalizacionesService";
import { Button } from "../../components/ui/Button";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowLeft, faEdit } from "@fortawesome/free-solid-svg-icons";

export const HospitalizacionDetalle = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Obtener detalles de la hospitalización
  const { data: hospitalizacion, isLoading, isError } = useQuery({
    queryKey: ["hospitalizacion", id],
    queryFn: () => hospitalizacionesService.getById(id!),
    enabled: !!id,
  });

  // Formatear fecha
  const formatDate = (dateString?: string) => {
    if (!dateString) return "-";
    try {
      return format(new Date(dateString), "dd/MM/yyyy HH:mm", { locale: es });
    } catch (error) {
      console.error("Error al formatear la fecha:", error);
      return dateString;
    }
  };

  return (
    <div className="p-6 bg-white min-h-screen">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Button
              variant="ghost"
              onClick={() => navigate("/hospitalizaciones")}
              className="mr-4 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:outline-none focus:scale-105 transition-all duration-200 focus:shadow-lg"
            >
              <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
              Volver
            </Button>
            <h1 className="text-2xl font-bold text-gray-900">
              Detalle de Hospitalización
            </h1>
          </div>
          {hospitalizacion && (
            <Button
              onClick={() => navigate(`/hospitalizaciones/editar/${id}`)}
              className="focus:ring-4 focus:ring-blue-300 focus:ring-offset-2 focus:outline-none focus:scale-105 transition-all duration-300 focus:shadow-xl focus:shadow-blue-500/50"
            >
              <FontAwesomeIcon icon={faEdit} className="mr-2" />
              Editar
            </Button>
          )}
        </div>

        {isLoading ? (
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
            <p className="text-gray-700 text-center">Cargando información...</p>
          </div>
        ) : isError ? (
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
            <p className="text-red-500 text-center">
              Error al cargar la información de la hospitalización
            </p>
          </div>
        ) : hospitalizacion ? (
          <div className="grid grid-cols-1 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
                Información General
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <p className="text-gray-600 text-sm font-medium">Consecutivo</p>
                  <p className="text-gray-900 font-semibold">{hospitalizacion.consecutivo}</p>
                </div>
                <div>
                  <p className="text-gray-600 text-sm font-medium">Fecha de Ingreso</p>
                  <p className="text-gray-900 font-semibold">{formatDate(hospitalizacion.fecha_ingreso)}</p>
                </div>
                <div>
                  <p className="text-gray-600 text-sm font-medium">Fecha de Alta</p>
                  <p className="text-gray-900 font-semibold">{formatDate(hospitalizacion.fecha_alta)}</p>
                </div>
                <div>
                  <p className="text-gray-600 text-sm font-medium">Cama</p>
                  <p className="text-gray-900 font-semibold">{hospitalizacion.cama?.numero || hospitalizacion.cama_id}</p>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
                Información del Paciente
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div>
                  <p className="text-gray-600 text-sm font-medium">Nombre</p>
                  <p className="text-gray-900 font-semibold">{hospitalizacion.paciente?.nombre_completo || "-"}</p>
                </div>
                <div>
                  <p className="text-gray-600 text-sm font-medium">Documento</p>
                  <p className="text-gray-900 font-semibold">
                    {hospitalizacion.tipo_documento_identificacion} {hospitalizacion.num_documento_identificacion}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 border-b border-gray-200 pb-2">
                Motivo de Hospitalización
              </h2>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <p className="text-gray-600 text-sm font-medium">Motivo</p>
                  <p className="text-gray-900 whitespace-pre-line">{hospitalizacion.motivo || "-"}</p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
            <p className="text-gray-700 text-center">No se encontró la hospitalización</p>
          </div>
        )}
      </div>
  );
};

