import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { hospitalizacionesService } from "../../services/hospitalizacionesService";
import { camasService } from "../../services/camasService";
import { Button } from "../../components/ui/Button";
import { Input } from "../../components/ui/Input";
import { Select } from "../../components/ui/Select";
import { Textarea } from "../../components/ui/Textarea";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowLeft, faSave, faStethoscope, faInfoCircle } from "@fortawesome/free-solid-svg-icons";
import { DiagnosticoBusquedaSelector, DiagnosticoCIE } from "../pacientes/components/DiagnosticoBusquedaSelector";
import { notificationService } from "../../services/notificationService";

export const HospitalizacionEditar = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditing = !!id;
  const queryClient = useQueryClient();

  // Estado del formulario
  const [formData, setFormData] = useState({
    paciente_id: "",
    cama_id: "",
    tipo_documento_identificacion: "CC",
    num_documento_identificacion: "",
    fecha_ingreso: "",
    fecha_alta: "",
    motivo: "",
    diagnostico_id: "",
    diagnostico_codigo: "",
    diagnostico_descripcion: "",
    diagnostico_version: "CIE-11" as "CIE-10" | "CIE-11",
  });

  // Obtener detalles de la hospitalización si estamos editando
  const { data: hospitalizacion, isLoading: isLoadingHospitalizacion } = useQuery({
    queryKey: ["hospitalizacion", id],
    queryFn: () => hospitalizacionesService.getById(id!),
    enabled: isEditing,
  });

  // Obtener lista de camas
  const { data: camas, isLoading: isLoadingCamas } = useQuery({
    queryKey: ["camas"],
    queryFn: () => camasService.getAll(),
  });

  // Actualizar el estado del formulario cuando se carga la hospitalización
  useEffect(() => {
    if (hospitalizacion) {
      setFormData({
        paciente_id: hospitalizacion.paciente_id || "",
        cama_id: hospitalizacion.cama_id || "",
        tipo_documento_identificacion: hospitalizacion.tipo_documento_identificacion || "CC",
        num_documento_identificacion: hospitalizacion.num_documento_identificacion || "",
        fecha_ingreso: hospitalizacion.fecha_ingreso ? format(new Date(hospitalizacion.fecha_ingreso), "yyyy-MM-dd'T'HH:mm") : "",
        fecha_alta: hospitalizacion.fecha_alta ? format(new Date(hospitalizacion.fecha_alta), "yyyy-MM-dd'T'HH:mm") : "",
        motivo: hospitalizacion.motivo || "",
        diagnostico_id: hospitalizacion.diagnostico_id || "",
        diagnostico_codigo: hospitalizacion.diagnostico_codigo || "",
        diagnostico_descripcion: hospitalizacion.diagnostico_descripcion || "",
        diagnostico_version: hospitalizacion.diagnostico_version || "CIE-11",
      });
    }
  }, [hospitalizacion]);

  // Manejar cambios en los campos del formulario
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };
  
  // Manejar la selección de diagnóstico CIE-11
  const handleSelectDiagnostico = (diagnostico: DiagnosticoCIE) => {
    setFormData(prev => ({
      ...prev,
      diagnostico_id: `${diagnostico.version}-${diagnostico.codigo}`,
      diagnostico_codigo: diagnostico.codigo,
      diagnostico_descripcion: diagnostico.descripcion,
      diagnostico_version: diagnostico.version
    }));
    
    if (diagnostico.version === 'CIE-11') {
      notificationService.diagnosticoCIE11(
        diagnostico.codigo,
        diagnostico.descripcion,
        'seleccionado'
      );
    }
  };

  // Mutación para crear/actualizar hospitalización
  const mutation = useMutation({
    mutationFn: (data: any) => {
      return isEditing
        ? hospitalizacionesService.update(id!, data)
        : hospitalizacionesService.create(data);
    },
    onSuccess: () => {
      // Mostrar notificación según el diagnóstico seleccionado
      if (formData.diagnostico_codigo && formData.diagnostico_version === 'CIE-11') {
        notificationService.diagnosticoCIE11(
          formData.diagnostico_codigo,
          formData.diagnostico_descripcion,
          'guardado'
        );
      } else {
        notificationService.success('Hospitalización guardada correctamente');
      }
      
      queryClient.invalidateQueries({ queryKey: ["hospitalizaciones"] });
      queryClient.invalidateQueries({ queryKey: ["camas"] });
      navigate("/hospitalizaciones");
    },
  });

  // Manejar envío del formulario
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    mutation.mutate(formData);
  };

  // Filtrar camas disponibles
  const camasDisponibles = camas
    ? camas.filter((c) => c.estado === "Libre" || c.estado === "Reservada" || (isEditing && c.id === hospitalizacion?.cama_id))
    : [];

  return (
    <div className="p-6">
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            onClick={() => navigate("/hospitalizaciones")}
            className="mr-4 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:outline-none focus:scale-105 transition-all duration-200 focus:shadow-lg"
          >
            <FontAwesomeIcon icon={faArrowLeft} className="mr-2" />
            Volver
          </Button>
          <h1 className="text-2xl font-bold text-white">
            {isEditing ? "Editar Hospitalización" : "Nueva Hospitalización"}
          </h1>
        </div>

        {isEditing && isLoadingHospitalizacion ? (
          <div className="bg-gray-800 rounded-lg shadow-lg p-6">
            <p className="text-white text-center">Cargando información...</p>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
              <h2 className="text-xl font-semibold text-white mb-4 border-b border-gray-700 pb-2">
                Información General
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-white mb-1">
                    ID del Paciente *
                  </label>
                  <Input
                    type="text"
                    name="paciente_id"
                    value={formData.paciente_id}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-white mb-1">
                    Cama *
                  </label>
                  <Select
                    name="cama_id"
                    value={formData.cama_id}
                    onChange={handleChange}
                    required
                  >
                    <option value="">Seleccione una cama</option>
                    {isLoadingCamas ? (
                      <option value="" disabled>Cargando camas...</option>
                    ) : (
                      camasDisponibles.map((cama) => (
                        <option key={cama.id} value={cama.id}>
                          {cama.numero} - {cama.ubicacion || ""}
                        </option>
                      ))
                    )}
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-white mb-1">
                    Tipo de Documento *
                  </label>
                  <Select
                    name="tipo_documento_identificacion"
                    value={formData.tipo_documento_identificacion}
                    onChange={handleChange}
                    required
                  >
                    <option value="CC">Cédula de Ciudadanía</option>
                    <option value="TI">Tarjeta de Identidad</option>
                    <option value="CE">Cédula de Extranjería</option>
                    <option value="PA">Pasaporte</option>
                    <option value="RC">Registro Civil</option>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-white mb-1">
                    Número de Documento *
                  </label>
                  <Input
                    type="text"
                    name="num_documento_identificacion"
                    value={formData.num_documento_identificacion}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-white mb-1">
                    Fecha de Ingreso *
                  </label>
                  <Input
                    type="datetime-local"
                    name="fecha_ingreso"
                    value={formData.fecha_ingreso}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-white mb-1">
                    Fecha de Alta
                  </label>
                  <Input
                    type="datetime-local"
                    name="fecha_alta"
                    value={formData.fecha_alta}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
              <h2 className="text-xl font-semibold text-white mb-4 border-b border-gray-700 pb-2">
                Motivo de Hospitalización
              </h2>
              <div className="grid grid-cols-1 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium text-white mb-1">
                    Motivo
                  </label>
                  <Textarea
                    name="motivo"
                    value={formData.motivo}
                    onChange={handleChange}
                    rows={4}
                  />
                </div>
              </div>
            </div>
            
            <div className="bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
              <h2 className="text-xl font-semibold text-white mb-4 border-b border-gray-700 pb-2 flex items-center">
                <FontAwesomeIcon icon={faStethoscope} className="mr-2 text-blue-500" />
                Diagnóstico Principal (CIE-10/CIE-11)
              </h2>

              {/* Mensaje de transición */}
              <div className="mb-4 p-3 bg-blue-900/30 border border-blue-700 rounded-md">
                <div className="flex items-start">
                  <FontAwesomeIcon icon={faInfoCircle} className="text-blue-400 mr-2 mt-0.5" />
                  <div className="text-sm text-blue-200">
                    <p className="font-medium">Transición CIE-10 → CIE-11</p>
                    <p>El sistema está en proceso de transición. Se recomienda usar códigos CIE-11 para nuevos diagnósticos.</p>
                  </div>
                </div>
              </div>

              <div className="mb-4">
                <DiagnosticoBusquedaSelector
                  version="AMBAS"
                  onSelectDiagnostico={handleSelectDiagnostico}
                  placeholder="Buscar diagnóstico CIE-10 o CIE-11..."
                  label="Seleccione el diagnóstico principal"
                  mostrarTransicion={true}
                />
                
                {formData.diagnostico_codigo && (
                  <div className="mt-4 p-3 bg-blue-900/30 border border-blue-700 rounded-md">
                    <p className="text-white font-medium">Diagnóstico seleccionado:</p>
                    <p className="text-sm text-white mt-1">
                      <span className="font-medium text-blue-400">{formData.diagnostico_codigo}</span> - {formData.diagnostico_descripcion}
                    </p>
                    <p className="text-xs text-gray-400 mt-1">
                      Versión: {formData.diagnostico_version}
                    </p>
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end">
              <Button
                type="button"
                variant="secondary"
                onClick={() => navigate("/hospitalizaciones")}
                className="mr-2 focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 focus:outline-none focus:scale-105 transition-all duration-200 focus:shadow-lg"
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                disabled={mutation.isPending}
                className="focus:ring-4 focus:ring-blue-300 focus:ring-offset-2 focus:outline-none focus:scale-105 transition-all duration-300 focus:shadow-xl focus:shadow-blue-500/50"
              >
                <FontAwesomeIcon icon={faSave} className="mr-2" />
                {mutation.isPending ? "Guardando..." : "Guardar"}
              </Button>
            </div>
          </form>
        )}
      </div>
  );
};

