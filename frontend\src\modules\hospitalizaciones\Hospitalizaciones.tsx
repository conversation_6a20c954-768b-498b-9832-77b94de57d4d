import { useState, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { hospitalizacionesService } from "../../services/hospitalizacionesService";
import { Hospitalizacion } from "../../types";
import { Button } from "../../components/ui/Button";
import { Input } from "../../components/ui/Input";
import { Select } from "../../components/ui/Select";
import { EstadoBadge } from "../../components/ui/EstadosBadges";
import { ModalConfirmacion, useModalConfirmacion } from "../../components/ui/ModalConfirmacion";
import GestorCamas from "./GestorCamas";
import GestorTraslados from "./GestorTraslados";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faEye,
  faBed,
  faExchangeAlt,
  faChartLine,
  faHospital,
  faUser,
  faCalendarAlt,
  faUserMinus
} from "@fortawesome/free-solid-svg-icons";

export const Hospitalizaciones = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'hospitalizaciones' | 'camas' | 'traslados' | 'reportes'>('hospitalizaciones');
  const [searchTerm, setSearchTerm] = useState("");
  const [estadoFilter, setEstadoFilter] = useState<string>("");
  const [selectedHospitalizacion, setSelectedHospitalizacion] = useState<Hospitalizacion | null>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0, showAbove: false });

  // Modal de confirmación
  const { mostrar: mostrarConfirmacion, ModalConfirmacion: ModalConfirmacionComponent } = useModalConfirmacion();

  // Obtener lista de hospitalizaciones
  const { data: hospitalizaciones, isLoading, isError, refetch } = useQuery({
    queryKey: ["hospitalizaciones"],
    queryFn: () => hospitalizacionesService.getAll(),
  });

  // Eliminar hospitalización
  const handleDelete = (id: string) => {
    mostrarConfirmacion({
      tipo: 'eliminar',
      titulo: 'Eliminar Hospitalización',
      mensaje: '¿Está seguro de eliminar esta hospitalización? Esta acción no se puede deshacer.',
      onConfirm: async () => {
        try {
          await hospitalizacionesService.delete(id);
          refetch();
        } catch (error) {
          console.error("Error al eliminar la hospitalización:", error);
        }
      }
    });
  };

  // Dar de alta a un paciente
  const handleDarAlta = (hospitalizacion: Hospitalizacion) => {
    mostrarConfirmacion({
      tipo: 'confirmar',
      titulo: 'Dar de Alta',
      mensaje: `¿Está seguro de dar de alta al paciente ${hospitalizacion.paciente?.nombre_completo}?`,
      requiereMotivo: true,
      placeholderMotivo: 'Ingrese el motivo del alta médica',
      onConfirm: async (motivo) => {
        try {
          console.log('Dando de alta con motivo:', motivo);
          // Aquí iría la llamada al servicio para dar de alta
          refetch();
        } catch (error) {
          console.error("Error al dar de alta:", error);
        }
      }
    });
  };

  // Filtrar hospitalizaciones
  const filteredHospitalizaciones = Array.isArray(hospitalizaciones)
    ? hospitalizaciones.filter((hospitalizacion: Hospitalizacion) => {
        // Filtro por término de búsqueda
        const matchesSearchTerm =
          hospitalizacion.consecutivo?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          hospitalizacion.num_documento_identificacion?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (hospitalizacion.paciente?.nombre_completo || "").toLowerCase().includes(searchTerm.toLowerCase());

        // Filtro por estado
        let matchesEstado = true;
        if (estadoFilter === "activa") {
          matchesEstado = !hospitalizacion.fecha_alta;
        } else if (estadoFilter === "alta") {
          matchesEstado = !!hospitalizacion.fecha_alta;
        }

        return matchesSearchTerm && matchesEstado;
      })
    : [];

  // Formatear fecha
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy HH:mm", { locale: es });
    } catch (error) {
      console.error("Error al formatear la fecha:", error);
      return dateString;
    }
  };

  // Manejar el mouse enter en una fila
  const handleRowMouseEnter = (hospitalizacion: Hospitalizacion, event: React.MouseEvent) => {
    setSelectedHospitalizacion(hospitalizacion);
    setShowTooltip(true);

    // Calcular la posición del tooltip
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    const tooltipHeight = 100; // Altura estimada del tooltip
    const windowHeight = window.innerHeight;
    const viewportBottom = window.scrollY + windowHeight;
    const rowMiddle = rect.top + (rect.height / 2);
    const viewportMiddle = window.scrollY + (windowHeight / 2);

    // Determinar si debe mostrarse arriba o abajo basado en la posición de la fila en la ventana
    const showAbove = rowMiddle > viewportMiddle;

    setTooltipPosition({
      x: rect.left,
      y: showAbove ? rect.top - 5 : rect.bottom + 5,
      showAbove: showAbove
    });
  };

  // Manejar el mouse leave en una fila
  const handleRowMouseLeave = () => {
    setShowTooltip(false);
  };

  // Mapear estado de hospitalización
  const mapEstadoHospitalizacion = (hospitalizacion: Hospitalizacion) => {
    if (hospitalizacion.fecha_alta) {
      return 'COMPLETADO'; // Alta médica
    } else {
      const fechaIngreso = new Date(hospitalizacion.fecha_ingreso);
      const hoy = new Date();
      const diasHospitalizado = Math.floor((hoy.getTime() - fechaIngreso.getTime()) / (1000 * 60 * 60 * 24));

      if (diasHospitalizado > 7) {
        return 'VENCIDO'; // Hospitalización prolongada
      } else if (diasHospitalizado > 3) {
        return 'EN_PROGRESO'; // Hospitalización media
      } else {
        return 'ACTIVO'; // Hospitalización reciente
      }
    }
  };

  // Calcular días de hospitalización
  const calcularDiasHospitalizacion = (fechaIngreso: string) => {
    const fecha = new Date(fechaIngreso);
    const hoy = new Date();
    const diferencia = hoy.getTime() - fecha.getTime();
    return Math.floor(diferencia / (1000 * 60 * 60 * 24));
  };

  // Renderizar contenido según pestaña activa
  const renderTabContent = () => {
    switch (activeTab) {
      case 'camas':
        return <GestorCamas />;
      case 'traslados':
        return <GestorTraslados />;
      case 'reportes':
        return (
          <div className="p-6 text-center">
            <FontAwesomeIcon icon={faChartLine} className="text-6xl text-gray-400 mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Reportes de Hospitalización</h2>
            <p className="text-gray-600">Funcionalidad en desarrollo</p>
          </div>
        );
      default:
        return renderHospitalizacionesContent();
    }
  };

  // Renderizar contenido de hospitalizaciones principal
  const renderHospitalizacionesContent = () => (
    <>
      {/* Filtros */}
      <div className="bg-white border border-gray-200 p-4 mb-6 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Filtros</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Buscar
            </label>
            <div className="relative">
              <Input
                type="text"
                placeholder="Buscar por consecutivo, documento o paciente"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 border-gray-300"
              />
              <FontAwesomeIcon
                icon={faSearch}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Estado
            </label>
            <Select
              value={estadoFilter}
              onChange={(e) => setEstadoFilter(e.target.value)}
              className="border-gray-300"
            >
              <option value="">Todos</option>
              <option value="activa">Activas (Sin alta)</option>
              <option value="alta">Con alta</option>
            </Select>
          </div>
        </div>
      </div>

      {/* Tabla de hospitalizaciones */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm">
        {isLoading ? (
          <p className="text-gray-700 text-center py-4">Cargando hospitalizaciones...</p>
        ) : isError ? (
          <p className="text-red-500 text-center py-4">
            Error al cargar las hospitalizaciones
          </p>
        ) : Array.isArray(filteredHospitalizaciones) && filteredHospitalizaciones.length > 0 ? (
          <table className="min-w-full table-auto">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Consecutivo
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Paciente
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Fecha Ingreso
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Días Hosp.
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Cama
                </th>
                <th className="px-4 py-3 text-right text-sm font-medium text-gray-700 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredHospitalizaciones.map((hospitalizacion: Hospitalizacion) => (
                <tr
                  key={hospitalizacion.id}
                  className="hover:bg-gray-50 transition-colors"
                  onMouseEnter={(e) => handleRowMouseEnter(hospitalizacion, e)}
                  onMouseLeave={handleRowMouseLeave}
                >
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    {hospitalizacion.consecutivo}
                  </td>
                  <td className="px-4 py-3 text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{hospitalizacion.paciente?.nombre_completo || 'Paciente no disponible'}</div>
                      <div className="text-gray-500 text-xs">
                        {hospitalizacion.tipo_documento_identificacion} {hospitalizacion.num_documento_identificacion}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-gray-500" />
                    {formatDate(hospitalizacion.fecha_ingreso)}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-medium">
                    {calcularDiasHospitalizacion(hospitalizacion.fecha_ingreso)} días
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    <EstadoBadge estado={mapEstadoHospitalizacion(hospitalizacion)} size="sm" />
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                    <FontAwesomeIcon icon={faBed} className="mr-2 text-gray-500" />
                    {hospitalizacion.cama?.numero || hospitalizacion.cama_id}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                    <div className="flex justify-end space-x-1">
                      {!hospitalizacion.fecha_alta && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDarAlta(hospitalizacion)}
                          className="text-green-700 hover:text-green-800 hover:bg-green-50 border border-green-200 bg-green-50/50"
                          title="Dar de alta"
                        >
                          <FontAwesomeIcon icon={faUserMinus} className="text-green-700" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigate(`/hospitalizaciones/ver/${hospitalizacion.id}`)}
                        className="text-blue-700 hover:text-blue-800 hover:bg-blue-50 border border-blue-200 bg-blue-50/50"
                        title="Ver detalles"
                      >
                        <FontAwesomeIcon icon={faEye} className="text-blue-700" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigate(`/hospitalizaciones/editar/${hospitalizacion.id}`)}
                        className="text-amber-700 hover:text-amber-800 hover:bg-amber-50 border border-amber-200 bg-amber-50/50"
                        title="Editar hospitalización"
                      >
                        <FontAwesomeIcon icon={faEdit} className="text-amber-700" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(hospitalizacion.id)}
                        className="text-red-700 hover:text-red-800 hover:bg-red-50 border border-red-200 bg-red-50/50"
                        title="Eliminar hospitalización"
                      >
                        <FontAwesomeIcon icon={faTrash} className="text-red-700" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p className="text-gray-700 text-center py-4">
            No se encontraron registros de hospitalizaciones.
          </p>
        )}
      </div>

      {/* Tooltip para mostrar información adicional */}
      {showTooltip && selectedHospitalizacion && selectedHospitalizacion.paciente && (
        <div
          ref={tooltipRef}
          className={`fixed z-50 bg-gray-800 text-white p-3 rounded shadow-lg border border-gray-700 tooltip-container ${tooltipPosition.showAbove ? 'tooltip-above' : 'tooltip-below'}`}
          style={{
            left: `${tooltipPosition.x}px`,
            top: tooltipPosition.showAbove ? 'auto' : `${tooltipPosition.y}px`,
            bottom: tooltipPosition.showAbove ? `calc(100vh - ${tooltipPosition.y}px)` : 'auto',
          }}
        >
          <p className="text-sm font-medium">
            <span className="font-bold">Paciente:</span> {selectedHospitalizacion.paciente.nombre_completo}
          </p>
          {selectedHospitalizacion.motivo && (
            <p className="text-sm font-medium">
              <span className="font-bold">Motivo:</span> {selectedHospitalizacion.motivo}
            </p>
          )}
          <p className="text-sm font-medium">
            <span className="font-bold">Días hospitalizados:</span> {calcularDiasHospitalizacion(selectedHospitalizacion.fecha_ingreso)}
          </p>
          {selectedHospitalizacion.fecha_alta && (
            <p className="text-sm font-medium">
              <span className="font-bold">Fecha alta:</span> {formatDate(selectedHospitalizacion.fecha_alta)}
            </p>
          )}
        </div>
      )}
    </>
  );

  return (
    <div className="p-6 bg-white min-h-screen">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          <FontAwesomeIcon icon={faHospital} className="mr-2" />
          Gestión de Hospitalizaciones
        </h1>
        {activeTab === 'hospitalizaciones' && (
          <Button onClick={() => navigate("/hospitalizaciones/nueva")} className="bg-blue-600 hover:bg-blue-700 text-white">
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nueva Hospitalización
          </Button>
        )}
      </div>

      {/* Navegación por pestañas */}
      <div className="mb-6 border-b border-gray-200">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('hospitalizaciones')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'hospitalizaciones'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FontAwesomeIcon icon={faHospital} className="mr-2" />
            Hospitalizaciones
          </button>
          <button
            onClick={() => setActiveTab('camas')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'camas'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FontAwesomeIcon icon={faBed} className="mr-2" />
            Gestión de Camas
          </button>
          <button
            onClick={() => setActiveTab('traslados')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'traslados'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FontAwesomeIcon icon={faExchangeAlt} className="mr-2" />
            Traslados
          </button>
          <button
            onClick={() => setActiveTab('reportes')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'reportes'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <FontAwesomeIcon icon={faChartLine} className="mr-2" />
            Reportes
          </button>
        </nav>
      </div>

      {/* Contenido de la pestaña activa */}
      {renderTabContent()}

      {/* Modal de confirmación */}
      <ModalConfirmacionComponent />
    </div>
  );
};
