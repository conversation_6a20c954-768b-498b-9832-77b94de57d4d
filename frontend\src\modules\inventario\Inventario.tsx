import { useState, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { inventarioService } from "../../services/inventarioService";
import { ItemInventario } from "../../types";
import { Button } from "../../components/ui/Button";
import { Input } from "../../components/ui/Input";
import { Select } from "../../components/ui/Select";
import MovimientosInventario from "./MovimientosInventario";
import TransferenciasInventario from "./TransferenciasInventario";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faPlus,
  faSearch,
  faEdit,
  faTrash,
  faEye,
  faBoxes,
  faExchangeAlt,
  faClipboardList,
  faChartLine
} from "@fortawesome/free-solid-svg-icons";

export default function Inventario() {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'inventario' | 'movimientos' | 'transferencias' | 'reportes'>('inventario');
  const [searchTerm, setSearchTerm] = useState("");
  const [tipoRecursoFilter, setTipoRecursoFilter] = useState<string>("");
  const [stockFilter, setStockFilter] = useState<string>("");
  const [selectedItem, setSelectedItem] = useState<ItemInventario | null>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0, showAbove: false });

  // Obtener lista de items de inventario
  const { data: items, isLoading, isError, refetch } = useQuery<ItemInventario[]>({
    queryKey: ["inventario", tipoRecursoFilter, stockFilter],
    queryFn: inventarioService.getAll
  });

  // Eliminar item
  const handleDelete = async (id: string) => {
    if (window.confirm("¿Está seguro de eliminar este ítem del inventario?")) {
      try {
        await inventarioService.delete(id);
        refetch();
      } catch (error) {
        console.error("Error al eliminar el ítem:", error);
      }
    }
  };

  // Filtrar items
  const filteredItems = items
    ? items.filter((item: ItemInventario) => {
        const matchesSearch =
          item.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.codigo.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (item.ubicacion || "").toLowerCase().includes(searchTerm.toLowerCase());

        const matchesTipoRecurso = tipoRecursoFilter ? item.tipo_recurso === tipoRecursoFilter : true;

        const matchesStock = stockFilter === ""
          ? true
          : stockFilter === "bajo"
            ? item.stock_actual <= item.stock_minimo
            : item.stock_actual > item.stock_minimo;

        return matchesSearch && matchesTipoRecurso && matchesStock;
      })
    : [];

  // Formatear fecha
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy", { locale: es });
    } catch (error) {
      console.error("Error al formatear la fecha:", error);
      return dateString;
    }
  };

  // Obtener color según nivel de stock
  const getStockColor = (stockActual: number, stockMinimo: number) => {
    if (stockActual <= stockMinimo * 0.5) {
      return "text-red-500"; // Crítico
    } else if (stockActual <= stockMinimo) {
      return "text-yellow-500"; // Bajo
    } else if (stockActual <= stockMinimo * 2) {
      return "text-blue-500"; // Normal
    } else {
      return "text-green-500"; // Alto
    }
  };

  // Manejar el mouse enter en una fila
  const handleRowMouseEnter = (item: ItemInventario, event: React.MouseEvent) => {
    setSelectedItem(item);
    setShowTooltip(true);

    // Calcular la posición del tooltip
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const rowMiddle = rect.top + (rect.height / 2);
    const viewportMiddle = window.scrollY + (windowHeight / 2);

    // Determinar si debe mostrarse arriba o abajo basado en la posición de la fila en la ventana
    const showAbove = rowMiddle > viewportMiddle;

    setTooltipPosition({
      x: rect.left,
      y: showAbove ? rect.top - 5 : rect.bottom + 5,
      showAbove: showAbove
    });
  };

  // Manejar el mouse leave en una fila
  const handleRowMouseLeave = () => {
    setShowTooltip(false);
  };

  // Renderizar contenido según pestaña activa
  const renderTabContent = () => {
    switch (activeTab) {
      case 'movimientos':
        return <MovimientosInventario />;
      case 'transferencias':
        return <TransferenciasInventario />;
      case 'reportes':
        return (
          <div className="p-6 text-center">
            <FontAwesomeIcon icon={faChartLine} className="text-6xl text-gray-400 mb-4" />
            <h2 className="text-xl font-semibold text-white mb-2">Reportes de Inventario</h2>
            <p className="text-gray-400">Funcionalidad en desarrollo</p>
          </div>
        );
      default:
        return renderInventarioContent();
    }
  };

  // Renderizar contenido del inventario principal
  const renderInventarioContent = () => (
    <>
      <div className="glassmorphism p-4 mb-6 rounded-lg">
        <h2 className="text-xl font-semibold text-white mb-4">Filtros</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="search" className="block text-white mb-1">Búsqueda</label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <FontAwesomeIcon icon={faSearch} className="text-gray-400" />
              </div>
              <Input
                id="search"
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Buscar por nombre, código o ubicación"
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <label htmlFor="tipoRecurso" className="block text-white mb-1">Tipo de Recurso</label>
            <Select
              id="tipoRecurso"
              value={tipoRecursoFilter}
              onChange={(e) => setTipoRecursoFilter(e.target.value)}
              className="w-full"
            >
              <option value="">Todos</option>
              <option value="Medicamento">Medicamento</option>
              <option value="Material Médico">Material Médico</option>
              <option value="Insumo">Insumo</option>
              <option value="Equipamiento">Equipamiento</option>
              <option value="Otro">Otro</option>
            </Select>
          </div>

          <div>
            <label htmlFor="stock" className="block text-white mb-1">Nivel de Stock</label>
            <Select
              id="stock"
              value={stockFilter}
              onChange={(e) => setStockFilter(e.target.value)}
              className="w-full"
            >
              <option value="">Todos</option>
              <option value="bajo">Bajo Mínimo</option>
              <option value="normal">Normal</option>
            </Select>
          </div>
        </div>
      </div>

      <div className="bg-gray-900/70 backdrop-blur-sm rounded-lg overflow-hidden">
        {isLoading ? (
          <p className="text-white text-center py-4">Cargando inventario...</p>
        ) : isError ? (
          <p className="text-red-500 text-center py-4">
            Error al cargar el inventario
          </p>
        ) : filteredItems.length > 0 ? (
          <table className="min-w-full table-auto">
            <thead className="border-b border-gray-700">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Código
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Nombre
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Tipo
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Stock Actual
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Stock Mínimo
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Ubicación
                </th>
                <th className="px-4 py-3 text-right text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredItems.map((item: ItemInventario) => (
                <tr
                  key={item.id}
                  className="hover:bg-gray-800 transition-colors duration-150 ease-in-out"
                  onMouseEnter={(e) => handleRowMouseEnter(item, e)}
                  onMouseLeave={handleRowMouseLeave}
                >
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                    {item.codigo}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                    {item.nombre}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                    {item.tipo_recurso}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    <span className={getStockColor(item.stock_actual, item.stock_minimo)}>
                      {item.stock_actual}
                    </span>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                    {item.stock_minimo}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                    {item.ubicacion || "-"}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => navigate(`/inventario/ver/${item.id}`)}
                      className="mr-2"
                    >
                      <FontAwesomeIcon icon={faEye} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => navigate(`/inventario/editar/${item.id}`)}
                      className="mr-2"
                    >
                      <FontAwesomeIcon icon={faEdit} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(item.id)}
                    >
                      <FontAwesomeIcon icon={faTrash} />
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p className="text-white text-center py-4">
            No se encontraron ítems en el inventario.
          </p>
        )}
      </div>

      {/* Tooltip para mostrar información adicional */}
      {showTooltip && selectedItem && (
        <div
          ref={tooltipRef}
          className={`fixed z-50 bg-gray-800 text-white p-3 rounded shadow-lg border border-gray-700 max-w-md tooltip-container ${tooltipPosition.showAbove ? 'tooltip-above' : 'tooltip-below'}`}
          style={{
            left: `${tooltipPosition.x}px`,
            top: tooltipPosition.showAbove ? 'auto' : `${tooltipPosition.y}px`,
            bottom: tooltipPosition.showAbove ? `calc(100vh - ${tooltipPosition.y}px)` : 'auto',
          }}
        >
          <p className="text-sm font-medium mb-1">
            <span className="font-bold">Código:</span> {selectedItem.codigo}
          </p>
          <p className="text-sm font-medium mb-1">
            <span className="font-bold">Nombre:</span> {selectedItem.nombre}
          </p>
          <p className="text-sm font-medium mb-1">
            <span className="font-bold">Tipo:</span> {selectedItem.tipo_recurso}
          </p>
          <p className="text-sm font-medium mb-1">
            <span className="font-bold">Stock Actual:</span>{" "}
            <span className={getStockColor(selectedItem.stock_actual, selectedItem.stock_minimo)}>
              {selectedItem.stock_actual}
            </span>
          </p>
          <p className="text-sm font-medium mb-1">
            <span className="font-bold">Stock Mínimo:</span> {selectedItem.stock_minimo}
          </p>
          {selectedItem.ubicacion && (
            <p className="text-sm font-medium mb-1">
              <span className="font-bold">Ubicación:</span> {selectedItem.ubicacion}
            </p>
          )}
          <p className="text-sm font-medium mb-1">
            <span className="font-bold">Fecha de Registro:</span> {selectedItem.created_at ? formatDate(selectedItem.created_at) : ''}
          </p>
          <p className="text-sm font-medium mb-1">
            <span className="font-bold">Estado:</span>{" "}
            {selectedItem.stock_actual <= selectedItem.stock_minimo * 0.5
              ? "Crítico"
              : selectedItem.stock_actual <= selectedItem.stock_minimo
                ? "Bajo"
                : selectedItem.stock_actual <= selectedItem.stock_minimo * 2
                  ? "Normal"
                  : "Alto"
            }
          </p>
        </div>
      )}
    </>
  );

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-white">Gestión de Inventario</h1>
        {activeTab === 'inventario' && (
          <Button onClick={() => navigate("/inventario/nuevo")}>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nuevo Ítem
          </Button>
        )}
      </div>

      {/* Navegación por pestañas */}
      <div className="mb-6 border-b border-gray-700">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('inventario')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'inventario'
                ? 'border-blue-500 text-blue-400'
                : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
            }`}
          >
            <FontAwesomeIcon icon={faBoxes} className="mr-2" />
            Inventario
          </button>
          <button
            onClick={() => setActiveTab('movimientos')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'movimientos'
                ? 'border-blue-500 text-blue-400'
                : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
            }`}
          >
            <FontAwesomeIcon icon={faClipboardList} className="mr-2" />
            Movimientos
          </button>
          <button
            onClick={() => setActiveTab('transferencias')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'transferencias'
                ? 'border-blue-500 text-blue-400'
                : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
            }`}
          >
            <FontAwesomeIcon icon={faExchangeAlt} className="mr-2" />
            Transferencias
          </button>
          <button
            onClick={() => setActiveTab('reportes')}
            className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
              activeTab === 'reportes'
                ? 'border-blue-500 text-blue-400'
                : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
            }`}
          >
            <FontAwesomeIcon icon={faChartLine} className="mr-2" />
            Reportes
          </button>
        </nav>
      </div>

      {/* Contenido de la pestaña activa */}
      {renderTabContent()}
    </div>
  );
}
