import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../context/AuthContext';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Select } from '../../components/ui/Select';
import { Modal } from '../../components/ui/Modal';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faSearch,
  faArrowUp,
  faArrowDown,
  faExchangeAlt,
  faCalendarAlt,
  faUser,
  faBoxes
} from '@fortawesome/free-solid-svg-icons';

// Tipos para movimientos de inventario
interface MovimientoInventario {
  id: string;
  item_inventario_id: string;
  tipo_movimiento: 'ENTRADA' | 'SALIDA' | 'TRANSFERENCIA' | 'AJUSTE';
  cantidad: number;
  motivo: string;
  ubicacion_origen?: string;
  ubicacion_destino?: string;
  usuario_id: string;
  fecha: string;
  observaciones?: string;
  hospital_id: number;
  // Datos relacionados
  item_inventario?: {
    codigo: string;
    nombre: string;
    tipo_recurso: string;
  };
  usuario?: {
    nombre: string;
  };
}

interface FormMovimiento {
  item_inventario_id: string;
  tipo_movimiento: 'ENTRADA' | 'SALIDA' | 'TRANSFERENCIA' | 'AJUSTE';
  cantidad: number;
  motivo: string;
  ubicacion_origen?: string;
  ubicacion_destino?: string;
  observaciones?: string;
}

const MovimientosInventario: React.FC = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const hospitalId = user?.hospital_id || 1;

  // Estados
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [tipoFilter, setTipoFilter] = useState<string>('');
  const [fechaInicio, setFechaInicio] = useState('');
  const [fechaFin, setFechaFin] = useState('');

  // Formulario
  const [formData, setFormData] = useState<FormMovimiento>({
    item_inventario_id: '',
    tipo_movimiento: 'ENTRADA',
    cantidad: 0,
    motivo: '',
    ubicacion_origen: '',
    ubicacion_destino: '',
    observaciones: ''
  });

  // Query para obtener movimientos
  const { data: movimientos, isLoading } = useQuery<MovimientoInventario[]>({
    queryKey: ['movimientos-inventario', hospitalId, tipoFilter, fechaInicio, fechaFin],
    queryFn: async () => {
      // Simulación de datos - en producción sería una llamada a la API
      return [
        {
          id: '1',
          item_inventario_id: '1',
          tipo_movimiento: 'ENTRADA',
          cantidad: 50,
          motivo: 'Compra a proveedor',
          usuario_id: '1',
          fecha: '2024-01-15T10:30:00Z',
          observaciones: 'Lote ABC123',
          hospital_id: hospitalId,
          item_inventario: {
            codigo: 'MED001',
            nombre: 'Paracetamol 500mg',
            tipo_recurso: 'Medicamento'
          },
          usuario: {
            nombre: 'Dr. Juan Pérez'
          }
        },
        {
          id: '2',
          item_inventario_id: '2',
          tipo_movimiento: 'SALIDA',
          cantidad: 10,
          motivo: 'Dispensación a paciente',
          usuario_id: '2',
          fecha: '2024-01-15T14:20:00Z',
          hospital_id: hospitalId,
          item_inventario: {
            codigo: 'MAT001',
            nombre: 'Jeringa 5ml',
            tipo_recurso: 'Material Médico'
          },
          usuario: {
            nombre: 'Enf. María García'
          }
        },
        {
          id: '3',
          item_inventario_id: '3',
          tipo_movimiento: 'TRANSFERENCIA',
          cantidad: 25,
          motivo: 'Traslado entre almacenes',
          ubicacion_origen: 'Almacén Central',
          ubicacion_destino: 'Farmacia',
          usuario_id: '1',
          fecha: '2024-01-16T09:15:00Z',
          hospital_id: hospitalId,
          item_inventario: {
            codigo: 'MED002',
            nombre: 'Ibuprofeno 400mg',
            tipo_recurso: 'Medicamento'
          },
          usuario: {
            nombre: 'Dr. Juan Pérez'
          }
        }
      ];
    }
  });

  // Query para obtener items de inventario (para el selector)
  const { data: itemsInventario } = useQuery({
    queryKey: ['items-inventario-simple', hospitalId],
    queryFn: async () => {
      // Simulación - en producción sería una llamada a la API
      return [
        { id: '1', codigo: 'MED001', nombre: 'Paracetamol 500mg' },
        { id: '2', codigo: 'MAT001', nombre: 'Jeringa 5ml' },
        { id: '3', codigo: 'MED002', nombre: 'Ibuprofeno 400mg' },
        { id: '4', codigo: 'INS001', nombre: 'Guantes de látex' }
      ];
    }
  });

  // Mutación para crear movimiento
  const createMovimientoMutation = useMutation({
    mutationFn: async (data: FormMovimiento) => {
      // Simulación - en producción sería una llamada a la API
      console.log('Creando movimiento:', data);
      return { id: Date.now().toString(), ...data };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['movimientos-inventario'] });
      queryClient.invalidateQueries({ queryKey: ['inventario'] });
      setIsModalOpen(false);
      resetForm();
    }
  });

  // Filtrar movimientos
  const filteredMovimientos = movimientos?.filter(mov => {
    const matchesSearch = 
      mov.item_inventario?.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mov.item_inventario?.codigo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mov.motivo.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesTipo = tipoFilter ? mov.tipo_movimiento === tipoFilter : true;

    const matchesFecha = (!fechaInicio || mov.fecha >= fechaInicio) && 
                        (!fechaFin || mov.fecha <= fechaFin);

    return matchesSearch && matchesTipo && matchesFecha;
  }) || [];

  // Manejar envío del formulario
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.item_inventario_id && formData.cantidad > 0) {
      createMovimientoMutation.mutate(formData);
    }
  };

  // Resetear formulario
  const resetForm = () => {
    setFormData({
      item_inventario_id: '',
      tipo_movimiento: 'ENTRADA',
      cantidad: 0,
      motivo: '',
      ubicacion_origen: '',
      ubicacion_destino: '',
      observaciones: ''
    });
  };

  // Obtener icono según tipo de movimiento
  const getMovimientoIcon = (tipo: string) => {
    switch (tipo) {
      case 'ENTRADA': return faArrowUp;
      case 'SALIDA': return faArrowDown;
      case 'TRANSFERENCIA': return faExchangeAlt;
      case 'AJUSTE': return faBoxes;
      default: return faBoxes;
    }
  };

  // Obtener color según tipo de movimiento
  const getMovimientoColor = (tipo: string) => {
    switch (tipo) {
      case 'ENTRADA': return 'text-green-500';
      case 'SALIDA': return 'text-red-500';
      case 'TRANSFERENCIA': return 'text-blue-500';
      case 'AJUSTE': return 'text-yellow-500';
      default: return 'text-gray-500';
    }
  };

  // Formatear fecha
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('es-ES', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-white">Movimientos de Inventario</h1>
        <Button onClick={() => setIsModalOpen(true)}>
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Nuevo Movimiento
        </Button>
      </div>

      {/* Filtros */}
      <div className="glassmorphism p-4 mb-6 rounded-lg">
        <h2 className="text-xl font-semibold text-white mb-4">Filtros</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-white mb-1">Búsqueda</label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Buscar por item o motivo"
                className="pl-10"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-white mb-1">Tipo de Movimiento</label>
            <Select
              value={tipoFilter}
              onChange={(e) => setTipoFilter(e.target.value)}
            >
              <option value="">Todos</option>
              <option value="ENTRADA">Entrada</option>
              <option value="SALIDA">Salida</option>
              <option value="TRANSFERENCIA">Transferencia</option>
              <option value="AJUSTE">Ajuste</option>
            </Select>
          </div>
          
          <div>
            <label className="block text-white mb-1">Fecha Inicio</label>
            <Input
              type="date"
              value={fechaInicio}
              onChange={(e) => setFechaInicio(e.target.value)}
            />
          </div>
          
          <div>
            <label className="block text-white mb-1">Fecha Fin</label>
            <Input
              type="date"
              value={fechaFin}
              onChange={(e) => setFechaFin(e.target.value)}
            />
          </div>
        </div>
      </div>

      {/* Lista de movimientos */}
      <div className="bg-gray-900/70 backdrop-blur-sm rounded-lg overflow-hidden">
        {isLoading ? (
          <p className="text-white text-center py-4">Cargando movimientos...</p>
        ) : filteredMovimientos.length > 0 ? (
          <table className="min-w-full table-auto">
            <thead className="border-b border-gray-700">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Fecha
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Tipo
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Item
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Cantidad
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Motivo
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Usuario
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredMovimientos.map((movimiento) => (
                <tr key={movimiento.id} className="hover:bg-gray-800 transition-colors">
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                    <FontAwesomeIcon icon={faCalendarAlt} className="mr-2 text-gray-400" />
                    {formatDate(movimiento.fecha)}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    <span className={`flex items-center ${getMovimientoColor(movimiento.tipo_movimiento)}`}>
                      <FontAwesomeIcon icon={getMovimientoIcon(movimiento.tipo_movimiento)} className="mr-2" />
                      {movimiento.tipo_movimiento}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm text-white">
                    <div>
                      <div className="font-medium">{movimiento.item_inventario?.nombre}</div>
                      <div className="text-gray-400 text-xs">{movimiento.item_inventario?.codigo}</div>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-white font-medium">
                    {movimiento.tipo_movimiento === 'SALIDA' ? '-' : '+'}{movimiento.cantidad}
                  </td>
                  <td className="px-4 py-3 text-sm text-white">
                    {movimiento.motivo}
                    {movimiento.observaciones && (
                      <div className="text-gray-400 text-xs mt-1">{movimiento.observaciones}</div>
                    )}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                    <FontAwesomeIcon icon={faUser} className="mr-2 text-gray-400" />
                    {movimiento.usuario?.nombre}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p className="text-white text-center py-4">
            No se encontraron movimientos.
          </p>
        )}
      </div>

      {/* Modal para nuevo movimiento */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Nuevo Movimiento de Inventario"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-white mb-1">Item de Inventario</label>
            <Select
              value={formData.item_inventario_id}
              onChange={(e) => setFormData({...formData, item_inventario_id: e.target.value})}
              required
            >
              <option value="">Seleccionar item</option>
              {itemsInventario?.map(item => (
                <option key={item.id} value={item.id}>
                  {item.codigo} - {item.nombre}
                </option>
              ))}
            </Select>
          </div>

          <div>
            <label className="block text-white mb-1">Tipo de Movimiento</label>
            <Select
              value={formData.tipo_movimiento}
              onChange={(e) => setFormData({...formData, tipo_movimiento: e.target.value as any})}
              required
            >
              <option value="ENTRADA">Entrada</option>
              <option value="SALIDA">Salida</option>
              <option value="TRANSFERENCIA">Transferencia</option>
              <option value="AJUSTE">Ajuste</option>
            </Select>
          </div>

          <div>
            <label className="block text-white mb-1">Cantidad</label>
            <Input
              type="number"
              min="1"
              value={formData.cantidad}
              onChange={(e) => setFormData({...formData, cantidad: parseInt(e.target.value) || 0})}
              required
            />
          </div>

          <div>
            <label className="block text-white mb-1">Motivo</label>
            <Input
              type="text"
              value={formData.motivo}
              onChange={(e) => setFormData({...formData, motivo: e.target.value})}
              placeholder="Ej: Compra a proveedor, Dispensación a paciente"
              required
            />
          </div>

          {formData.tipo_movimiento === 'TRANSFERENCIA' && (
            <>
              <div>
                <label className="block text-white mb-1">Ubicación Origen</label>
                <Input
                  type="text"
                  value={formData.ubicacion_origen}
                  onChange={(e) => setFormData({...formData, ubicacion_origen: e.target.value})}
                  placeholder="Ej: Almacén Central"
                />
              </div>
              <div>
                <label className="block text-white mb-1">Ubicación Destino</label>
                <Input
                  type="text"
                  value={formData.ubicacion_destino}
                  onChange={(e) => setFormData({...formData, ubicacion_destino: e.target.value})}
                  placeholder="Ej: Farmacia"
                />
              </div>
            </>
          )}

          <div>
            <label className="block text-white mb-1">Observaciones</label>
            <textarea
              className="w-full p-2 rounded bg-transparent border border-gray-300 text-white"
              rows={3}
              value={formData.observaciones}
              onChange={(e) => setFormData({...formData, observaciones: e.target.value})}
              placeholder="Observaciones adicionales (opcional)"
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsModalOpen(false)}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={createMovimientoMutation.isPending}>
              {createMovimientoMutation.isPending ? 'Guardando...' : 'Guardar'}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default MovimientosInventario;
