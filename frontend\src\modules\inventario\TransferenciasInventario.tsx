import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../context/AuthContext';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Select } from '../../components/ui/Select';
import { Modal } from '../../components/ui/Modal';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faPlus,
  faSearch,
  faExchangeAlt,
  faMapMarkerAlt,
  faCheck,
  faTimes,
  faClock,
  faUser
} from '@fortawesome/free-solid-svg-icons';

// Tipos para transferencias
interface TransferenciaInventario {
  id: string;
  item_inventario_id: string;
  cantidad: number;
  ubicacion_origen: string;
  ubicacion_destino: string;
  estado: 'PENDIENTE' | 'EN_TRANSITO' | 'COMPLETADA' | 'CANCELADA';
  motivo: string;
  observaciones?: string;
  usuario_solicita_id: string;
  usuario_autoriza_id?: string;
  usuario_recibe_id?: string;
  fecha_solicitud: string;
  fecha_autorizacion?: string;
  fecha_completada?: string;
  hospital_id: number;
  // Datos relacionados
  item_inventario?: {
    codigo: string;
    nombre: string;
    tipo_recurso: string;
    stock_actual: number;
  };
  usuario_solicita?: {
    nombre: string;
  };
  usuario_autoriza?: {
    nombre: string;
  };
  usuario_recibe?: {
    nombre: string;
  };
}

interface FormTransferencia {
  item_inventario_id: string;
  cantidad: number;
  ubicacion_origen: string;
  ubicacion_destino: string;
  motivo: string;
  observaciones?: string;
}

const TransferenciasInventario: React.FC = () => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const hospitalId = user?.hospital_id || 1;

  // Estados
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [estadoFilter, setEstadoFilter] = useState<string>('');
  const [ubicacionFilter, setUbicacionFilter] = useState<string>('');

  // Formulario
  const [formData, setFormData] = useState<FormTransferencia>({
    item_inventario_id: '',
    cantidad: 0,
    ubicacion_origen: '',
    ubicacion_destino: '',
    motivo: '',
    observaciones: ''
  });

  // Query para obtener transferencias
  const { data: transferencias, isLoading } = useQuery<TransferenciaInventario[]>({
    queryKey: ['transferencias-inventario', hospitalId, estadoFilter, ubicacionFilter],
    queryFn: async () => {
      // Simulación de datos - en producción sería una llamada a la API
      return [
        {
          id: '1',
          item_inventario_id: '1',
          cantidad: 25,
          ubicacion_origen: 'Almacén Central',
          ubicacion_destino: 'Farmacia',
          estado: 'COMPLETADA',
          motivo: 'Reposición de stock',
          fecha_solicitud: '2024-01-15T08:00:00Z',
          fecha_autorizacion: '2024-01-15T08:30:00Z',
          fecha_completada: '2024-01-15T10:00:00Z',
          usuario_solicita_id: '1',
          usuario_autoriza_id: '2',
          usuario_recibe_id: '3',
          hospital_id: hospitalId,
          item_inventario: {
            codigo: 'MED001',
            nombre: 'Paracetamol 500mg',
            tipo_recurso: 'Medicamento',
            stock_actual: 100
          },
          usuario_solicita: { nombre: 'Dr. Juan Pérez' },
          usuario_autoriza: { nombre: 'Jefe Farmacia' },
          usuario_recibe: { nombre: 'Aux. Farmacia' }
        },
        {
          id: '2',
          item_inventario_id: '2',
          cantidad: 50,
          ubicacion_origen: 'Farmacia',
          ubicacion_destino: 'UCI',
          estado: 'PENDIENTE',
          motivo: 'Urgencia médica',
          fecha_solicitud: '2024-01-16T14:30:00Z',
          usuario_solicita_id: '4',
          hospital_id: hospitalId,
          item_inventario: {
            codigo: 'MAT001',
            nombre: 'Jeringa 5ml',
            tipo_recurso: 'Material Médico',
            stock_actual: 200
          },
          usuario_solicita: { nombre: 'Enf. María García' }
        },
        {
          id: '3',
          item_inventario_id: '3',
          cantidad: 10,
          ubicacion_origen: 'Almacén Central',
          ubicacion_destino: 'Quirófano 1',
          estado: 'EN_TRANSITO',
          motivo: 'Cirugía programada',
          fecha_solicitud: '2024-01-16T09:00:00Z',
          fecha_autorizacion: '2024-01-16T09:15:00Z',
          usuario_solicita_id: '5',
          usuario_autoriza_id: '2',
          hospital_id: hospitalId,
          item_inventario: {
            codigo: 'INS001',
            nombre: 'Guantes estériles',
            tipo_recurso: 'Insumo',
            stock_actual: 500
          },
          usuario_solicita: { nombre: 'Dr. Carlos López' },
          usuario_autoriza: { nombre: 'Jefe Almacén' }
        }
      ];
    }
  });

  // Query para obtener items de inventario
  const { data: itemsInventario } = useQuery({
    queryKey: ['items-inventario-transferencia', hospitalId],
    queryFn: async () => {
      return [
        { id: '1', codigo: 'MED001', nombre: 'Paracetamol 500mg', stock_actual: 100 },
        { id: '2', codigo: 'MAT001', nombre: 'Jeringa 5ml', stock_actual: 200 },
        { id: '3', codigo: 'INS001', nombre: 'Guantes estériles', stock_actual: 500 },
        { id: '4', codigo: 'MED002', nombre: 'Ibuprofeno 400mg', stock_actual: 75 }
      ];
    }
  });

  // Query para obtener ubicaciones
  const { data: ubicaciones } = useQuery({
    queryKey: ['ubicaciones-inventario', hospitalId],
    queryFn: async () => {
      return [
        'Almacén Central',
        'Farmacia',
        'UCI',
        'Urgencias',
        'Quirófano 1',
        'Quirófano 2',
        'Hospitalización',
        'Consulta Externa'
      ];
    }
  });

  // Mutación para crear transferencia
  const createTransferenciaMutation = useMutation({
    mutationFn: async (data: FormTransferencia) => {
      console.log('Creando transferencia:', data);
      return { id: Date.now().toString(), ...data };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transferencias-inventario'] });
      setIsModalOpen(false);
      resetForm();
    }
  });

  // Mutación para autorizar transferencia
  const autorizarTransferenciaMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log('Autorizando transferencia:', id);
      return { id };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transferencias-inventario'] });
    }
  });

  // Mutación para completar transferencia
  const completarTransferenciaMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log('Completando transferencia:', id);
      return { id };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transferencias-inventario'] });
    }
  });

  // Mutación para cancelar transferencia
  const cancelarTransferenciaMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log('Cancelando transferencia:', id);
      return { id };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transferencias-inventario'] });
    }
  });

  // Filtrar transferencias
  const filteredTransferencias = transferencias?.filter(trans => {
    const matchesSearch = 
      trans.item_inventario?.nombre.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trans.item_inventario?.codigo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trans.motivo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trans.ubicacion_origen.toLowerCase().includes(searchTerm.toLowerCase()) ||
      trans.ubicacion_destino.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesEstado = estadoFilter ? trans.estado === estadoFilter : true;

    const matchesUbicacion = ubicacionFilter ? 
      trans.ubicacion_origen === ubicacionFilter || trans.ubicacion_destino === ubicacionFilter : true;

    return matchesSearch && matchesEstado && matchesUbicacion;
  }) || [];

  // Manejar envío del formulario
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.item_inventario_id && formData.cantidad > 0 && 
        formData.ubicacion_origen && formData.ubicacion_destino) {
      createTransferenciaMutation.mutate(formData);
    }
  };

  // Resetear formulario
  const resetForm = () => {
    setFormData({
      item_inventario_id: '',
      cantidad: 0,
      ubicacion_origen: '',
      ubicacion_destino: '',
      motivo: '',
      observaciones: ''
    });
  };

  // Obtener color según estado
  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case 'PENDIENTE': return 'text-yellow-500';
      case 'EN_TRANSITO': return 'text-blue-500';
      case 'COMPLETADA': return 'text-green-500';
      case 'CANCELADA': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  // Obtener icono según estado
  const getEstadoIcon = (estado: string) => {
    switch (estado) {
      case 'PENDIENTE': return faClock;
      case 'EN_TRANSITO': return faExchangeAlt;
      case 'COMPLETADA': return faCheck;
      case 'CANCELADA': return faTimes;
      default: return faClock;
    }
  };

  // Formatear fecha
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('es-ES', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-white">Transferencias de Inventario</h1>
        <Button onClick={() => setIsModalOpen(true)}>
          <FontAwesomeIcon icon={faPlus} className="mr-2" />
          Nueva Transferencia
        </Button>
      </div>

      {/* Filtros */}
      <div className="glassmorphism p-4 mb-6 rounded-lg">
        <h2 className="text-xl font-semibold text-white mb-4">Filtros</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-white mb-1">Búsqueda</label>
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Buscar por item, motivo o ubicación"
                className="pl-10"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-white mb-1">Estado</label>
            <Select
              value={estadoFilter}
              onChange={(e) => setEstadoFilter(e.target.value)}
            >
              <option value="">Todos</option>
              <option value="PENDIENTE">Pendiente</option>
              <option value="EN_TRANSITO">En Tránsito</option>
              <option value="COMPLETADA">Completada</option>
              <option value="CANCELADA">Cancelada</option>
            </Select>
          </div>
          
          <div>
            <label className="block text-white mb-1">Ubicación</label>
            <Select
              value={ubicacionFilter}
              onChange={(e) => setUbicacionFilter(e.target.value)}
            >
              <option value="">Todas</option>
              {ubicaciones?.map(ubicacion => (
                <option key={ubicacion} value={ubicacion}>{ubicacion}</option>
              ))}
            </Select>
          </div>
        </div>
      </div>

      {/* Lista de transferencias */}
      <div className="bg-gray-900/70 backdrop-blur-sm rounded-lg overflow-hidden">
        {isLoading ? (
          <p className="text-white text-center py-4">Cargando transferencias...</p>
        ) : filteredTransferencias.length > 0 ? (
          <table className="min-w-full table-auto">
            <thead className="border-b border-gray-700">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Estado
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Item
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Cantidad
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Origen → Destino
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Motivo
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Solicitante
                </th>
                <th className="px-4 py-3 text-right text-sm font-medium text-gray-200 uppercase tracking-wider">
                  Acciones
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredTransferencias.map((transferencia) => (
                <tr key={transferencia.id} className="hover:bg-gray-800 transition-colors">
                  <td className="px-4 py-3 whitespace-nowrap text-sm">
                    <span className={`flex items-center ${getEstadoColor(transferencia.estado)}`}>
                      <FontAwesomeIcon icon={getEstadoIcon(transferencia.estado)} className="mr-2" />
                      {transferencia.estado.replace('_', ' ')}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm text-white">
                    <div>
                      <div className="font-medium">{transferencia.item_inventario?.nombre}</div>
                      <div className="text-gray-400 text-xs">{transferencia.item_inventario?.codigo}</div>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-white font-medium">
                    {transferencia.cantidad}
                  </td>
                  <td className="px-4 py-3 text-sm text-white">
                    <div className="flex items-center">
                      <FontAwesomeIcon icon={faMapMarkerAlt} className="mr-1 text-gray-400" />
                      <span className="text-blue-400">{transferencia.ubicacion_origen}</span>
                      <FontAwesomeIcon icon={faExchangeAlt} className="mx-2 text-gray-400" />
                      <span className="text-green-400">{transferencia.ubicacion_destino}</span>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm text-white">
                    {transferencia.motivo}
                    <div className="text-gray-400 text-xs">
                      {formatDate(transferencia.fecha_solicitud)}
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                    <FontAwesomeIcon icon={faUser} className="mr-2 text-gray-400" />
                    {transferencia.usuario_solicita?.nombre}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                    <div className="flex justify-end space-x-1">
                      {transferencia.estado === 'PENDIENTE' && (
                        <>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => autorizarTransferenciaMutation.mutate(transferencia.id)}
                            className="text-green-400 hover:text-green-300"
                          >
                            <FontAwesomeIcon icon={faCheck} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => cancelarTransferenciaMutation.mutate(transferencia.id)}
                            className="text-red-400 hover:text-red-300"
                          >
                            <FontAwesomeIcon icon={faTimes} />
                          </Button>
                        </>
                      )}
                      {transferencia.estado === 'EN_TRANSITO' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => completarTransferenciaMutation.mutate(transferencia.id)}
                          className="text-blue-400 hover:text-blue-300"
                        >
                          <FontAwesomeIcon icon={faCheck} />
                        </Button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p className="text-white text-center py-4">
            No se encontraron transferencias.
          </p>
        )}
      </div>

      {/* Modal para nueva transferencia */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Nueva Transferencia de Inventario"
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-white mb-1">Item de Inventario</label>
            <Select
              value={formData.item_inventario_id}
              onChange={(e) => setFormData({...formData, item_inventario_id: e.target.value})}
              required
            >
              <option value="">Seleccionar item</option>
              {itemsInventario?.map(item => (
                <option key={item.id} value={item.id}>
                  {item.codigo} - {item.nombre} (Stock: {item.stock_actual})
                </option>
              ))}
            </Select>
          </div>

          <div>
            <label className="block text-white mb-1">Cantidad</label>
            <Input
              type="number"
              min="1"
              value={formData.cantidad}
              onChange={(e) => setFormData({...formData, cantidad: parseInt(e.target.value) || 0})}
              required
            />
          </div>

          <div>
            <label className="block text-white mb-1">Ubicación Origen</label>
            <Select
              value={formData.ubicacion_origen}
              onChange={(e) => setFormData({...formData, ubicacion_origen: e.target.value})}
              required
            >
              <option value="">Seleccionar origen</option>
              {ubicaciones?.map(ubicacion => (
                <option key={ubicacion} value={ubicacion}>{ubicacion}</option>
              ))}
            </Select>
          </div>

          <div>
            <label className="block text-white mb-1">Ubicación Destino</label>
            <Select
              value={formData.ubicacion_destino}
              onChange={(e) => setFormData({...formData, ubicacion_destino: e.target.value})}
              required
            >
              <option value="">Seleccionar destino</option>
              {ubicaciones?.filter(ub => ub !== formData.ubicacion_origen).map(ubicacion => (
                <option key={ubicacion} value={ubicacion}>{ubicacion}</option>
              ))}
            </Select>
          </div>

          <div>
            <label className="block text-white mb-1">Motivo</label>
            <Input
              type="text"
              value={formData.motivo}
              onChange={(e) => setFormData({...formData, motivo: e.target.value})}
              placeholder="Ej: Reposición de stock, Urgencia médica"
              required
            />
          </div>

          <div>
            <label className="block text-white mb-1">Observaciones</label>
            <textarea
              className="w-full p-2 rounded bg-transparent border border-gray-300 text-white"
              rows={3}
              value={formData.observaciones}
              onChange={(e) => setFormData({...formData, observaciones: e.target.value})}
              placeholder="Observaciones adicionales (opcional)"
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="secondary"
              onClick={() => setIsModalOpen(false)}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={createTransferenciaMutation.isPending}>
              {createTransferenciaMutation.isPending ? 'Guardando...' : 'Crear Transferencia'}
            </Button>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default TransferenciasInventario;
