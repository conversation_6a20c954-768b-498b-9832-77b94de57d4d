import { useState, useEffect, ChangeEvent } from 'react';
import { Input } from '../../../components/ui/Input';
import { Button } from '../../../components/ui/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faSearch, 
  faTimes, 
  faSpinner,
  faExclamationCircle
} from '@fortawesome/free-solid-svg-icons';
import { useQuery } from '@tanstack/react-query';
import { cie10Service, DiagnosticoCIE10 } from '../../../services/cie10Service';
import { cie11Service, DiagnosticoCIE11 } from '../../../services/cie11Service';
import { useAuth } from '../../../context/AuthContext';
import { notificationService } from '../../../services/notificationService';

export interface DiagnosticoCIE {
  codigo: string;
  descripcion: string;
  version: 'CIE-10' | 'CIE-11';
}

// Adaptador para normalizar los diagnósticos de ambas versiones al formato común DiagnosticoCIE
const adaptarDiagnostico = (diag: DiagnosticoCIE10 | DiagnosticoCIE11, version: 'CIE-10' | 'CIE-11'): DiagnosticoCIE => {
  if (version === 'CIE-10') {
    const diagCIE10 = diag as DiagnosticoCIE10;
    return {
      codigo: diagCIE10.codigo,
      descripcion: diagCIE10.descripcion,
      version: 'CIE-10'
    };
  } else {
    const diagCIE11 = diag as DiagnosticoCIE11;
    return {
      codigo: diagCIE11.codigo,
      descripcion: diagCIE11.titulo, // Mapeo desde titulo a descripcion para CIE-11
      version: 'CIE-11'
    };
  }
};

interface DiagnosticoBusquedaSelectorProps {
  version?: 'CIE-10' | 'CIE-11' | 'AMBAS';
  onSelectDiagnostico: (diagnostico: DiagnosticoCIE) => void;
  placeholder?: string;
  label?: string;
  mostrarTransicion?: boolean;
}

export const DiagnosticoBusquedaSelector = ({
  version = 'AMBAS',
  onSelectDiagnostico,
  placeholder = 'Buscar por código o descripción...',
  label,
  mostrarTransicion = true
}: DiagnosticoBusquedaSelectorProps) => {
  const { user } = useAuth();
  const hospitalId = user?.hospital_id || 1;

  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [debouncedTerm, setDebouncedTerm] = useState('');
  const [versionActiva, setVersionActiva] = useState<'CIE-10' | 'CIE-11'>('CIE-11');

  // Debounce search term
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedTerm(searchTerm);
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm]);

  // Búsqueda de diagnósticos según la versión
  const { data: diagnosticos, isLoading, error } = useQuery({
    queryKey: ['diagnosticos-busqueda', version, versionActiva, debouncedTerm, hospitalId],
    queryFn: async () => {
      if (!debouncedTerm || debouncedTerm.length < 2) return [];

      const versionBusqueda = version === 'AMBAS' ? versionActiva : version;

      if (versionBusqueda === 'CIE-10') {
        const resultados = await cie10Service.buscarDiagnostico(debouncedTerm, hospitalId);
        return resultados.map(d => adaptarDiagnostico(d, 'CIE-10'));
      } else {
        // Corregido: usamos buscarDiagnosticos (con s) para CIE-11
        const resultados = await cie11Service.buscarDiagnosticos(debouncedTerm, hospitalId);
        return resultados.map(d => adaptarDiagnostico(d, 'CIE-11'));
      }
    },
    enabled: debouncedTerm.length >= 2,
  });

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    if (e.target.value.length >= 2) {
      setIsOpen(true);
    } else {
      setIsOpen(false);
    }
  };

  const handleSelectDiagnostico = (diagnostico: DiagnosticoCIE) => {
    onSelectDiagnostico(diagnostico);
    setSearchTerm('');
    setIsOpen(false);

    // Mostrar notificación cuando se selecciona un diagnóstico
    if (diagnostico.version === 'CIE-11') {
      notificationService.diagnosticoCIE11(
        diagnostico.codigo,
        diagnostico.descripcion,
        'seleccionado'
      );
    } else {
      // Notificación especial para CIE-10 indicando la transición
      notificationService.warning(
        `Código CIE-10 seleccionado: ${diagnostico.codigo}. Se recomienda migrar a CIE-11.`,
        { duration: 5000 }
      );
    }
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    setIsOpen(false);
  };

  return (
    <div className="relative">
      {label && (
        <label className="block text-sm font-medium text-white mb-1">
          {label}
        </label>
      )}

      {/* Selector de versión CIE si se permite ambas */}
      {version === 'AMBAS' && mostrarTransicion && (
        <div className="mb-3">
          <div className="flex space-x-2 mb-2">
            <Button
              type="button"
              variant={versionActiva === 'CIE-10' ? 'primary' : 'secondary'}
              size="sm"
              onClick={() => setVersionActiva('CIE-10')}
              className="flex-1"
            >
              CIE-10
              <FontAwesomeIcon icon={faExclamationCircle} className="ml-2 text-yellow-500" title="En transición" />
            </Button>
            <Button
              type="button"
              variant={versionActiva === 'CIE-11' ? 'primary' : 'secondary'}
              size="sm"
              onClick={() => setVersionActiva('CIE-11')}
              className="flex-1"
            >
              CIE-11
              <span className="ml-2 text-green-500">✓</span>
            </Button>
          </div>

          {/* Mensaje de transición */}
          {versionActiva === 'CIE-10' && (
            <div className="p-2 bg-yellow-900/30 border border-yellow-700 rounded-md text-yellow-200 text-xs">
              <FontAwesomeIcon icon={faExclamationCircle} className="mr-1" />
              CIE-10 será deprecado. Se recomienda usar CIE-11 para nuevos diagnósticos.
            </div>
          )}

          {versionActiva === 'CIE-11' && (
            <div className="p-2 bg-green-900/30 border border-green-700 rounded-md text-green-200 text-xs">
              <span className="mr-1">✓</span>
              CIE-11 es la versión recomendada y actual.
            </div>
          )}
        </div>
      )}

      <div className="relative">
        <Input
          value={searchTerm}
          onChange={handleInputChange}
          placeholder={placeholder}
          className="text-white pr-10"
          aria-label={`Buscar diagnóstico ${version === 'AMBAS' ? versionActiva : version}`}
        />

        {searchTerm && (
          <button
            type="button"
            onClick={handleClearSearch}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
            aria-label="Limpiar búsqueda"
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        )}
      </div>
      
      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-gray-800 border border-gray-700 rounded-md shadow-lg max-h-60 overflow-y-auto">
          {isLoading && (
            <div className="p-3 text-center text-gray-400">
              <FontAwesomeIcon icon={faSpinner} spin className="mr-2" />
              Buscando...
            </div>
          )}
          
          {error && (
            <div className="p-3 text-red-400 text-center">
              <FontAwesomeIcon icon={faExclamationCircle} className="mr-2" />
              Error en la búsqueda
            </div>
          )}
          
          {!isLoading && !error && diagnosticos?.length === 0 && debouncedTerm.length >= 2 && (
            <div className="p-3 text-center text-gray-400">
              No se encontraron resultados para "{debouncedTerm}"
            </div>
          )}
          
          {!isLoading && !error && diagnosticos && diagnosticos.length > 0 && (
            <ul>
              {diagnosticos.map((diag) => (
                <li
                  key={diag.codigo}
                  className="px-3 py-2 hover:bg-gray-700 cursor-pointer border-b border-gray-700 last:border-0"
                  onClick={() => handleSelectDiagnostico(diag)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium">
                        <span className="text-blue-400">{diag.codigo}</span> - {diag.descripcion}
                      </div>
                      <div className="flex items-center mt-1">
                        <span className={`px-2 py-1 text-xs rounded mr-2 ${
                          diag.version === 'CIE-11'
                            ? 'bg-green-900/50 text-green-300 border border-green-700'
                            : 'bg-yellow-900/50 text-yellow-300 border border-yellow-700'
                        }`}>
                          {diag.version}
                        </span>
                        {diag.version === 'CIE-10' && (
                          <span className="text-xs text-yellow-400">
                            <FontAwesomeIcon icon={faExclamationCircle} className="mr-1" />
                            En transición
                          </span>
                        )}
                        {diag.version === 'CIE-11' && (
                          <span className="text-xs text-green-400">
                            ✓ Recomendado
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}
    </div>
  );
};
