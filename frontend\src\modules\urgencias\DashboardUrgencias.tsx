import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { urgenciasService } from '../../services/urgenciasService';
import { NivelTriage, EstadoUrgencia } from '../../types/urgencias';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faUsers, 
  faClock, 
  faExclamationTriangle, 
  faHeartbeat,
  faChartLine,
  faUserMd,
  faBed,
  faStethoscope,
  faArrowUp,
  faArrowDown,
  faEquals
} from '@fortawesome/free-solid-svg-icons';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface EstadisticasUrgencias {
  total_pacientes: number;
  por_nivel_triage: Record<string, number>;
  por_estado: Record<string, number>;
  tiempo_promedio_triaje: number;
  tiempo_promedio_atencion: number;
  alertas_tiempo_excedido: number;
  camas_ocupadas: number;
  camas_disponibles: number;
  pacientes_criticos: number;
  tendencia_ingresos: 'subiendo' | 'bajando' | 'estable';
}

interface AlertaUrgencia {
  id: string;
  paciente_nombre: string;
  nivel_triage: NivelTriage;
  tiempo_espera: number;
  tiempo_objetivo: number;
  tipo: 'TIEMPO_EXCEDIDO' | 'DETERIORO_CLINICO' | 'ALTA_PRIORIDAD';
  mensaje: string;
}

export const DashboardUrgencias = () => {
  const [alertas, setAlertas] = useState<AlertaUrgencia[]>([]);
  const [horaActualizacion, setHoraActualizacion] = useState(new Date());

  // Obtener estadísticas de urgencias
  const { data: estadisticas, isLoading, refetch } = useQuery({
    queryKey: ['estadisticas-urgencias'],
    queryFn: async (): Promise<EstadisticasUrgencias> => {
      // Simular datos del dashboard (en producción vendría del backend)
      const urgencias = await urgenciasService.getUrgencias(1);
      
      const stats: EstadisticasUrgencias = {
        total_pacientes: urgencias.length,
        por_nivel_triage: {
          'NIVEL_1': urgencias.filter(u => u.nivel_triage === 'NIVEL_1').length,
          'NIVEL_2': urgencias.filter(u => u.nivel_triage === 'NIVEL_2').length,
          'NIVEL_3': urgencias.filter(u => u.nivel_triage === 'NIVEL_3').length,
          'NIVEL_4': urgencias.filter(u => u.nivel_triage === 'NIVEL_4').length,
          'NIVEL_5': urgencias.filter(u => u.nivel_triage === 'NIVEL_5').length,
          'SIN_TRIAJE': urgencias.filter(u => !u.nivel_triage).length,
        },
        por_estado: {
          'ESPERA_TRIAGE': urgencias.filter(u => u.estado === 'ESPERA_TRIAGE').length,
          'EN_TRIAGE': urgencias.filter(u => u.estado === 'EN_TRIAGE').length,
          'ESPERA_ATENCION': urgencias.filter(u => u.estado === 'ESPERA_ATENCION').length,
          'EN_ATENCION': urgencias.filter(u => u.estado === 'EN_ATENCION').length,
          'EN_OBSERVACION': urgencias.filter(u => u.estado === 'EN_OBSERVACION').length,
        },
        tiempo_promedio_triaje: Math.floor(Math.random() * 15) + 5, // 5-20 min
        tiempo_promedio_atencion: Math.floor(Math.random() * 30) + 15, // 15-45 min
        alertas_tiempo_excedido: Math.floor(Math.random() * 5),
        camas_ocupadas: Math.floor(Math.random() * 8) + 2, // 2-10
        camas_disponibles: 12 - (Math.floor(Math.random() * 8) + 2),
        pacientes_criticos: urgencias.filter(u => u.nivel_triage === 'NIVEL_1' || u.nivel_triage === 'NIVEL_2').length,
        tendencia_ingresos: ['subiendo', 'bajando', 'estable'][Math.floor(Math.random() * 3)] as any
      };

      return stats;
    },
    refetchInterval: 30000, // Actualizar cada 30 segundos
  });

  // Actualizar hora cada minuto
  useEffect(() => {
    const interval = setInterval(() => {
      setHoraActualizacion(new Date());
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  // Obtener color según nivel de triaje
  const getColorNivel = (nivel: string) => {
    switch (nivel) {
      case 'NIVEL_1': return 'bg-red-600 text-white';
      case 'NIVEL_2': return 'bg-orange-500 text-white';
      case 'NIVEL_3': return 'bg-yellow-500 text-white';
      case 'NIVEL_4': return 'bg-green-500 text-white';
      case 'NIVEL_5': return 'bg-blue-500 text-white';
      case 'SIN_TRIAJE': return 'bg-gray-600 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  // Obtener texto del nivel
  const getTextoNivel = (nivel: string) => {
    switch (nivel) {
      case 'NIVEL_1': return 'Nivel 1 - Crítico';
      case 'NIVEL_2': return 'Nivel 2 - Emergencia';
      case 'NIVEL_3': return 'Nivel 3 - Urgencia';
      case 'NIVEL_4': return 'Nivel 4 - Menor';
      case 'NIVEL_5': return 'Nivel 5 - No urgente';
      case 'SIN_TRIAJE': return 'Sin Triaje';
      default: return nivel;
    }
  };

  // Obtener icono de tendencia
  const getIconoTendencia = (tendencia: string) => {
    switch (tendencia) {
      case 'subiendo': return { icon: faArrowUp, color: 'text-red-500' };
      case 'bajando': return { icon: faArrowDown, color: 'text-green-500' };
      case 'estable': return { icon: faEquals, color: 'text-yellow-500' };
      default: return { icon: faEquals, color: 'text-gray-500' };
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header del Dashboard */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">Dashboard de Urgencias</h1>
          <p className="text-gray-300 mt-1">
            Última actualización: {format(horaActualizacion, 'HH:mm:ss', { locale: es })}
          </p>
        </div>
        <button
          onClick={() => refetch()}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          <FontAwesomeIcon icon={faChartLine} className="mr-2" />
          Actualizar
        </button>
      </div>

      {/* Métricas principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total de pacientes */}
        <div className="glassmorphism p-6 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Total Pacientes</p>
              <p className="text-3xl font-bold text-white">{estadisticas?.total_pacientes || 0}</p>
            </div>
            <div className="bg-blue-500 p-3 rounded-full">
              <FontAwesomeIcon icon={faUsers} className="text-white text-xl" />
            </div>
          </div>
          <div className="mt-4 flex items-center">
            <FontAwesomeIcon 
              icon={getIconoTendencia(estadisticas?.tendencia_ingresos || 'estable').icon} 
              className={`mr-2 ${getIconoTendencia(estadisticas?.tendencia_ingresos || 'estable').color}`} 
            />
            <span className="text-gray-300 text-sm">
              Tendencia: {estadisticas?.tendencia_ingresos || 'estable'}
            </span>
          </div>
        </div>

        {/* Pacientes críticos */}
        <div className="glassmorphism p-6 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Pacientes Críticos</p>
              <p className="text-3xl font-bold text-red-400">{estadisticas?.pacientes_criticos || 0}</p>
            </div>
            <div className="bg-red-500 p-3 rounded-full">
              <FontAwesomeIcon icon={faExclamationTriangle} className="text-white text-xl" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-gray-300 text-sm">Niveles 1 y 2</span>
          </div>
        </div>

        {/* Tiempo promedio de triaje */}
        <div className="glassmorphism p-6 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Tiempo Triaje</p>
              <p className="text-3xl font-bold text-yellow-400">{estadisticas?.tiempo_promedio_triaje || 0} min</p>
            </div>
            <div className="bg-yellow-500 p-3 rounded-full">
              <FontAwesomeIcon icon={faStethoscope} className="text-white text-xl" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-gray-300 text-sm">Promedio actual</span>
          </div>
        </div>

        {/* Camas disponibles */}
        <div className="glassmorphism p-6 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-300 text-sm">Camas Disponibles</p>
              <p className="text-3xl font-bold text-green-400">{estadisticas?.camas_disponibles || 0}</p>
            </div>
            <div className="bg-green-500 p-3 rounded-full">
              <FontAwesomeIcon icon={faBed} className="text-white text-xl" />
            </div>
          </div>
          <div className="mt-4">
            <span className="text-gray-300 text-sm">
              {estadisticas?.camas_ocupadas || 0} ocupadas
            </span>
          </div>
        </div>
      </div>

      {/* Distribución por nivel de triaje */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="glassmorphism p-6 rounded-lg">
          <h3 className="text-xl font-semibold text-white mb-4">
            <FontAwesomeIcon icon={faHeartbeat} className="mr-2 text-red-500" />
            Distribución por Nivel de Triaje
          </h3>
          <div className="space-y-3">
            {Object.entries(estadisticas?.por_nivel_triage || {}).map(([nivel, cantidad]) => (
              <div key={nivel} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <span className={`px-3 py-1 rounded-full text-sm font-bold ${getColorNivel(nivel)}`}>
                    {nivel === 'SIN_TRIAJE' ? '?' : nivel.replace('NIVEL_', '')}
                  </span>
                  <span className="text-gray-300">{getTextoNivel(nivel)}</span>
                </div>
                <span className="text-white font-bold text-lg">{cantidad}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Distribución por estado */}
        <div className="glassmorphism p-6 rounded-lg">
          <h3 className="text-xl font-semibold text-white mb-4">
            <FontAwesomeIcon icon={faClock} className="mr-2 text-blue-500" />
            Distribución por Estado
          </h3>
          <div className="space-y-3">
            {Object.entries(estadisticas?.por_estado || {}).map(([estado, cantidad]) => (
              <div key={estado} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    estado === 'ESPERA_TRIAGE' ? 'bg-gray-500' :
                    estado === 'EN_TRIAGE' ? 'bg-yellow-500' :
                    estado === 'ESPERA_ATENCION' ? 'bg-blue-500' :
                    estado === 'EN_ATENCION' ? 'bg-green-500' :
                    estado === 'EN_OBSERVACION' ? 'bg-purple-500' :
                    'bg-gray-400'
                  }`}></div>
                  <span className="text-gray-300">
                    {estado.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                </div>
                <span className="text-white font-bold text-lg">{cantidad}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Alertas y métricas avanzadas */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Alertas de tiempo */}
        <div className="lg:col-span-2 glassmorphism p-6 rounded-lg">
          <h3 className="text-xl font-semibold text-white mb-4">
            <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2 text-red-500" />
            Alertas de Tiempo Excedido
          </h3>

          {estadisticas?.alertas_tiempo_excedido === 0 ? (
            <div className="text-center py-8">
              <FontAwesomeIcon icon={faHeartbeat} className="text-green-500 text-4xl mb-4" />
              <p className="text-green-400 font-semibold">¡Todos los pacientes dentro de tiempo objetivo!</p>
              <p className="text-gray-400 text-sm mt-2">No hay alertas de tiempo excedido</p>
            </div>
          ) : (
            <div className="space-y-3">
              {/* Simulación de alertas */}
              {Array.from({ length: estadisticas?.alertas_tiempo_excedido || 0 }, (_, i) => (
                <div key={i} className="bg-red-900 bg-opacity-30 border border-red-500 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="bg-red-500 p-2 rounded-full">
                        <FontAwesomeIcon icon={faClock} className="text-white text-sm" />
                      </div>
                      <div>
                        <p className="text-white font-semibold">Paciente #{1000 + i}</p>
                        <p className="text-red-300 text-sm">
                          Nivel {Math.floor(Math.random() * 3) + 1} - Tiempo excedido: {15 + Math.floor(Math.random() * 30)} min
                        </p>
                      </div>
                    </div>
                    <button className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm transition-colors">
                      Atender
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Métricas de rendimiento */}
        <div className="glassmorphism p-6 rounded-lg">
          <h3 className="text-xl font-semibold text-white mb-4">
            <FontAwesomeIcon icon={faChartLine} className="mr-2 text-green-500" />
            Métricas de Rendimiento
          </h3>

          <div className="space-y-4">
            {/* Tiempo promedio de atención */}
            <div className="bg-gray-800 bg-opacity-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-gray-300 text-sm">Tiempo Atención</span>
                <span className="text-white font-bold">{estadisticas?.tiempo_promedio_atencion || 0} min</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min((estadisticas?.tiempo_promedio_atencion || 0) / 60 * 100, 100)}%` }}
                ></div>
              </div>
              <span className="text-gray-400 text-xs">Objetivo: &lt; 60 min</span>
            </div>

            {/* Eficiencia de triaje */}
            <div className="bg-gray-800 bg-opacity-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-gray-300 text-sm">Eficiencia Triaje</span>
                <span className="text-white font-bold">
                  {Math.round((1 - (estadisticas?.tiempo_promedio_triaje || 0) / 20) * 100)}%
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.round((1 - (estadisticas?.tiempo_promedio_triaje || 0) / 20) * 100)}%` }}
                ></div>
              </div>
              <span className="text-gray-400 text-xs">Basado en tiempo objetivo</span>
            </div>

            {/* Ocupación de camas */}
            <div className="bg-gray-800 bg-opacity-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-gray-300 text-sm">Ocupación Camas</span>
                <span className="text-white font-bold">
                  {Math.round(((estadisticas?.camas_ocupadas || 0) / 12) * 100)}%
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    ((estadisticas?.camas_ocupadas || 0) / 12) > 0.8 ? 'bg-red-500' :
                    ((estadisticas?.camas_ocupadas || 0) / 12) > 0.6 ? 'bg-yellow-500' :
                    'bg-green-500'
                  }`}
                  style={{ width: `${Math.round(((estadisticas?.camas_ocupadas || 0) / 12) * 100)}%` }}
                ></div>
              </div>
              <span className="text-gray-400 text-xs">
                {estadisticas?.camas_ocupadas || 0} de 12 camas
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Gráfico de tendencias por hora */}
      <div className="glassmorphism p-6 rounded-lg">
        <h3 className="text-xl font-semibold text-white mb-4">
          <FontAwesomeIcon icon={faChartLine} className="mr-2 text-blue-500" />
          Tendencias de Ingresos por Hora
        </h3>

        <div className="grid grid-cols-12 gap-2 h-32 items-end">
          {Array.from({ length: 12 }, (_, i) => {
            const altura = Math.floor(Math.random() * 80) + 20;
            const hora = i + 8; // 8 AM a 7 PM
            return (
              <div key={i} className="flex flex-col items-center">
                <div
                  className="bg-blue-500 rounded-t transition-all duration-300 hover:bg-blue-400 w-full"
                  style={{ height: `${altura}%` }}
                  title={`${hora}:00 - ${Math.floor(altura / 10)} pacientes`}
                ></div>
                <span className="text-gray-400 text-xs mt-2">{hora}h</span>
              </div>
            );
          })}
        </div>

        <div className="mt-4 flex justify-between text-sm text-gray-400">
          <span>8:00 AM</span>
          <span>Ingresos por hora</span>
          <span>7:00 PM</span>
        </div>
      </div>

      {/* Acciones rápidas */}
      <div className="glassmorphism p-6 rounded-lg">
        <h3 className="text-xl font-semibold text-white mb-4">
          <FontAwesomeIcon icon={faUserMd} className="mr-2 text-purple-500" />
          Acciones Rápidas
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button className="bg-red-600 hover:bg-red-700 text-white p-4 rounded-lg transition-colors flex items-center justify-center space-x-2">
            <FontAwesomeIcon icon={faExclamationTriangle} />
            <span>Emergencias Activas</span>
          </button>

          <button className="bg-yellow-600 hover:bg-yellow-700 text-white p-4 rounded-lg transition-colors flex items-center justify-center space-x-2">
            <FontAwesomeIcon icon={faStethoscope} />
            <span>Pendientes Triaje</span>
          </button>

          <button className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg transition-colors flex items-center justify-center space-x-2">
            <FontAwesomeIcon icon={faBed} />
            <span>Gestionar Camas</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default DashboardUrgencias;
