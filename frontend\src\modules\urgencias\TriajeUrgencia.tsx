import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { urgenciasService } from '../../services/urgenciasService';
import { pacientesService } from '../../services/pacientesService';
import { profesionalesService } from '../../services/profesionalesService';
import { NivelTriage, EstadoUrgencia } from '../../types/urgencias';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Textarea } from '../../components/ui/Textarea';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faArrowLeft,
  faSave,
  faExclamationTriangle,
  faHeartbeat,
  faThermometerHalf,
  faLungs,
  faTachometerAlt,
  faEye,
  faClock
} from '@fortawesome/free-solid-svg-icons';
import { notificationService } from '../../services/notificationService';

// Esquema de validación para el triaje
const triageSchema = z.object({
  motivo_consulta: z.string().min(10, 'El motivo debe tener al menos 10 caracteres'),
  sintomas_principales: z.string().min(5, 'Describa los síntomas principales'),
  tiempo_inicio_sintomas: z.string().min(1, 'Indique cuándo iniciaron los síntomas'),
  dolor_escala: z.number().min(0).max(10),
  glasgow_escala: z.number().min(3).max(15).default(15),

  // Signos vitales
  temperatura: z.number().min(30).max(45).optional(),
  presion_sistolica: z.number().min(60).max(250).optional(),
  presion_diastolica: z.number().min(40).max(150).optional(),
  frecuencia_cardiaca: z.number().min(30).max(200).optional(),
  frecuencia_respiratoria: z.number().min(8).max(60).optional(),
  saturacion_oxigeno: z.number().min(70).max(100).optional(),

  // Evaluación clínica
  via_aerea_comprometida: z.boolean().default(false),
  dificultad_respiratoria: z.boolean().default(false),
  shock_hemorragia: z.boolean().default(false),
  alteracion_conciencia: z.boolean().default(false),
  dolor_severo: z.boolean().default(false),

  // Factores de riesgo
  alergias_conocidas: z.string().optional(),
  medicamentos_actuales: z.string().optional(),
  antecedentes_relevantes: z.string().optional(),

  profesional_triage_id: z.string().min(1, 'Debe asignar un profesional'),
  notas_adicionales: z.string().optional()
});

type TriageFormData = z.infer<typeof triageSchema>;

export const TriajeUrgencia = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const [nivelCalculado, setNivelCalculado] = useState<NivelTriage | null>(null);
  const [tiempoObjetivo, setTiempoObjetivo] = useState<number>(0);

  // Obtener datos de la urgencia
  const { data: urgencia, isLoading: loadingUrgencia } = useQuery({
    queryKey: ['urgencia', id],
    queryFn: () => urgenciasService.getUrgenciaById(id!, 1),
    enabled: !!id
  });

  // Obtener profesionales para triaje
  const { data: profesionales } = useQuery({
    queryKey: ['profesionales-triaje'],
    queryFn: () => profesionalesService.getProfesionales(1, { especialidad: 'ENFERMERIA' })
  });

  // Configurar formulario
  const { register, handleSubmit, watch, setValue, formState: { errors } } = useForm<TriageFormData>({
    resolver: zodResolver(triageSchema),
    defaultValues: {
      dolor_escala: 0,
      glasgow_escala: 15,
      via_aerea_comprometida: false,
      dificultad_respiratoria: false,
      shock_hemorragia: false,
      alteracion_conciencia: false,
      dolor_severo: false
    }
  });

  // Observar cambios para calcular nivel de triaje automáticamente
  const watchedValues = watch();

  // Algoritmo de clasificación Manchester
  const calcularNivelTriaje = (datos: Partial<TriageFormData>): { nivel: NivelTriage; tiempo: number; razon: string } => {
    // NIVEL 1 - Resucitación (Inmediato)
    if (
      datos.via_aerea_comprometida ||
      (datos.saturacion_oxigeno && datos.saturacion_oxigeno < 85) ||
      (datos.presion_sistolica && datos.presion_sistolica < 80) ||
      (datos.glasgow_escala && datos.glasgow_escala < 9) ||
      datos.shock_hemorragia
    ) {
      return { nivel: NivelTriage.NIVEL_1, tiempo: 0, razon: 'Compromiso vital inmediato' };
    }

    // NIVEL 2 - Emergencia (<15 min)
    if (
      datos.dificultad_respiratoria ||
      (datos.saturacion_oxigeno && datos.saturacion_oxigeno < 90) ||
      (datos.presion_sistolica && datos.presion_sistolica < 90) ||
      (datos.glasgow_escala && datos.glasgow_escala < 12) ||
      (datos.temperatura && datos.temperatura > 39.5) ||
      (datos.frecuencia_cardiaca && (datos.frecuencia_cardiaca > 120 || datos.frecuencia_cardiaca < 50)) ||
      datos.dolor_escala >= 8
    ) {
      return { nivel: NivelTriage.NIVEL_2, tiempo: 15, razon: 'Emergencia que requiere atención inmediata' };
    }

    // NIVEL 3 - Urgencia (<30 min)
    if (
      datos.alteracion_conciencia ||
      (datos.temperatura && datos.temperatura > 38.5) ||
      (datos.presion_sistolica && datos.presion_sistolica > 180) ||
      (datos.frecuencia_cardiaca && datos.frecuencia_cardiaca > 100) ||
      datos.dolor_escala >= 6 ||
      datos.dolor_severo
    ) {
      return { nivel: NivelTriage.NIVEL_3, tiempo: 30, razon: 'Urgencia que requiere atención prioritaria' };
    }

    // NIVEL 4 - Urgencia menor (<60 min)
    if (
      (datos.temperatura && datos.temperatura > 37.8) ||
      datos.dolor_escala >= 4 ||
      (datos.presion_sistolica && datos.presion_sistolica > 160)
    ) {
      return { nivel: NivelTriage.NIVEL_4, tiempo: 60, razon: 'Urgencia menor, puede esperar' };
    }

    // NIVEL 5 - No urgente (<120 min)
    return { nivel: NivelTriage.NIVEL_5, tiempo: 120, razon: 'No urgente, consulta programable' };
  };

  // Recalcular nivel cuando cambien los valores
  useEffect(() => {
    const resultado = calcularNivelTriaje(watchedValues);
    setNivelCalculado(resultado.nivel);
    setTiempoObjetivo(resultado.tiempo);
  }, [watchedValues]);

  // Mutación para realizar triaje
  const triageMutation = useMutation({
    mutationFn: async (data: TriageFormData) => {
      if (!id || !nivelCalculado) throw new Error('Datos incompletos');

      return urgenciasService.realizarTriage({
        id,
        nivel_triage: nivelCalculado,
        profesional_triage_id: data.profesional_triage_id,
        signos_vitales: {
          temperatura: data.temperatura,
          presion_sistolica: data.presion_sistolica,
          presion_diastolica: data.presion_diastolica,
          frecuencia_cardiaca: data.frecuencia_cardiaca,
          frecuencia_respiratoria: data.frecuencia_respiratoria,
          saturacion_oxigeno: data.saturacion_oxigeno,
          escala_dolor: data.dolor_escala,
          glasgow: data.glasgow_escala
        },
        notas: `${data.motivo_consulta}\n\nSíntomas: ${data.sintomas_principales}\n\nTiempo de inicio: ${data.tiempo_inicio_sintomas}\n\nNotas adicionales: ${data.notas_adicionales || 'N/A'}`
      });
    },
    onSuccess: () => {
      notificationService.success('Triaje realizado exitosamente');
      queryClient.invalidateQueries({ queryKey: ['urgencias'] });
      queryClient.invalidateQueries({ queryKey: ['urgencia', id] });
      navigate('/urgencias');
    },
    onError: (error) => {
      console.error('Error al realizar triaje:', error);
      notificationService.error('Error al realizar el triaje');
    }
  });

  const onSubmit = (data: TriageFormData) => {
    triageMutation.mutate(data);
  };

  // Obtener color según nivel de triaje
  const getColorNivel = (nivel: NivelTriage) => {
    switch (nivel) {
      case NivelTriage.NIVEL_1: return 'bg-red-600 text-white';
      case NivelTriage.NIVEL_2: return 'bg-orange-500 text-white';
      case NivelTriage.NIVEL_3: return 'bg-yellow-500 text-white';
      case NivelTriage.NIVEL_4: return 'bg-green-500 text-white';
      case NivelTriage.NIVEL_5: return 'bg-blue-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  // Obtener texto del nivel
  const getTextoNivel = (nivel: NivelTriage) => {
    switch (nivel) {
      case NivelTriage.NIVEL_1: return 'NIVEL 1 - RESUCITACIÓN';
      case NivelTriage.NIVEL_2: return 'NIVEL 2 - EMERGENCIA';
      case NivelTriage.NIVEL_3: return 'NIVEL 3 - URGENCIA';
      case NivelTriage.NIVEL_4: return 'NIVEL 4 - URGENCIA MENOR';
      case NivelTriage.NIVEL_5: return 'NIVEL 5 - NO URGENTE';
      default: return 'EVALUANDO...';
    }
  };

  if (loadingUrgencia) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!urgencia) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600">Urgencia no encontrada</p>
        <Button onClick={() => navigate('/urgencias')} className="mt-4">
          Volver a Urgencias
        </Button>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            onClick={() => navigate('/urgencias')}
            className="mr-4"
          >
            <FontAwesomeIcon icon={faArrowLeft} />
          </Button>
          <h1 className="text-2xl font-bold text-white">
            Triaje de Urgencia - {urgencia.paciente?.nombre_completo}
          </h1>
        </div>

        {nivelCalculado && (
          <div className={`px-4 py-2 rounded-lg font-bold ${getColorNivel(nivelCalculado)}`}>
            {getTextoNivel(nivelCalculado)}
            <div className="text-sm mt-1">
              <FontAwesomeIcon icon={faClock} className="mr-1" />
              Tiempo objetivo: {tiempoObjetivo === 0 ? 'INMEDIATO' : `${tiempoObjetivo} min`}
            </div>
          </div>
        )}
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="glassmorphism p-6 rounded-lg">
          {/* Información del paciente */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Información del Paciente</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Nombre:</span>
                <p className="text-gray-800">{urgencia.paciente?.nombre_completo}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Documento:</span>
                <p className="text-gray-800">{urgencia.paciente?.tipo_documento} {urgencia.paciente?.numero_documento}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Edad:</span>
                <p className="text-gray-800">{urgencia.paciente?.edad} años</p>
              </div>
            </div>
          </div>

          {/* Evaluación inicial */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Motivo y síntomas */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 border-b border-gray-300 pb-2">
                Evaluación Inicial
              </h3>

              <div>
                <label className="block text-sm font-medium text-gray-800 mb-2">
                  Motivo de consulta *
                </label>
                <Textarea
                  {...register('motivo_consulta')}
                  rows={3}
                  placeholder="Describa el motivo principal de la consulta..."
                  className="w-full"
                />
                {errors.motivo_consulta && (
                  <p className="text-red-500 text-xs mt-1">{errors.motivo_consulta.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-800 mb-2">
                  Síntomas principales *
                </label>
                <Textarea
                  {...register('sintomas_principales')}
                  rows={3}
                  placeholder="Liste los síntomas más relevantes..."
                  className="w-full"
                />
                {errors.sintomas_principales && (
                  <p className="text-red-500 text-xs mt-1">{errors.sintomas_principales.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-800 mb-2">
                  Tiempo de inicio de síntomas *
                </label>
                <Input
                  {...register('tiempo_inicio_sintomas')}
                  placeholder="Ej: 2 horas, 1 día, 3 semanas..."
                  className="w-full"
                />
                {errors.tiempo_inicio_sintomas && (
                  <p className="text-red-500 text-xs mt-1">{errors.tiempo_inicio_sintomas.message}</p>
                )}
              </div>
            </div>

            {/* Escalas de evaluación */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-800 border-b border-gray-300 pb-2">
                Escalas de Evaluación
              </h3>

              <div>
                <label className="block text-sm font-medium text-gray-800 mb-2">
                  <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2 text-red-500" />
                  Escala de dolor (0-10)
                </label>
                <Input
                  type="number"
                  min="0"
                  max="10"
                  {...register('dolor_escala', { valueAsNumber: true })}
                  className="w-full"
                />
                <div className="text-xs text-gray-600 mt-1">
                  0 = Sin dolor, 10 = Dolor insoportable
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-800 mb-2">
                  <FontAwesomeIcon icon={faEye} className="mr-2 text-blue-500" />
                  Escala de Glasgow (3-15)
                </label>
                <Input
                  type="number"
                  min="3"
                  max="15"
                  {...register('glasgow_escala', { valueAsNumber: true })}
                  className="w-full"
                />
                <div className="text-xs text-gray-600 mt-1">
                  15 = Consciente, 3 = Coma profundo
                </div>
              </div>
            </div>
          </div>

          {/* Signos vitales */}
          <div className="mt-6 p-4 bg-green-50 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 border-b border-gray-300 pb-2 mb-4">
              <FontAwesomeIcon icon={faHeartbeat} className="mr-2 text-red-500" />
              Signos Vitales
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-800 mb-2">
                  <FontAwesomeIcon icon={faThermometerHalf} className="mr-2 text-orange-500" />
                  Temperatura (°C)
                </label>
                <Input
                  type="number"
                  step="0.1"
                  min="30"
                  max="45"
                  {...register('temperatura', { valueAsNumber: true })}
                  placeholder="36.5"
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-800 mb-2">
                  <FontAwesomeIcon icon={faTachometerAlt} className="mr-2 text-red-500" />
                  Presión sistólica (mmHg)
                </label>
                <Input
                  type="number"
                  min="60"
                  max="250"
                  {...register('presion_sistolica', { valueAsNumber: true })}
                  placeholder="120"
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-800 mb-2">
                  Presión diastólica (mmHg)
                </label>
                <Input
                  type="number"
                  min="40"
                  max="150"
                  {...register('presion_diastolica', { valueAsNumber: true })}
                  placeholder="80"
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-800 mb-2">
                  <FontAwesomeIcon icon={faHeartbeat} className="mr-2 text-red-500" />
                  Frecuencia cardíaca (lpm)
                </label>
                <Input
                  type="number"
                  min="30"
                  max="200"
                  {...register('frecuencia_cardiaca', { valueAsNumber: true })}
                  placeholder="70"
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-800 mb-2">
                  <FontAwesomeIcon icon={faLungs} className="mr-2 text-blue-500" />
                  Frecuencia respiratoria (rpm)
                </label>
                <Input
                  type="number"
                  min="8"
                  max="60"
                  {...register('frecuencia_respiratoria', { valueAsNumber: true })}
                  placeholder="16"
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-800 mb-2">
                  Saturación O₂ (%)
                </label>
                <Input
                  type="number"
                  min="70"
                  max="100"
                  {...register('saturacion_oxigeno', { valueAsNumber: true })}
                  placeholder="98"
                  className="w-full"
                />
              </div>
            </div>
          </div>

          {/* Evaluación clínica crítica */}
          <div className="mt-6 p-4 bg-red-50 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 border-b border-gray-300 pb-2 mb-4">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2 text-red-500" />
              Evaluación Clínica Crítica
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    {...register('via_aerea_comprometida')}
                    className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500"
                  />
                  <span className="text-sm font-medium text-gray-800">
                    Vía aérea comprometida
                  </span>
                </label>

                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    {...register('dificultad_respiratoria')}
                    className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500"
                  />
                  <span className="text-sm font-medium text-gray-800">
                    Dificultad respiratoria severa
                  </span>
                </label>

                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    {...register('shock_hemorragia')}
                    className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500"
                  />
                  <span className="text-sm font-medium text-gray-800">
                    Shock o hemorragia activa
                  </span>
                </label>
              </div>

              <div className="space-y-3">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    {...register('alteracion_conciencia')}
                    className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500"
                  />
                  <span className="text-sm font-medium text-gray-800">
                    Alteración del estado de conciencia
                  </span>
                </label>

                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    {...register('dolor_severo')}
                    className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500"
                  />
                  <span className="text-sm font-medium text-gray-800">
                    Dolor severo incontrolable
                  </span>
                </label>
              </div>
            </div>
          </div>

          {/* Información adicional */}
          <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-800 mb-2">
                Alergias conocidas
              </label>
              <Textarea
                {...register('alergias_conocidas')}
                rows={2}
                placeholder="Liste alergias conocidas del paciente..."
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-800 mb-2">
                Medicamentos actuales
              </label>
              <Textarea
                {...register('medicamentos_actuales')}
                rows={2}
                placeholder="Medicamentos que toma actualmente..."
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-800 mb-2">
                Antecedentes relevantes
              </label>
              <Textarea
                {...register('antecedentes_relevantes')}
                rows={2}
                placeholder="Antecedentes médicos relevantes..."
                className="w-full"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-800 mb-2">
                Profesional de triaje *
              </label>
              <select
                {...register('profesional_triage_id')}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Seleccione un profesional</option>
                {profesionales?.map((profesional) => (
                  <option key={profesional.id} value={profesional.id}>
                    {profesional.nombre_completo} - {profesional.especialidad}
                  </option>
                ))}
              </select>
              {errors.profesional_triage_id && (
                <p className="text-red-500 text-xs mt-1">{errors.profesional_triage_id.message}</p>
              )}
            </div>
          </div>

          {/* Notas adicionales */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-800 mb-2">
              Notas adicionales del triaje
            </label>
            <Textarea
              {...register('notas_adicionales')}
              rows={3}
              placeholder="Observaciones adicionales del profesional de triaje..."
              className="w-full"
            />
          </div>

          {/* Botones de acción */}
          <div className="mt-8 flex justify-between items-center">
            <Button
              type="button"
              variant="ghost"
              onClick={() => navigate('/urgencias')}
            >
              Cancelar
            </Button>

            <Button
              type="submit"
              disabled={triageMutation.isPending}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2"
            >
              {triageMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Guardando...
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faSave} className="mr-2" />
                  Completar Triaje
                </>
              )}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default TriajeUrgencia;
