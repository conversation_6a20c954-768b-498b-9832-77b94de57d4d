import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery, useMutation } from "@tanstack/react-query";
import { urgenciasService } from "../../services/urgenciasService";
import { Button } from "../../components/ui/Button";
import { Input } from "../../components/ui/Input";
import { Select } from "../../components/ui/Select";
import { Textarea } from "../../components/ui/Textarea";
import { DiagnosticoBusquedaSelector, DiagnosticoCIE } from "../pacientes/components/DiagnosticoBusquedaSelector";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowLeft, faSave, faExclamationTriangle, faInfoCircle, faStethoscope } from "@fortawesome/free-solid-svg-icons";
import { notificationService } from "../../services/notificationService";

export const UrgenciaEditar = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditing = !!id;

  // Estado del formulario
  const [formData, setFormData] = useState({
    paciente_id: "",
    tipo_documento_identificacion: "CC",
    num_documento_identificacion: "",
    fecha_ingreso: "",
    triage_nivel: 3,
    estado: "En Observación",
    notas: "",
    diagnostico_principal: "",
    diagnostico_id: "",
    diagnostico_codigo: "",
    diagnostico_descripcion: "",
    diagnostico_version: "CIE-11",
    codigo_cups: "",
    numero_autorizacion: "",
  });

  // Estado para manejo de diagnóstico seleccionado
  const [diagnosticoSeleccionado, setDiagnosticoSeleccionado] = useState<DiagnosticoCIE | null>(null);

  // Obtener detalles de la urgencia si estamos editando
  const { data: urgencia, isLoading: isLoadingUrgencia } = useQuery({
    queryKey: ["urgencia", id],
    queryFn: () => urgenciasService.getUrgenciaById(id || "", 1),
    enabled: isEditing,
  });

  // Cargar datos de la urgencia en el formulario
  useEffect(() => {
    if (urgencia) {
      // Usar casting para acceder a las propiedades que no están en el tipo Urgencia
      const urgenciaData = urgencia as any;
      
      setFormData({
        paciente_id: urgenciaData.paciente_id,
        tipo_documento_identificacion: urgenciaData.tipo_documento_identificacion || "CC",
        num_documento_identificacion: urgenciaData.num_documento_identificacion || "",
        fecha_ingreso: urgenciaData.fecha_ingreso.split(".")[0], // Eliminar milisegundos si existen
        triage_nivel: urgenciaData.triage_nivel || 3,
        estado: urgenciaData.estado || "En Observación",
        notas: urgenciaData.notas || "",
        diagnostico_principal: urgenciaData.diagnostico_principal || "",
        diagnostico_id: urgenciaData.diagnostico_id || "",
        diagnostico_codigo: urgenciaData.diagnostico_codigo || "",
        diagnostico_descripcion: urgenciaData.diagnostico_descripcion || "",
        diagnostico_version: urgenciaData.diagnostico_version || "CIE-11",
        codigo_cups: urgenciaData.codigo_cups || "",
        numero_autorizacion: urgenciaData.numero_autorizacion || "",
      });
    }
  }, [urgencia]);

  // Mutación para crear/actualizar urgencia
  const mutation = useMutation({
    mutationFn: (data: any) => {
      if (isEditing) {
        return urgenciasService.updateUrgencia(id || "", data, 1);
      } else {
        return urgenciasService.createUrgencia(data, 1);
      }
    },
    onSuccess: () => {
      navigate("/urgencias");
    },
  });

  // Manejar cambios en el formulario
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "triage_nivel" ? parseInt(value) : value,
    }));
  };

  // Manejar selección de diagnóstico
  const handleSelectDiagnostico = (diagnostico: DiagnosticoCIE) => {
    setDiagnosticoSeleccionado(diagnostico);
    setFormData(prev => ({
      ...prev,
      diagnostico_id: diagnostico.codigo,
      diagnostico_codigo: diagnostico.codigo,
      diagnostico_descripcion: diagnostico.descripcion,
      diagnostico_principal: `${diagnostico.codigo} - ${diagnostico.descripcion}`,
      diagnostico_version: diagnostico.version
    }));

    // Mostrar notificación según la versión
    if (diagnostico.version === 'CIE-11') {
      notificationService.success(
        `Diagnóstico CIE-11 seleccionado: ${diagnostico.codigo}`,
        { duration: 3000 }
      );
    } else {
      notificationService.warning(
        `Código CIE-10 seleccionado: ${diagnostico.codigo}. Se recomienda migrar a CIE-11.`,
        { duration: 5000 }
      );
    }
  };

  // Manejar envío del formulario
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    mutation.mutate(formData);
  };

  if (isEditing && isLoadingUrgencia) {
    return (
      <div className="p-6">
        <p className="text-white text-center">Cargando...</p>
      </div>
    );
  }

  return (
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <Button
              variant="ghost"
              onClick={() => navigate("/urgencias")}
              className="mr-4"
            >
              <FontAwesomeIcon icon={faArrowLeft} />
            </Button>
            <h1 className="text-2xl font-bold text-white">
              {isEditing ? "Editar Urgencia" : "Nueva Urgencia"}
            </h1>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="glassmorphism p-6 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Información del paciente */}
            <div>
              <h2 className="text-xl font-semibold text-white mb-4">
                Información del Paciente
              </h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-white mb-1">
                    ID del Paciente *
                  </label>
                  <Input
                    name="paciente_id"
                    value={formData.paciente_id}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-white mb-1">
                      Tipo de Documento *
                    </label>
                    <Select
                      name="tipo_documento_identificacion"
                      value={formData.tipo_documento_identificacion}
                      onChange={handleChange}
                      required
                    >
                      <option value="CC">Cédula de Ciudadanía</option>
                      <option value="TI">Tarjeta de Identidad</option>
                      <option value="CE">Cédula de Extranjería</option>
                      <option value="PA">Pasaporte</option>
                      <option value="RC">Registro Civil</option>
                    </Select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-white mb-1">
                      Número de Documento *
                    </label>
                    <Input
                      name="num_documento_identificacion"
                      value={formData.num_documento_identificacion}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Información de la urgencia */}
            <div>
              <h2 className="text-xl font-semibold text-white mb-4">
                Información de la Urgencia
              </h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-white mb-1">
                    Fecha y Hora de Ingreso *
                  </label>
                  <Input
                    type="datetime-local"
                    name="fecha_ingreso"
                    value={formData.fecha_ingreso}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-white mb-1">
                    Nivel de Triage *
                  </label>
                  <Select
                    name="triage_nivel"
                    value={formData.triage_nivel.toString()}
                    onChange={handleChange}
                    required
                  >
                    <option value="1">Nivel 1 - Resucitación</option>
                    <option value="2">Nivel 2 - Emergencia</option>
                    <option value="3">Nivel 3 - Urgente</option>
                    <option value="4">Nivel 4 - Menos Urgente</option>
                    <option value="5">Nivel 5 - No Urgente</option>
                  </Select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-white mb-1">
                    Estado *
                  </label>
                  <Select
                    name="estado"
                    value={formData.estado}
                    onChange={handleChange}
                    required
                  >
                    <option value="En Observación">En Observación</option>
                    <option value="Alta">Alta</option>
                    <option value="Hospitalizado">Hospitalizado</option>
                    <option value="Crítico">Crítico</option>
                  </Select>
                </div>
              </div>
            </div>

            {/* Información clínica */}
            <div>
              <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
                <FontAwesomeIcon icon={faStethoscope} className="mr-2 text-blue-500" />
                Información Clínica
              </h2>

              {/* Mensaje de transición CIE */}
              <div className="mb-4 p-3 bg-blue-900/30 border border-blue-700 rounded-md">
                <div className="flex items-start">
                  <FontAwesomeIcon icon={faInfoCircle} className="text-blue-400 mr-2 mt-0.5" />
                  <div className="text-sm text-blue-200">
                    <p className="font-medium">Transición CIE-10 → CIE-11</p>
                    <p>El sistema está en proceso de transición. Se recomienda usar códigos CIE-11 para nuevos diagnósticos de urgencias.</p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-white mb-2">
                    Diagnóstico Principal de Urgencia
                  </label>
                  <DiagnosticoBusquedaSelector
                    version="AMBAS"
                    onSelectDiagnostico={handleSelectDiagnostico}
                    placeholder="Buscar diagnóstico de urgencia..."
                    label=""
                    mostrarTransicion={true}
                  />

                  {/* Mostrar diagnóstico seleccionado */}
                  {diagnosticoSeleccionado && (
                    <div className="mt-2 p-3 bg-gray-800/50 border border-gray-600 rounded-md">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <span className="font-mono text-blue-400 mr-2">
                            {diagnosticoSeleccionado.codigo}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded mr-2 ${
                            diagnosticoSeleccionado.version === 'CIE-11'
                              ? 'bg-green-900/50 text-green-300 border border-green-700'
                              : 'bg-yellow-900/50 text-yellow-300 border border-yellow-700'
                          }`}>
                            {diagnosticoSeleccionado.version}
                          </span>
                        </div>
                        {diagnosticoSeleccionado.version === 'CIE-10' && (
                          <span className="text-xs text-yellow-400">
                            <FontAwesomeIcon icon={faExclamationTriangle} className="mr-1" />
                            En transición
                          </span>
                        )}
                      </div>
                      <p className="text-white text-sm mt-1">{diagnosticoSeleccionado.descripcion}</p>
                    </div>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-white mb-1">
                    Código CUPS
                  </label>
                  <Input
                    name="codigo_cups"
                    value={formData.codigo_cups}
                    onChange={handleChange}
                    placeholder="Ej. 890201"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-white mb-1">
                    Número de Autorización
                  </label>
                  <Input
                    name="numero_autorizacion"
                    value={formData.numero_autorizacion}
                    onChange={handleChange}
                  />
                </div>
              </div>
            </div>

            {/* Notas */}
            <div>
              <h2 className="text-xl font-semibold text-white mb-4">Notas</h2>
              <div>
                <Textarea
                  name="notas"
                  value={formData.notas}
                  onChange={handleChange}
                  rows={5}
                  placeholder="Ingrese notas sobre la urgencia..."
                />
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate("/urgencias")}
              className="mr-2"
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              disabled={mutation.isPending}
            >
              <FontAwesomeIcon icon={faSave} className="mr-2" />
              {mutation.isPending
                ? "Guardando..."
                : isEditing
                ? "Actualizar"
                : "Guardar"}
            </Button>
          </div>
        </form>
      </div>
  );
};
