import { useState, useRef } from "react";
import { useQuery } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import { es } from "date-fns/locale";
import { urgenciasService } from "../../services/urgenciasService";
import { Urgencia } from "../../types/urgencias";
import { Button } from "../../components/ui/Button";
import { Input } from "../../components/ui/Input";
import { Select } from "../../components/ui/Select";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPlus, faSearch, faEdit, faTrash, faEye, faStethoscope, faClock, faExclamationTriangle } from "@fortawesome/free-solid-svg-icons";

export const Urgencias = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [estadoFilter, setEstadoFilter] = useState<string>("");
  const [triageFilter, setTriageFilter] = useState<string>("");
  const [selectedUrgencia, setSelectedUrgencia] = useState<Urgencia | null>(null);
  const [showTooltip, setShowTooltip] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0, showAbove: false });

  // Obtener lista de urgencias
  const { data: urgencias, isLoading, isError, refetch } = useQuery({
    queryKey: ["urgencias", estadoFilter, triageFilter],
    queryFn: () => urgenciasService.getUrgencias(1, {
      estado: estadoFilter as any || undefined,
      triage_nivel: triageFilter ? parseInt(triageFilter) : undefined
    }),
  });

  // Filtrar urgencias por término de búsqueda
  const filteredUrgencias = Array.isArray(urgencias)
    ? urgencias.filter((urgencia) => {
        const searchTermLower = searchTerm.toLowerCase();
        return (
          urgencia.id?.toLowerCase().includes(searchTermLower) ||
          urgencia.paciente?.numero_documento?.toLowerCase().includes(searchTermLower) ||
          urgencia.paciente?.nombre_completo?.toLowerCase().includes(searchTermLower) ||
          urgencia.motivo?.toLowerCase().includes(searchTermLower)
        );
      })
    : [];

  // Manejar la eliminación de una urgencia
  const handleDelete = async (id: string) => {
    if (window.confirm("¿Está seguro de eliminar este registro de urgencia?")) {
      try {
        await urgenciasService.deleteUrgencia(id);
        refetch();
      } catch (error) {
        console.error("Error al eliminar la urgencia:", error);
      }
    }
  };

  // Formatear fecha
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd/MM/yyyy HH:mm", { locale: es });
    } catch (error) {
      return dateString;
    }
  };

  // Obtener color según nivel de triage
  const getTriageColor = (nivel: number) => {
    switch (nivel) {
      case 1:
        return "text-red-500 font-bold";
      case 2:
        return "text-orange-500 font-bold";
      case 3:
        return "text-yellow-500 font-bold";
      case 4:
        return "text-blue-500";
      case 5:
        return "text-green-500";
      default:
        return "";
    }
  };

  // Obtener color según estado
  const getEstadoColor = (estado: string) => {
    switch (estado) {
      case "En Observación":
        return "text-yellow-500";
      case "Alta":
        return "text-green-500";
      case "Hospitalizado":
        return "text-blue-500";
      case "Crítico":
        return "text-red-500 font-bold";
      default:
        return "";
    }
  };

  // Obtener texto legible del estado
  const getEstadoText = (estado: string) => {
    return estado; // Ya no necesitamos convertir el estado porque ya viene en formato legible
  };

  // Manejar el mouse enter en una fila
  const handleRowMouseEnter = (urgencia: Urgencia, event: React.MouseEvent) => {
    setSelectedUrgencia(urgencia);
    setShowTooltip(true);

    // Calcular la posición del tooltip
    const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
    const tooltipHeight = 100; // Altura estimada del tooltip
    const windowHeight = window.innerHeight;
    const viewportBottom = window.scrollY + windowHeight;
    const rowMiddle = rect.top + (rect.height / 2);
    const viewportMiddle = window.scrollY + (windowHeight / 2);

    // Determinar si debe mostrarse arriba o abajo basado en la posición de la fila en la ventana
    const showAbove = rowMiddle > viewportMiddle;

    setTooltipPosition({
      x: rect.left,
      y: showAbove ? rect.top - 5 : rect.bottom + 5,
      showAbove: showAbove
    });
  };

  // Manejar el mouse leave en una fila
  const handleRowMouseLeave = () => {
    setShowTooltip(false);
  };

  return (
    <>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">Urgencias</h1>
          <Button onClick={() => navigate("/urgencias/nuevo")}>
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            Nueva Urgencia
          </Button>
        </div>

        {/* Filtros */}
        <div className="glassmorphism p-4 mb-6 rounded-lg">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-white mb-1">
                Buscar
              </label>
              <div className="relative">
                <Input
                  type="text"
                  placeholder="Buscar por documento, consecutivo..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
                <FontAwesomeIcon
                  icon={faSearch}
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                />
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-white mb-1">
                Estado
              </label>
              <Select
                value={estadoFilter}
                onChange={(e) => setEstadoFilter(e.target.value)}
              >
                <option value="">Todos</option>
                <option value="En Observación">En Observación</option>
                <option value="Alta">Alta</option>
                <option value="Hospitalizado">Hospitalizado</option>
                <option value="Crítico">Crítico</option>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium text-white mb-1">
                Nivel de Triage
              </label>
              <Select
                value={triageFilter}
                onChange={(e) => setTriageFilter(e.target.value)}
              >
                <option value="">Todos</option>
                <option value="1">Nivel 1 - Resucitación</option>
                <option value="2">Nivel 2 - Emergencia</option>
                <option value="3">Nivel 3 - Urgente</option>
                <option value="4">Nivel 4 - Menos Urgente</option>
                <option value="5">Nivel 5 - No Urgente</option>
              </Select>
            </div>
          </div>
        </div>

        {/* Tabla de urgencias */}
        <div className="glassmorphism p-4 rounded-lg overflow-x-auto">
          {isLoading ? (
            <p className="text-white text-center py-4">Cargando...</p>
          ) : isError ? (
            <p className="text-red-400 text-center py-4">
              Error al cargar los datos. Intente nuevamente.
            </p>
          ) : Array.isArray(filteredUrgencias) && filteredUrgencias.length > 0 ? (
            <table className="min-w-full divide-y divide-gray-700">
              <thead>
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Consecutivo
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Documento
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Fecha Ingreso
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Triage
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Estado
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Diagnóstico
                  </th>
                  <th className="px-4 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                    Acciones
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {filteredUrgencias.map((urgencia: Urgencia) => (
                  <tr
                    key={urgencia.id}
                    className="hover:bg-gray-800"
                    onMouseEnter={(e) => handleRowMouseEnter(urgencia, e)}
                    onMouseLeave={handleRowMouseLeave}
                  >
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                      URG-{urgencia.id.slice(-6)}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                      {urgencia.paciente?.tipo_documento} {urgencia.paciente?.numero_documento}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                      {formatDate(urgencia.fecha_ingreso)}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm">
                      {urgencia.nivel_triage ? (
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                            urgencia.nivel_triage === 'NIVEL_1' ? 'bg-red-600 text-white' :
                            urgencia.nivel_triage === 'NIVEL_2' ? 'bg-orange-500 text-white' :
                            urgencia.nivel_triage === 'NIVEL_3' ? 'bg-yellow-500 text-white' :
                            urgencia.nivel_triage === 'NIVEL_4' ? 'bg-green-500 text-white' :
                            urgencia.nivel_triage === 'NIVEL_5' ? 'bg-blue-500 text-white' :
                            'bg-gray-500 text-white'
                          }`}>
                            {urgencia.nivel_triage.replace('NIVEL_', '')}
                          </span>
                          <span className="text-gray-300 text-xs">
                            {urgencia.nivel_triage === 'NIVEL_1' ? 'INMEDIATO' :
                             urgencia.nivel_triage === 'NIVEL_2' ? '15 min' :
                             urgencia.nivel_triage === 'NIVEL_3' ? '30 min' :
                             urgencia.nivel_triage === 'NIVEL_4' ? '60 min' :
                             urgencia.nivel_triage === 'NIVEL_5' ? '120 min' : ''}
                          </span>
                        </div>
                      ) : (
                        <span className="px-2 py-1 rounded-full text-xs bg-gray-600 text-white flex items-center">
                          <FontAwesomeIcon icon={faExclamationTriangle} className="mr-1" />
                          SIN TRIAJE
                        </span>
                      )}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm">
                      <span className={getEstadoColor(urgencia.estado)}>
                        {getEstadoText(urgencia.estado)}
                      </span>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-white">
                      {urgencia.motivo || "-"}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-right">
                      {/* Botón de triaje - solo si no tiene triaje o está en espera */}
                      {(!urgencia.nivel_triage || urgencia.estado === 'ESPERA_TRIAGE') && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => navigate(`/urgencias/triaje/${urgencia.id}`)}
                          className="mr-2 text-orange-500 hover:text-orange-400"
                          title="Realizar Triaje"
                        >
                          <FontAwesomeIcon icon={faStethoscope} />
                        </Button>
                      )}

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigate(`/urgencias/${urgencia.id}`)}
                        className="mr-2"
                        title="Ver Detalle"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigate(`/urgencias/editar/${urgencia.id}`)}
                        className="mr-2"
                        title="Editar"
                      >
                        <FontAwesomeIcon icon={faEdit} />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(urgencia.id)}
                        className="text-red-500 hover:text-red-400"
                        title="Eliminar"
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <p className="text-white text-center py-4">
              No se encontraron registros de urgencias.
            </p>
          )}
        </div>
      </div>

    {/* Tooltip para mostrar el nombre del paciente */}
    {showTooltip && selectedUrgencia && selectedUrgencia.paciente && (
      <div
          ref={tooltipRef}
          className={`fixed z-50 bg-gray-800 text-white p-3 rounded shadow-lg border border-gray-700 tooltip-container ${tooltipPosition.showAbove ? 'tooltip-above' : 'tooltip-below'}`}
          style={{
            left: `${tooltipPosition.x}px`,
            top: tooltipPosition.showAbove ? 'auto' : `${tooltipPosition.y}px`,
            bottom: tooltipPosition.showAbove ? `calc(100vh - ${tooltipPosition.y}px)` : 'auto',
          }}
        >
          <p className="text-sm font-medium">
            <span className="font-bold">Paciente:</span> {selectedUrgencia.paciente.nombre_completo}
          </p>
        </div>
      )}
    </>
  );
};