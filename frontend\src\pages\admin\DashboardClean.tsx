import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// Iconos SVG
const UserIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
  </svg>
);

const CalendarIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

const CurrencyIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
  </svg>
);

const VideoIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
  </svg>
);

const TrendUpIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
  </svg>
);

const ArrowRightIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
  </svg>
);

const AlertIcon = ({ className }: { className?: string }) => (
  <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
  </svg>
);

export const Dashboard = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);

  // Datos simulados
  const stats = {
    pacientes: { total: 1247, cambio: 12.5 },
    citas: { total: 89, cambio: 8.3 },
    ingresos: { total: 2450000, cambio: 15.7 },
    teleconsultas: { total: 23, cambio: 22.1 }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('es-CO', {
      style: 'currency',
      currency: 'COP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-white">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <p className="text-gray-700 text-lg font-medium">Cargando dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Bienvenido de vuelta, aquí tienes un resumen de tu hospital
          </p>
        </div>
      </div>

      {/* Estadísticas principales */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Pacientes */}
        <div
          className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200 cursor-pointer group shadow-sm"
          onClick={() => navigate('/pacientes')}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-blue-100 rounded-lg">
                <UserIcon className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-600">Pacientes</h3>
                <p className="text-2xl font-bold text-gray-900">{stats.pacientes.total.toLocaleString()}</p>
              </div>
            </div>
            <ArrowRightIcon className="h-5 w-5 text-gray-400 group-hover:text-blue-600 transition-colors" />
          </div>
          <div className="mt-4 flex items-center space-x-2">
            <TrendUpIcon className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-600">
              +{stats.pacientes.cambio}%
            </span>
            <span className="text-sm text-gray-500">este mes</span>
          </div>
        </div>

        {/* Citas */}
        <div
          className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200 cursor-pointer group shadow-sm"
          onClick={() => navigate('/citas')}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-green-100 rounded-lg">
                <CalendarIcon className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-600">Citas Hoy</h3>
                <p className="text-2xl font-bold text-gray-900">{stats.citas.total}</p>
              </div>
            </div>
            <ArrowRightIcon className="h-5 w-5 text-gray-400 group-hover:text-green-600 transition-colors" />
          </div>
          <div className="mt-4 flex items-center space-x-2">
            <TrendUpIcon className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-600">
              +{stats.citas.cambio}%
            </span>
            <span className="text-sm text-gray-500">vs ayer</span>
          </div>
        </div>

        {/* Ingresos */}
        <div
          className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200 cursor-pointer group shadow-sm"
          onClick={() => navigate('/financiero')}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-yellow-100 rounded-lg">
                <CurrencyIcon className="h-6 w-6 text-yellow-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-600">Ingresos</h3>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.ingresos.total)}</p>
              </div>
            </div>
            <ArrowRightIcon className="h-5 w-5 text-gray-400 group-hover:text-yellow-600 transition-colors" />
          </div>
          <div className="mt-4 flex items-center space-x-2">
            <TrendUpIcon className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-600">
              +{stats.ingresos.cambio}%
            </span>
            <span className="text-sm text-gray-500">este mes</span>
          </div>
        </div>

        {/* Teleconsultas */}
        <div
          className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-all duration-200 cursor-pointer group shadow-sm"
          onClick={() => navigate('/telemedicina')}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-purple-100 rounded-lg">
                <VideoIcon className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-600">Teleconsultas</h3>
                <p className="text-2xl font-bold text-gray-900">{stats.teleconsultas.total}</p>
              </div>
            </div>
            <ArrowRightIcon className="h-5 w-5 text-gray-400 group-hover:text-purple-600 transition-colors" />
          </div>
          <div className="mt-4 flex items-center space-x-2">
            <TrendUpIcon className="h-4 w-4 text-green-600" />
            <span className="text-sm font-medium text-green-600">
              +{stats.teleconsultas.cambio}%
            </span>
            <span className="text-sm text-gray-500">esta semana</span>
          </div>
        </div>
      </div>

      {/* Alertas */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="bg-white border border-orange-200 rounded-lg p-4 shadow-sm">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <AlertIcon className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Stock Bajo</h4>
              <p className="text-sm text-gray-600">3 medicamentos requieren reposición</p>
            </div>
          </div>
        </div>

        <div className="bg-white border border-blue-200 rounded-lg p-4 shadow-sm">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <CalendarIcon className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Citas Pendientes</h4>
              <p className="text-sm text-gray-600">12 citas por confirmar hoy</p>
            </div>
          </div>
        </div>

        <div className="bg-white border border-red-200 rounded-lg p-4 shadow-sm">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 rounded-lg">
              <CurrencyIcon className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Facturas Vencidas</h4>
              <p className="text-sm text-gray-600">5 facturas requieren seguimiento</p>
            </div>
          </div>
        </div>
      </div>

      {/* Accesos rápidos */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Accesos Rápidos</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
          <button
            onClick={() => navigate('/pacientes/nuevo')}
            className="flex items-center space-x-3 p-3 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors group border border-blue-200"
          >
            <UserIcon className="h-5 w-5 text-blue-600" />
            <span className="text-sm font-medium text-blue-700">Nuevo Paciente</span>
            <ArrowRightIcon className="h-4 w-4 text-blue-600 ml-auto group-hover:translate-x-1 transition-transform" />
          </button>

          <button
            onClick={() => navigate('/citas/nueva')}
            className="flex items-center space-x-3 p-3 rounded-lg bg-green-50 hover:bg-green-100 transition-colors group border border-green-200"
          >
            <CalendarIcon className="h-5 w-5 text-green-600" />
            <span className="text-sm font-medium text-green-700">Agendar Cita</span>
            <ArrowRightIcon className="h-4 w-4 text-green-600 ml-auto group-hover:translate-x-1 transition-transform" />
          </button>

          <button
            onClick={() => navigate('/telemedicina/nueva')}
            className="flex items-center space-x-3 p-3 rounded-lg bg-purple-50 hover:bg-purple-100 transition-colors group border border-purple-200"
          >
            <VideoIcon className="h-5 w-5 text-purple-600" />
            <span className="text-sm font-medium text-purple-700">Teleconsulta</span>
            <ArrowRightIcon className="h-4 w-4 text-purple-600 ml-auto group-hover:translate-x-1 transition-transform" />
          </button>

          <button
            onClick={() => navigate('/inventario')}
            className="flex items-center space-x-3 p-3 rounded-lg bg-orange-50 hover:bg-orange-100 transition-colors group border border-orange-200"
          >
            <AlertIcon className="h-5 w-5 text-orange-600" />
            <span className="text-sm font-medium text-orange-700">Ver Inventario</span>
            <ArrowRightIcon className="h-4 w-4 text-orange-600 ml-auto group-hover:translate-x-1 transition-transform" />
          </button>
        </div>
      </div>
    </div>
  );
};
