import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Layout } from '../components/layout/Layout';
import NotFound from '../pages/NotFound';
import { useAuth } from '../context/AuthContext';

import { Login } from '../pages/auth/Login';
import { Dashboard } from '../pages/admin/DashboardClean';
import Pacientes from '../modules/pacientes/Pacientes';
import PacienteNuevo from '../modules/pacientes/PacienteNuevo';
import PacienteEditar from '../modules/pacientes/PacienteEditar';
import PacienteDetalle from '../modules/pacientes/PacienteDetalle';
import AnaliticaPredictiva from '../modules/analitica-predictiva/AnaliticaPredictiva';
import ProveedoresYCompras from '../modules/proveedores-y-compras/ProveedoresYCompras';
import { HistoriasClinicas } from '../modules/historias-clinicas/HistoriasClinicas';
import { HistoriaClinicaNueva } from '../modules/historias-clinicas/HistoriaClinicaNueva';
import { HistoriaClinicaDetalle } from '../modules/historias-clinicas/HistoriaClinicaDetalle';
import { ConfiguracionCIE11 } from '../modules/configuraciones/ConfiguracionCIE11';
import { EstadisticasCIE11 } from '../modules/reportes/EstadisticasCIE11';
import { Consultas } from '../modules/consultas/Consultas';
import { ConsultaNueva } from '../modules/consultas/ConsultaNueva';
import { ConsultaEditar } from '../modules/consultas/ConsultaEditar';
import { ConsultaDetalle } from '../modules/consultas/ConsultaDetalle';
// Importaciones de Dispensaciones
import Dispensaciones from '../modules/dispensaciones/Dispensaciones';
import DispensacionDetalle from '../modules/dispensaciones/DispensacionDetalle';
import DispensacionForm from '../modules/dispensaciones/DispensacionForm';

// Componente de carga
const Loading = () => (
  <div className="flex items-center justify-center h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500" />
  </div>
);

// Ruta protegida
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuth();
  if (isLoading) return <Loading />;
  if (!user) return <Navigate to="/login" replace />;
  return <>{children}</>;
};

const AppRoutes = () => (
  <Routes>
    <Route path="/login" element={<Login />} />
    <Route path="/" element={<Navigate to="/dashboard" replace />} />

    <Route
      path="/dashboard"
      element={
        <ProtectedRoute>
          <Layout>
            <Dashboard />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/pacientes"
      element={
        <ProtectedRoute>
          <Layout>
            <Pacientes />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/analitica-predictiva"
      element={
        <ProtectedRoute>
          <Layout>
            <AnaliticaPredictiva />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/proveedores-y-compras"
      element={
        <ProtectedRoute>
          <Layout>
            <ProveedoresYCompras />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Historias Clínicas */}
    <Route
      path="/historias-clinicas"
      element={
        <ProtectedRoute>
          <Layout>
            <HistoriasClinicas />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/historias-clinicas/nueva"
      element={
        <ProtectedRoute>
          <Layout>
            <HistoriaClinicaNueva />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/historias-clinicas/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <HistoriaClinicaDetalle />
          </Layout>
        </ProtectedRoute>
      }
    />

        {/* Rutas de Pacientes */}
    <Route
      path="/pacientes/nuevo"
      element={
        <ProtectedRoute>
          <Layout>
            <PacienteNuevo />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/pacientes/:id/editar"
      element={
        <ProtectedRoute>
          <Layout>
            <PacienteEditar />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/pacientes/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <PacienteDetalle />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Consultas */}
    <Route
      path="/consultas"
      element={
        <ProtectedRoute>
          <Layout>
            <Consultas />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/consultas/nueva"
      element={
        <ProtectedRoute>
          <Layout>
            <ConsultaNueva />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/consultas/:id/editar"
      element={
        <ProtectedRoute>
          <Layout>
            <ConsultaEditar />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/consultas/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <ConsultaDetalle />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Configuración CIE-11 */}
    <Route
      path="/configuraciones/cie11"
      element={
        <ProtectedRoute>
          <Layout>
            <ConfiguracionCIE11 />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Estadísticas CIE-11 */}
    <Route
      path="/reportes/cie11"
      element={
        <ProtectedRoute>
          <Layout>
            <EstadisticasCIE11 />
          </Layout>
        </ProtectedRoute>
      }
    />

    {/* Rutas de Dispensaciones */}
    <Route
      path="/dispensaciones"
      element={
        <ProtectedRoute>
          <Layout>
            <Dispensaciones />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/dispensaciones/nueva"
      element={
        <ProtectedRoute>
          <Layout>
            <DispensacionForm />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/dispensaciones/editar/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <DispensacionForm />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route
      path="/dispensaciones/:id"
      element={
        <ProtectedRoute>
          <Layout>
            <DispensacionDetalle />
          </Layout>
        </ProtectedRoute>
      }
    />

    <Route path="*" element={<NotFound />} />
  </Routes>
);

export default AppRoutes;
