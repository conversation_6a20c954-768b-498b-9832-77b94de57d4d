import { Routes, Route, Outlet, Navigate } from 'react-router-dom';
import { Layout } from '../components/layout/Layout';
import NotFound from '../pages/NotFound';
import React, { Suspense, lazy } from 'react';
import { ErrorBoundary } from '../components/ErrorBoundary';

// Componente de carga
const Loading = () => (
  <div className="flex items-center justify-center h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>
);

// Lazy loading de componentes
// Auth
const Login = lazy(() => import('../pages/auth/Login').then(module => ({ default: module.Login })));

// Admin
const Dashboard = lazy(() => import('../pages/admin/DashboardClean').then(module => ({ default: module.Dashboard })));
const Users = lazy(() => import('../pages/admin/Users').then(module => ({ default: module.Users })));
const Settings = lazy(() => import('../pages/admin/Settings').then(module => ({ default: module.Settings })));
const Roles = lazy(() => import('../pages/admin/Roles').then(module => ({ default: module.Roles })));

// Componente genérico para módulos en desarrollo
const ModuloEnDesarrollo = lazy(() => import('../components/ui/ModuloEnDesarrollo'));

// Módulos implementados
const Pacientes = lazy(() => import('../modules/pacientes'));
const FormularioPaciente = lazy(() => import('../modules/pacientes/FormularioPaciente').then(m => ({ default: m.FormularioPaciente })));
const PacienteDetalle = lazy(() => import('../modules/pacientes/PacienteDetalle'));

// Módulo de Dispensaciones
const Dispensaciones = lazy(() => import('../modules/dispensaciones'));
const DispensacionDetalle = lazy(() => import('../modules/dispensaciones/DispensacionDetalle'));
const DispensacionForm = lazy(() => import('../modules/dispensaciones/DispensacionForm'));

// Módulo de Citas
const Citas = lazy(() => import('../modules/citas/Citas'));
const CitaForm = lazy(() => import('../modules/citas/CitaForm'));
const CitaDetalle = lazy(() => import('../modules/citas/CitaDetalle'));

// Módulo de Consultas
const Consultas = lazy(() => import('../modules/consultas'));
const ConsultaForm = lazy(() => import('../modules/consultas/FormularioConsulta').then(module => ({ default: module.FormularioConsulta })));
const ConsultaDetalle = lazy(() => import('../modules/consultas/ConsultaDetalle').then(module => ({ default: module.ConsultaDetalle })));
const ConsultasReportes = lazy(() => import('../modules/consultas/ReportesConsultas').then(module => ({ default: module.ReportesConsultas })));

// Módulo de Quirófanos
const Quirofanos = lazy(() => import('../modules/quirofanos').then(module => ({ default: module.Quirofanos })));
const QuirofanoDetalle = lazy(() => import('../modules/quirofanos').then(module => ({ default: module.QuirofanoDetalle })));
const QuirofanoAsignacion = lazy(() => import('../modules/quirofanos').then(module => ({ default: module.QuirofanoAsignacion })));
const QuirofanoEstadisticas = lazy(() => import('../modules/quirofanos').then(module => ({ default: module.QuirofanoEstadisticas })));
const QuirofanoReserva = lazy(() => import('../modules/quirofanos').then(module => ({ default: module.QuirofanoReserva })));
const CirugiaDetalle = lazy(() => import('../modules/quirofanos').then(module => ({ default: module.CirugiaDetalle })));
const CirugiaLista = lazy(() => import('../modules/quirofanos').then(module => ({ default: module.CirugiaLista })));

// Módulo de Urgencias
const Urgencias = lazy(() => import('../modules/urgencias'));
const UrgenciaNueva = lazy(() => import('../modules/urgencias/UrgenciaNueva').then(module => ({ default: module.UrgenciaNueva })));
const UrgenciaDetalle = lazy(() => import('../modules/urgencias/UrgenciaDetalle').then(module => ({ default: module.UrgenciaDetalle })));
const UrgenciaEditar = lazy(() => import('../modules/urgencias/UrgenciaEditar').then(module => ({ default: module.UrgenciaEditar })));
const TriajeUrgencia = lazy(() => import('../modules/urgencias/TriajeUrgencia').then(module => ({ default: module.default })));
const Hospitalizaciones = lazy(() => import('../modules/hospitalizaciones'));
const HospitalizacionNueva = lazy(() => import('../modules/hospitalizaciones/HospitalizacionNueva').then(module => ({ default: module.HospitalizacionNueva })));
const HospitalizacionDetalle = lazy(() => import('../modules/hospitalizaciones/HospitalizacionDetalle').then(module => ({ default: module.HospitalizacionDetalle })));
const HospitalizacionEditar = lazy(() => import('../modules/hospitalizaciones/HospitalizacionEditar').then(module => ({ default: module.HospitalizacionEditar })));

// Módulo de Ambulancias
const Ambulancias = lazy(() => import('../modules/ambulancias').then(module => ({ default: module.Ambulancias })));
const AmbulanciasMapa = lazy(() => import('../modules/ambulancias').then(module => ({ default: module.AmbulanciasMapa })));
const HistoriasClinicas = lazy(() => import('../modules/historias-clinicas/HistoriasClinicas').then(module => ({ default: module.HistoriasClinicas })));
const HistoriaClinicaNueva = lazy(() => import('../modules/historias-clinicas/HistoriaClinicaNueva').then(module => ({ default: module.HistoriaClinicaNueva })));
const HistoriaClinicaDetalle = lazy(() => import('../modules/historias-clinicas/HistoriaClinicaDetalle').then(module => ({ default: module.HistoriaClinicaDetalle })));
const Facturacion = lazy(() => import('../modules/facturacion'));
const Medicamentos = lazy(() => import('../modules/medicamentos'));
const AnaliticaPredictiva = lazy(() => import('../modules/analitica-predictiva'));
const Activos = lazy(() => import('../modules/activos'));
const ActivoDetalle = lazy(() => import('../modules/activos/ActivoDetalle'));
const ActivoForm = lazy(() => import('../modules/activos/ActivoForm'));
const Contabilidad = lazy(() => import('../modules/contabilidad'));
const Inventario = lazy(() => import('../modules/inventario'));
const Presupuesto = lazy(() => import('../modules/presupuesto'));
const Reportes = lazy(() => import('../modules/reportes'));
const ResiduosHospitalarios = lazy(() => import('../modules/residuos-hospitalarios'));
const Proveedores = lazy(() => import('../modules/proveedores'));
const RRHH = lazy(() => import('../modules/recursosHumanos'));
const Autorizaciones = lazy(() => import('../modules/autorizaciones'));
const IncidentesAdversos = lazy(() => import('../modules/incidentes-adversos'));
const Teleconsultas = lazy(() => import('../modules/teleconsultas'));
const Cirugias = lazy(() => import('../modules/cirugias'));

// Módulo CIE-11
const ConfiguracionCIE11 = lazy(() => import('../modules/configuraciones/ConfiguracionCIE11').then(module => ({ default: module.ConfiguracionCIE11 })));
const EstadisticasCIE11 = lazy(() => import('../modules/reportes/EstadisticasCIE11').then(module => ({ default: module.EstadisticasCIE11 })));

// Componente de ruta protegida
interface ProtectedRouteProps {
  children: React.ReactNode;
}
const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  // Temporalmente, permitir acceso sin autenticación para depuración
  console.log('ProtectedRoute - Renderizando hijos sin verificar autenticación');
  return <>{children}</>;

  /*
  const auth = useAuth();

  console.log('ProtectedRoute - Contexto de autenticación completo:', auth);
  console.log('ProtectedRoute - user:', auth.user, 'isAuthenticated:', auth.isAuthenticated, 'isLoading:', auth.isLoading);

  if (auth.isLoading) {
    console.log('ProtectedRoute - Loading...');
    return <Loading />;
  }

  if (!auth.isAuthenticated) {
    console.log('ProtectedRoute - No autenticado, redirigiendo a /login');
    return <Navigate to="/login" replace />;
  }

  console.log('ProtectedRoute - Usuario autenticado, renderizando hijos');
  return <>{children}</>;
  */
};

const AppRoutes = () => (
  <Suspense fallback={<Loading />}>
    <Routes>
      {/* Rutas públicas */}
      <Route path="/login" element={<Login />} />

      {/* Rutas protegidas */}
      <Route path="/" element={
        <ProtectedRoute>
          <Layout>
            <Outlet />
          </Layout>
        </ProtectedRoute>
      }>
        {/* Ruta por defecto */}
        <Route index element={<Navigate to="/dashboard" />} />

        {/* Dashboard */}
        <Route path="/dashboard" element={<Dashboard />} />

        {/* Administración */}
        <Route path="/admin/users" element={<Users />} />
        <Route path="/admin/settings" element={<Settings />} />
        <Route path="/admin/roles" element={<Roles />} />

        {/* Módulos implementados */}
        {/* Pacientes */}
        <Route path="/pacientes" element={<Pacientes />} />
        <Route path="/pacientes/nuevo" element={<FormularioPaciente />} />
        <Route path="/pacientes/editar/:id" element={<FormularioPaciente />} />
        <Route path="/pacientes/:id" element={<PacienteDetalle />} />

        {/* Citas */}
        <Route path="/citas" element={<Citas />} />
        <Route path="/citas/nueva" element={<CitaForm />} />
        <Route path="/citas/:id" element={<CitaDetalle />} />
        <Route path="/pacientes/:id/citas/nueva" element={<CitaForm />} />

        {/* Módulo de Consultas */}
        <Route path="/consultas" element={<Consultas />} />
        <Route path="/consultas/nuevo" element={<ConsultaForm />} />
        <Route path="/consultas/:id" element={<ConsultaDetalle />} />
        <Route path="/consultas/editar/:id" element={<ConsultaForm />} />
        <Route path="/consultas/reportes" element={<ConsultasReportes />} />
        <Route path="/urgencias" element={<Urgencias />} />
        <Route path="/urgencias/nuevo" element={<UrgenciaNueva />} />
        <Route path="/urgencias/:id" element={<UrgenciaDetalle />} />
        <Route path="/urgencias/editar/:id" element={<UrgenciaEditar />} />
        <Route path="/urgencias/triaje/:id" element={<TriajeUrgencia />} />
        <Route path="/hospitalizaciones" element={<Hospitalizaciones />} />
        <Route path="/hospitalizaciones/nueva" element={<HospitalizacionNueva />} />
        <Route path="/hospitalizaciones/ver/:id" element={<HospitalizacionDetalle />} />
        <Route path="/hospitalizaciones/editar/:id" element={<HospitalizacionEditar />} />
        <Route path="/ambulancias" element={<Ambulancias />} />
        <Route path="/ambulancias/mapa" element={<AmbulanciasMapa />} />
        <Route path="/ambulancias/:id" element={<ModuloEnDesarrollo titulo="Detalle de Ambulancia" />} />
        <Route path="/ambulancias/editar/:id" element={<ModuloEnDesarrollo titulo="Editar Ambulancia" />} />
        <Route path="/historias-clinicas" element={<HistoriasClinicas />} />
        <Route path="/historias-clinicas/nueva" element={<HistoriaClinicaNueva />} />
        <Route path="/historias-clinicas/:id" element={<HistoriaClinicaDetalle />} />
        <Route path="/facturacion" element={<Facturacion />} />
        <Route path="/facturacion/nueva" element={<ModuloEnDesarrollo titulo="Nueva Factura" />} />
        <Route path="/facturacion/:id" element={<ModuloEnDesarrollo titulo="Detalle de Factura" />} />

        {/* Módulos implementados */}
        <Route path="/medicamentos" element={<Medicamentos />} />
        <Route path="/medicamentos/nuevo" element={<ModuloEnDesarrollo titulo="Nuevo Medicamento" />} />
        <Route path="/medicamentos/:id" element={<ModuloEnDesarrollo titulo="Detalle de Medicamento" />} />
        <Route path="/activos" element={<Activos />} />
        <Route path="/activos/:id" element={<ActivoDetalle />} />
        <Route path="/activos/nuevo" element={<ActivoForm />} />
        <Route path="/activos/editar/:id" element={<ActivoForm />} />
        <Route path="/analitica-predictiva" element={<AnaliticaPredictiva />} />
        <Route path="/analitica-predictiva/nuevo" element={<ModuloEnDesarrollo titulo="Nuevo Análisis Predictivo" />} />
        <Route path="/analitica-predictiva/:id" element={<ModuloEnDesarrollo titulo="Detalle de Análisis Predictivo" />} />
        <Route path="/contabilidad" element={<Contabilidad />} />
        <Route path="/contabilidad/nuevo" element={<ModuloEnDesarrollo titulo="Nuevo Registro Contable" />} />
        <Route path="/contabilidad/:id" element={<ModuloEnDesarrollo titulo="Detalle de Registro Contable" />} />
        <Route path="/presupuesto" element={<Presupuesto />} />
        <Route path="/presupuesto/nuevo" element={<ModuloEnDesarrollo titulo="Nuevo Presupuesto" />} />
        <Route path="/presupuesto/:id" element={<ModuloEnDesarrollo titulo="Detalle de Presupuesto" />} />
        <Route path="/reportes" element={<Reportes />} />
        <Route path="/reportes/nuevo" element={<ModuloEnDesarrollo titulo="Nuevo Reporte" />} />
        <Route path="/reportes/:id" element={<ModuloEnDesarrollo titulo="Detalle de Reporte" />} />
        <Route path="/residuos-hospitalarios" element={<ResiduosHospitalarios />} />
        <Route path="/residuos-hospitalarios/nuevo" element={<ModuloEnDesarrollo titulo="Nuevo Registro de Residuos" />} />
        <Route path="/residuos-hospitalarios/:id" element={<ModuloEnDesarrollo titulo="Detalle de Registro de Residuos" />} />
        <Route path="/proveedores" element={<Proveedores />} />
        <Route path="/proveedores/nuevo" element={<ModuloEnDesarrollo titulo="Nuevo Proveedor" />} />
        <Route path="/proveedores/:id" element={<ModuloEnDesarrollo titulo="Detalle de Proveedor" />} />
        <Route path="/recursos-humanos" element={<RRHH />} />
        <Route path="/recursos-humanos/nuevo" element={<ModuloEnDesarrollo titulo="Nuevo Empleado" />} />
        <Route path="/recursos-humanos/:id" element={<ModuloEnDesarrollo titulo="Detalle de Empleado" />} />
        <Route path="/autorizaciones" element={<Autorizaciones />} />
        <Route path="/autorizaciones/nueva" element={<ModuloEnDesarrollo titulo="Nueva Autorización" />} />
        <Route path="/autorizaciones/:id" element={<ModuloEnDesarrollo titulo="Detalle de Autorización" />} />
        <Route path="/incidentes-adversos" element={<IncidentesAdversos />} />
        <Route path="/incidentes-adversos/nuevo" element={<ModuloEnDesarrollo titulo="Nuevo Incidente Adverso" />} />
        <Route path="/incidentes-adversos/:id" element={<ModuloEnDesarrollo titulo="Detalle de Incidente Adverso" />} />
        <Route path="/teleconsultas" element={<Teleconsultas />} />
        <Route path="/teleconsultas/nueva" element={<ModuloEnDesarrollo titulo="Nueva Teleconsulta" />} />
        <Route path="/teleconsultas/:id" element={<ModuloEnDesarrollo titulo="Detalle de Teleconsulta" />} />
        <Route path="/cirugias" element={<Cirugias />} />
        <Route path="/cirugias/nueva" element={<ModuloEnDesarrollo titulo="Nueva Cirugía" />} />
        <Route path="/cirugias/:id" element={<ModuloEnDesarrollo titulo="Detalle de Cirugía" />} />

        {/* Módulo de Dispensaciones */}
        <Route path="/dispensaciones" element={
          <ErrorBoundary fallback={<div className="p-6">Error en el módulo de Dispensaciones</div>}>
            <Dispensaciones />
          </ErrorBoundary>
        } />
        <Route path="/dispensaciones/nueva" element={
          <ErrorBoundary fallback={<div className="p-6">Error al crear nueva Dispensación</div>}>
            <DispensacionForm />
          </ErrorBoundary>
        } />
        <Route path="/dispensaciones/editar/:id" element={
          <ErrorBoundary fallback={<div className="p-6">Error al editar Dispensación</div>}>
            <DispensacionForm />
          </ErrorBoundary>
        } />
        <Route path="/dispensaciones/:id" element={
          <ErrorBoundary fallback={<div className="p-6">Error en el detalle de Dispensación</div>}>
            <DispensacionDetalle />
          </ErrorBoundary>
        } />
        <Route path="/inventario" element={<Inventario />} />
        <Route path="/inventario/nuevo" element={<ModuloEnDesarrollo titulo="Nuevo Item de Inventario" />} />
        <Route path="/inventario/editar/:id" element={<ModuloEnDesarrollo titulo="Editar Item de Inventario" />} />
        <Route path="/inventario/ver/:id" element={<ModuloEnDesarrollo titulo="Detalle de Item de Inventario" />} />
        
        {/* Módulo de Quirófanos */}
        <Route path="/quirofanos" element={
          <ErrorBoundary fallback={<div className="p-6">Error en el módulo de Quirófanos</div>}>
            <Quirofanos />
          </ErrorBoundary>
        } />
        <Route path="/quirofanos/:id" element={
          <ErrorBoundary fallback={<div className="p-6">Error en el detalle de Quirófano</div>}>
            <QuirofanoDetalle />
          </ErrorBoundary>
        } />
        <Route path="/quirofanos/asignacion" element={
          <ErrorBoundary fallback={<div className="p-6">Error en la asignación de Quirófano</div>}>
            <QuirofanoAsignacion />
          </ErrorBoundary>
        } />
        <Route path="/quirofanos/estadisticas" element={
          <ErrorBoundary fallback={<div className="p-6">Error en las estadísticas de Quirófanos</div>}>
            <QuirofanoEstadisticas />
          </ErrorBoundary>
        } />
        <Route path="/quirofanos/reservas" element={
          <ErrorBoundary fallback={<div className="p-6">Error en la reserva de Quirófano</div>}>
            <QuirofanoReserva />
          </ErrorBoundary>
        } />
        <Route path="/quirofanos/reservas/:id" element={
          <ErrorBoundary fallback={<div className="p-6">Error en la edición de reserva de Quirófano</div>}>
            <QuirofanoReserva />
          </ErrorBoundary>
        } />
        <Route path="/quirofanos/cirugias" element={
          <ErrorBoundary fallback={<div className="p-6">Error en el listado de Cirugías</div>}>
            <CirugiaLista />
          </ErrorBoundary>
        } />
        <Route path="/quirofanos/cirugias/nueva" element={
          <ErrorBoundary fallback={<div className="p-6">Error en la programación de Cirugía</div>}>
            <CirugiaDetalle />
          </ErrorBoundary>
        } />
        <Route path="/quirofanos/cirugias/:id" element={
          <ErrorBoundary fallback={<div className="p-6">Error en el detalle de Cirugía</div>}>
            <CirugiaDetalle />
          </ErrorBoundary>
        } />

        {/* Rutas CIE-11 */}
        <Route path="/configuraciones/cie11" element={
          <ErrorBoundary fallback={<div className="p-6">Error en el componente de Configuración CIE-11</div>}>
            <ConfiguracionCIE11 />
          </ErrorBoundary>
        } />
        <Route path="/reportes/cie11" element={
          <ErrorBoundary fallback={<div className="p-6">Error en el componente de Estadísticas CIE-11</div>}>
            <EstadisticasCIE11 />
          </ErrorBoundary>
        } />
      </Route>

      {/* Ruta 404 */}
      <Route path="*" element={<NotFound />} />
    </Routes>
  </Suspense>
);

export default AppRoutes;


