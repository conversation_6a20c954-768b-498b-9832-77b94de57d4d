import api from './api';
import { Consulta } from '../types';
import mockConsultasApi from './mockConsultasApi';
import { isMockEnvironment } from './api';

export interface ConsultaFiltros {
  paciente_id?: string;
  medico_id?: string;
  especialidad?: string;
  estado?: 'PROGRAMADA' | 'EN_CURSO' | 'COMPLETADA' | 'CANCELADA' | 'NO_ASISTIO'; // Actualizado: FINALIZADA → COMPLETADA
  fecha?: string;
  fecha_inicio?: string;
  fecha_fin?: string;
  tipo_consulta?: 'PRIMERA_VEZ' | 'CONTROL' | 'PROCEDIMIENTO';
  finalidad_consulta?: '01' | '02' | '03' | '04' | '05' | '06' | '07' | '08' | '09' | '10';
  causa_externa?: '01' | '02' | '03' | '04' | '05' | '06' | '07' | '08' | '09' | '10' | '11' | '12' | '13' | '14' | '15';
  tipo_diagnostico?: 'IMPRESION_DIAGNOSTICA' | 'CONFIRMADO_NUEVO' | 'CONFIRMADO_REPETIDO';
  codigo_consulta?: string;
  facturada?: boolean;
  codigo_habilitacion_prestador?: string;
  autorizacion_id?: string;
}

export interface ReporteConsultasFiltros {
  medico_id?: string;
  especialidad?: string;
  fecha_inicio: string;
  fecha_fin: string;
  finalidad_consulta?: '01' | '02' | '03' | '04' | '05' | '06' | '07' | '08' | '09' | '10';
  causa_externa?: '01' | '02' | '03' | '04' | '05' | '06' | '07' | '08' | '09' | '10' | '11' | '12' | '13' | '14' | '15';
  tipo_diagnostico?: 'IMPRESION_DIAGNOSTICA' | 'CONFIRMADO_NUEVO' | 'CONFIRMADO_REPETIDO';
  formato?: 'PDF' | 'EXCEL' | 'CSV';
  tipo_reporte?: 'GENERAL' | 'RIPS' | 'ESTADISTICO';
}

export interface FinalizarConsultaData {
  diagnostico_principal: string; // Código CIE10
  diagnosticos_secundarios?: string[];
  medicamentos_formulados?: string[];
  procedimientos_solicitados?: string[];
  observaciones?: string;
  notas_medicas?: string;
  finalidad_consulta: '01' | '02' | '03' | '04' | '05' | '06' | '07' | '08' | '09' | '10';
  causa_externa: '01' | '02' | '03' | '04' | '05' | '06' | '07' | '08' | '09' | '10' | '11' | '12' | '13' | '14' | '15';
  tipo_diagnostico: 'IMPRESION_DIAGNOSTICA' | 'CONFIRMADO_NUEVO' | 'CONFIRMADO_REPETIDO';
  codigo_consulta: string; // Código CUPS de la consulta
  proxima_cita?: string;
  generar_factura?: boolean;
  valor_consulta?: number;
  codigo_habilitacion_prestador?: string;
  autorizacion_id?: string;
  numero_autorizacion?: string;
}

export const consultasService = {
  // Obtener todas las consultas con filtros opcionales
  getConsultas: (hospitalId: number, filtros?: ConsultaFiltros) => {
    if (isMockEnvironment()) {
      return Promise.resolve(mockConsultasApi.getConsultas({ ...filtros, hospital_id: hospitalId }).data);
    }
    
    const params = new URLSearchParams();
    params.append('hospital_id', hospitalId.toString());

    if (filtros) {
      if (filtros.paciente_id) params.append('paciente_id', filtros.paciente_id);
      if (filtros.medico_id) params.append('medico_id', filtros.medico_id);
      if (filtros.especialidad) params.append('especialidad', filtros.especialidad);
      if (filtros.estado) params.append('estado', filtros.estado);
      if (filtros.fecha_inicio) params.append('fecha_inicio', filtros.fecha_inicio);
      if (filtros.fecha_fin) params.append('fecha_fin', filtros.fecha_fin);
      if (filtros.finalidad_consulta) params.append('finalidad_consulta', filtros.finalidad_consulta);
      if (filtros.causa_externa) params.append('causa_externa', filtros.causa_externa);
      if (filtros.tipo_diagnostico) params.append('tipo_diagnostico', filtros.tipo_diagnostico);
      if (filtros.codigo_consulta) params.append('codigo_consulta', filtros.codigo_consulta);
      if (filtros.facturada !== undefined) params.append('facturada', filtros.facturada.toString());
      if (filtros.codigo_habilitacion_prestador) params.append('codigo_habilitacion_prestador', filtros.codigo_habilitacion_prestador);
      if (filtros.autorizacion_id) params.append('autorizacion_id', filtros.autorizacion_id);
    }

    return api.get<Consulta[]>(`/consultas?${params.toString()}`).then((res) => res.data);
  },

  // Obtener una consulta por ID
  getConsultaById: (id: string, hospitalId: number) => {
    if (isMockEnvironment()) {
      const response = mockConsultasApi.getConsultaById(id);
      if (response.status === 200) {
        return Promise.resolve(response.data);
      }
      return Promise.reject(new Error(response.message || 'Consulta no encontrada'));
    }
    return api.get<Consulta>(`/consultas/${id}?hospital_id=${hospitalId}`).then((res) => res.data);
  },

  // Crear una nueva consulta
  createConsulta: (data: Partial<Consulta>, hospitalId: number) => {
    if (isMockEnvironment()) {
      const response = mockConsultasApi.createConsulta({ ...data, hospital_id: hospitalId });
      if (response.status === 201) {
        return Promise.resolve(response.data);
      }
      return Promise.reject(new Error('Error al crear consulta'));
    }
    return api.post<Consulta>('/consultas', { ...data, hospital_id: hospitalId }).then((res) => res.data);
  },

  // Actualizar una consulta existente
  updateConsulta: (id: string, data: Partial<Consulta>, hospitalId: number) => {
    if (isMockEnvironment()) {
      const response = mockConsultasApi.updateConsulta(id, { ...data, hospital_id: hospitalId });
      if (response.status === 200) {
        return Promise.resolve(response.data);
      }
      return Promise.reject(new Error(response.message || 'Error al actualizar consulta'));
    }
    return api.put<Consulta>(`/consultas/${id}`, { ...data, hospital_id: hospitalId }).then((res) => res.data);
  },

  // Eliminar una consulta
  deleteConsulta: (id: string) => {
    if (isMockEnvironment()) {
      const response = mockConsultasApi.deleteConsulta(id);
      if (response.status === 200) {
        return Promise.resolve({ success: true });
      }
      return Promise.reject(new Error(response.message || 'Error al eliminar consulta'));
    }
    return api.delete(`/consultas/${id}`).then(() => ({ success: true }));
  },

  // Obtener todas las especialidades disponibles
  getEspecialidades: (hospitalId: number) => {
    return api.get<string[]>(`/especialidades?hospital_id=${hospitalId}`).then((res) => res.data);
  },

  // Finalizar una consulta con datos RIPS y opción de facturación
  finalizarConsulta: (id: string, data: FinalizarConsultaData, hospitalId: number) => {
    if (isMockEnvironment()) {
      const response = mockConsultasApi.finalizarConsulta(id, { ...data, hospital_id: hospitalId });
      if (response.status === 200) {
        return Promise.resolve(response.data);
      }
      return Promise.reject(new Error(response.message || 'Error al finalizar consulta'));
    }
    return api.post<Consulta>(`/consultas/${id}/finalizar`, { ...data, hospital_id: hospitalId }).then((res) => res.data);
  },
  
  // Validar datos RIPS de una consulta
  validarDatosRIPS: (id: string, hospitalId: number) => {
    return api.get<{
      valido: boolean;
      errores?: { campo: string; mensaje: string }[];
      completo: boolean;
    }>(`/consultas/${id}/validar-rips?hospital_id=${hospitalId}`).then((res) => res.data);
  },
  
  // Exportar datos RIPS de consultas por período
  exportarRIPS: (filtros: ReporteConsultasFiltros, hospitalId: number) => {
    return api.post('/consultas/exportar-rips', { ...filtros, hospital_id: hospitalId }, {
      responseType: 'blob'
    }).then((res) => {
      const blob = new Blob([res.data]); // Aquí res.data es un Blob
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      const contentDisposition = res.headers['content-disposition'];
      let filename = 'rips_consultas.zip';
      
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/); 
        if (filenameMatch && filenameMatch.length === 2) {
          filename = filenameMatch[1];
        }
      }
      
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
      return url;
    });
  },

  // Generar factura para una consulta
  generarFactura: (id: string, hospitalId: number) => {
    return api.post<{ factura_id: string; numero_factura: string }>(`/consultas/${id}/facturar`, { hospital_id: hospitalId }).then((res) => res.data);
  },

  // Generar reportes de consultas
  generarReporte: (filtros: ReporteConsultasFiltros, hospitalId: number) => {
    return api.post('/consultas/reportes', { ...filtros, hospital_id: hospitalId }, {
      responseType: 'arraybuffer'
    }).then((res) => {
      const blob = new Blob([res.data], { type: 'application/pdf' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Obtener nombre del archivo del header Content-Disposition
      const contentDisposition = res.headers['content-disposition'];
      let filename = 'reporte_consultas.pdf';

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/); 
        if (filenameMatch && filenameMatch.length === 2) {
          filename = filenameMatch[1];
        }
      }

      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();

      return url;
    });
  },

  // Obtener estadísticas de consultas
  getEstadisticasConsultas: (filtros: Partial<ReporteConsultasFiltros>, hospitalId: number) => {
    if (isMockEnvironment()) {
      const response = mockConsultasApi.getEstadisticasConsultas({ ...filtros, hospital_id: hospitalId });
      if (response.status === 200) {
        return Promise.resolve(response.data);
      }
      return Promise.reject(new Error('Error al obtener estadísticas'));
    }

    const params = new URLSearchParams();
    params.append('hospital_id', hospitalId.toString());

    if (filtros.fecha_inicio) params.append('fecha_inicio', filtros.fecha_inicio);
    if (filtros.fecha_fin) params.append('fecha_fin', filtros.fecha_fin);
    if (filtros.medico_id) params.append('medico_id', filtros.medico_id);
    if (filtros.especialidad) params.append('especialidad', filtros.especialidad);
    if (filtros.finalidad_consulta) params.append('finalidad_consulta', filtros.finalidad_consulta);
    if (filtros.causa_externa) params.append('causa_externa', filtros.causa_externa);
    if (filtros.tipo_diagnostico) params.append('tipo_diagnostico', filtros.tipo_diagnostico);

    return api.get<{
      total_consultas: number;
      por_estado: { estado: string; cantidad: number }[];
      por_especialidad: { especialidad: string; cantidad: number }[];
      por_tipo: { tipo: string; cantidad: number }[];
    }>(`/consultas/estadisticas?${params.toString()}`).then((res) => res.data);
  },

  // Estadísticas de consultas RIPS
  getEstadisticasRIPS: (filtros: Partial<ReporteConsultasFiltros>, hospitalId: number) => {
    const params = new URLSearchParams();
    params.append('hospital_id', hospitalId.toString());

    if (filtros.fecha_inicio) params.append('fecha_inicio', filtros.fecha_inicio);
    if (filtros.fecha_fin) params.append('fecha_fin', filtros.fecha_fin);
    if (filtros.medico_id) params.append('medico_id', filtros.medico_id);
    if (filtros.especialidad) params.append('especialidad', filtros.especialidad);
    if (filtros.finalidad_consulta) params.append('finalidad_consulta', filtros.finalidad_consulta);
    if (filtros.causa_externa) params.append('causa_externa', filtros.causa_externa);
    if (filtros.tipo_diagnostico) params.append('tipo_diagnostico', filtros.tipo_diagnostico);

    return api.get<{
      consultas_totales: number;
      cantidad_por_finalidad: { codigo: string; descripcion: string; cantidad: number }[];
      cantidad_por_causa_externa: { codigo: string; descripcion: string; cantidad: number }[];
      cantidad_por_diagnostico: { codigo: string; descripcion: string; cantidad: number }[];
      consultas_facturadas: number;
      consultas_pendientes: number;
      porcentaje_completitud_rips: number;
    }>(`/consultas/estadisticas-rips?${params.toString()}`).then((res) => res.data);
  },

  // Obtener datos de asistencia y seguimiento de consultas
  getDatosSeguimientoConsultas: (hospitalId: number, periodo: 'SEMANA' | 'MES' | 'TRIMESTRE' | 'ANIO') => {
    return api.get<{
      total_consultas: number;
      asistidas: number;
      no_asistidas: number;
      canceladas: number;
      reprogramadas: number;
      porcentaje_asistencia: number;
      tendencia: { periodo: string; cantidad: number }[];
    }>(`/consultas/seguimiento?hospital_id=${hospitalId}&periodo=${periodo}`).then((res) => res.data);
  },

  // Obtener procedimientos médicos (simulado)
  getProcedimientos: () => {
    const procedimientos = [
      'Electrocardiograma',
      'Ecocardiograma',
      'Radiografía de tórax',
      'Espirometría',
      'Audiometría',
      'Tonometría ocular',
      'Fondo de ojo',
      'Evaluación de sensibilidad en pies',
      'Limpieza dental',
      'Infiltración articular',
      'Toma de muestra para cultivo',
      'Citología cervical',
      'Biopsia de piel',
      'Extracción de cuerpo extraño',
      'Drenaje de absceso',
      'Sutura de herida',
      'Vendaje compresivo',
      'Inmovilización con férula',
      'Nebulización',
      'Terapia de rehidratación oral',
    ];

    return Promise.resolve(procedimientos);
  },
  // Buscar diagnósticos CIE10 (simulado)
  buscarDiagnosticosCIE10: (query: string) => {
    // Simulamos una búsqueda de diagnósticos CIE10
    const diagnosticos = [
      { codigo: 'I20.9', descripcion: 'Angina de pecho, no especificada' },
      { codigo: 'I10', descripcion: 'Hipertensión esencial (primaria)' },
      { codigo: 'E11.9', descripcion: 'Diabetes mellitus tipo 2 sin complicaciones' },
      { codigo: 'J03.9', descripcion: 'Amigdalitis aguda, no especificada' },
      { codigo: 'J00', descripcion: 'Rinofaringitis aguda [resfriado común]' },
      { codigo: 'K29.7', descripcion: 'Gastritis, no especificada' },
      { codigo: 'M54.5', descripcion: 'Lumbago no especificado' },
      { codigo: 'G43.9', descripcion: 'Migraña, no especificada' },
      { codigo: 'L71.9', descripcion: 'Rosácea, no especificada' },
      { codigo: 'E78.0', descripcion: 'Hipercolesterolemia pura' },
      { codigo: 'G44.2', descripcion: 'Cefalea tensional' },
      { codigo: 'F41.9', descripcion: 'Trastorno de ansiedad, no especificado' },
      { codigo: 'F32.9', descripcion: 'Episodio depresivo, no especificado' },
      { codigo: 'H10.9', descripcion: 'Conjuntivitis, no especificada' },
      { codigo: 'B34.9', descripcion: 'Infección viral, no especificada' },
    ];

    // Filtramos los diagnósticos que coincidan con la búsqueda
    const resultados = diagnosticos.filter(
      (d) => d.codigo.toLowerCase().includes(query.toLowerCase()) ||
             d.descripcion.toLowerCase().includes(query.toLowerCase())
    );

    // Simulamos una respuesta asíncrona
    return Promise.resolve(resultados);
  },

  // Obtener lista de procedimientos médicos (simulado)
  getProcedimientos: () => {
    const procedimientos = [
      'Electrocardiograma',
      'Ecocardiograma',
      'Radiografía de tórax',
      'Espirometría',
      'Audiometría',
      'Tonometría ocular',
      'Fondo de ojo',
      'Evaluación de sensibilidad en pies',
      'Limpieza dental',
      'Infiltración articular',
      'Toma de muestra para cultivo',
      'Citología cervical',
      'Biopsia de piel',
      'Extracción de cuerpo extraño',
      'Drenaje de absceso',
      'Sutura de herida',
      'Vendaje compresivo',
      'Inmovilización con férula',
      'Nebulización',
      'Terapia de rehidratación oral',
    ];

    return Promise.resolve(procedimientos);
  },

  // Obtener datos de asistencia y seguimiento de consultas
  getDatosSeguimientoConsultas: (hospitalId: number, periodo: 'SEMANA' | 'MES' | 'TRIMESTRE' | 'ANIO') => {
    return api.get<{
      total_consultas: number;
      asistidas: number;
      no_asistidas: number;
      canceladas: number;
      reprogramadas: number;
      porcentaje_asistencia: number;
      tendencia: { periodo: string; cantidad: number }[];
    }>(`/consultas/seguimiento?hospital_id=${hospitalId}&periodo=${periodo}`).then((res) => res.data);
  },

  // Obtener procedimientos CUPS para consultas
  buscarProcedimientosCUPS: (query: string) => {
    // Simulamos una búsqueda de procedimientos CUPS
    const procedimientos = [
      { codigo: '890201', descripcion: 'Consulta de medicina general' },
      { codigo: '890301', descripcion: 'Consulta de medicina especializada' },
      { codigo: '890305', descripcion: 'Consulta de pediatría' },
      { codigo: '890304', descripcion: 'Consulta de obstetricia' },
      { codigo: '890302', descripcion: 'Consulta de medicina interna' },
      { codigo: '890308', descripcion: 'Consulta de cardiología' },
      { codigo: '890306', descripcion: 'Consulta de ginecología' },
      { codigo: '890307', descripcion: 'Consulta de dermatología' },
      { codigo: '890310', descripcion: 'Consulta de ortopedia' },
      { codigo: '890309', descripcion: 'Consulta de oftalmología' },
      { codigo: '890311', descripcion: 'Consulta de psiquiatría' },
      { codigo: '890312', descripcion: 'Consulta de psicología' },
      { codigo: '890313', descripcion: 'Consulta de neurología' },
      { codigo: '890314', descripcion: 'Consulta de urología' },
      { codigo: '890315', descripcion: 'Consulta de otorrinolaringología' },
    ];

    // Filtramos los procedimientos que coincidan con la búsqueda
    const resultados = procedimientos.filter(
      (p) => p.codigo.toLowerCase().includes(query.toLowerCase()) ||
             p.descripcion.toLowerCase().includes(query.toLowerCase())
    );

    // Simulamos una respuesta asíncrona
    return Promise.resolve(resultados);
  },
  // Convertir una cita en consulta (NUEVO)
  convertirCitaEnConsulta: (citaId: string, hospitalId: number) => {
    if (isMockEnvironment()) {
      const response = mockConsultasApi.convertirCitaEnConsulta(citaId);
      if (response.status === 200 || response.status === 201) {
        return Promise.resolve(response.data);
      }
      return Promise.reject(new Error(response.message || 'Error al convertir cita en consulta'));
    }
    return api.post<Consulta>(`/citas/${citaId}/convertir-consulta`, { hospital_id: hospitalId }).then((res) => res.data);
  }
};
