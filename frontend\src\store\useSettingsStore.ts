import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type Theme = 'light' | 'dark';
export type ContentBackground = 'elegant' | 'glass' | 'solid';

interface SettingsState {
  // Sistema de temas
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;

  // Configuración de fondo
  backgroundContrast: number; // 0 a 100
  backgroundTint: number; // -100 (blanco) a 100 (negro), 0 es neutral
  backgroundOpacity: number; // 0 a 100 - Opacidad de la imagen de fondo
  updateBackgroundContrast: (value: number) => void;
  updateBackgroundTint: (value: number) => void;
  updateBackgroundOpacity: (value: number) => void;

  // Glassmorphism (solo para header, sidebar, footer)
  glassmorphismOpacity: number; // 0 a 100
  glassmorphismColor: string; // Color base del glassmorphism
  updateGlassmorphismOpacity: (value: number) => void;
  updateGlassmorphismColor: (value: string) => void;

  // Configuración de contenido
  contentBackground: ContentBackground; // Tipo de fondo para el área de contenido
  updateContentBackground: (value: ContentBackground) => void;

  // Contraste de texto
  textContrast: number; // 0 a 100
  updateTextContrast: (value: number) => void;

  // Configuración de accesibilidad
  highContrast: boolean;
  reducedMotion: boolean;
  toggleHighContrast: () => void;
  toggleReducedMotion: () => void;
}

// Función para detectar preferencia del sistema
const getSystemTheme = (): Theme => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  }
  return 'light';
};

// Función para aplicar el tema al DOM
const applyTheme = (theme: Theme) => {
  if (typeof document !== 'undefined') {
    document.documentElement.setAttribute('data-theme', theme);
    document.documentElement.classList.remove('light', 'dark');
    document.documentElement.classList.add(theme);
  }
};

// Usar middleware persist para guardar configuración en localStorage
export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      // Sistema de temas
      theme: getSystemTheme(),
      toggleTheme: () => {
        const newTheme = get().theme === 'light' ? 'dark' : 'light';
        set({ theme: newTheme });
        applyTheme(newTheme);
      },
      setTheme: (theme: Theme) => {
        set({ theme });
        applyTheme(theme);
      },

      // Configuración de fondo
      backgroundContrast: 80,
      backgroundTint: 0, // 0 es neutral, negativo tiende a blanco, positivo a negro
      backgroundOpacity: 100, // 100% de opacidad por defecto (imagen completamente visible)
      updateBackgroundContrast: (value) => set({ backgroundContrast: value }),
      updateBackgroundTint: (value) => set({ backgroundTint: value }),
      updateBackgroundOpacity: (value) => set({ backgroundOpacity: value }),

      // Glassmorphism (solo para header, sidebar, footer)
      glassmorphismOpacity: 60,
      glassmorphismColor: '26, 34, 54', // RGB del color base
      updateGlassmorphismOpacity: (value) => set({ glassmorphismOpacity: value }),
      updateGlassmorphismColor: (value) => set({ glassmorphismColor: value }),

      // Configuración de contenido
      contentBackground: 'elegant',
      updateContentBackground: (value) => set({ contentBackground: value }),

      // Contraste de texto
      textContrast: 90,
      updateTextContrast: (value) => set({ textContrast: value }),

      // Configuración de accesibilidad
      highContrast: false,
      reducedMotion: false,
      toggleHighContrast: () => {
        const newValue = !get().highContrast;
        set({ highContrast: newValue });
        if (typeof document !== 'undefined') {
          document.documentElement.classList.toggle('high-contrast', newValue);
        }
      },
      toggleReducedMotion: () => {
        const newValue = !get().reducedMotion;
        set({ reducedMotion: newValue });
        if (typeof document !== 'undefined') {
          document.documentElement.classList.toggle('reduced-motion', newValue);
        }
      },
    }),
    {
      name: 'hipocrates-ui-settings', // nombre para localStorage
      onRehydrateStorage: () => (state) => {
        // Aplicar tema al cargar desde localStorage
        if (state?.theme) {
          applyTheme(state.theme);
        }
        // Aplicar configuraciones de accesibilidad
        if (state?.highContrast && typeof document !== 'undefined') {
          document.documentElement.classList.add('high-contrast');
        }
        if (state?.reducedMotion && typeof document !== 'undefined') {
          document.documentElement.classList.add('reduced-motion');
        }
      },
    }
  )
);
