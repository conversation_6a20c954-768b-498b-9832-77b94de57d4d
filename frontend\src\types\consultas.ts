// Enums para mantener consistencia en el tipo de datos (alineado con citas)
export enum EstadoConsulta {
  PROGRAMADA = 'PROGRAMADA',
  EN_CURSO = 'EN_CURSO',
  COMPLETADA = 'COMPLETADA', // Ali<PERSON>do con citas (FINALIZADA → COMPLETADA)
  CANCELADA = 'CANCELADA',
  NO_ASISTIO = 'NO_ASISTIO' // Agregado para consistencia con citas
}

export enum TipoDiagnostico {
  IMPRESION_DIAGNOSTICA = 'IMPRESION_DIAGNOSTICA',
  CONFIRMADO_NUEVO = 'CONFIRMADO_NUEVO',
  CONFIRMADO_REPETIDO = 'CONFIRMADO_REPETIDO'
}

export enum TipoConsulta {
  PRIMERA_VEZ = 'PRIMERA_VEZ',
  CONTROL = 'CONTROL',
  PROCEDIMIENTO = 'PROCEDIMIENTO'
}

// Interface basada en la tabla clinico.consultas
export interface Consulta {
  id: string;
  hospital_id: number;
  fecha: string;
  historia_clinica_id: string;
  profesional_id: string;
  paciente_id: string;
  tipo_documento_identificacion: string;
  num_documento_identificacion: string;
  consecutivo: string;
  fecha_inicio_atencion: string;
  motivo_consulta?: string;
  diagnostico_principal?: string;
  version_cie?: 'CIE-10' | 'CIE-11'; // Agregado para transición CIE
  tipo_diagnostico?: TipoDiagnostico;
  notas?: string;
  codigo_cups?: string;
  finalidad_consulta?: string; // formato: [0-1][0-9]
  causa_externa?: string; // formato: [A-Z][0-9]{2}
  valor_consulta?: number;
  cuota_moderadora?: number;
  valor_neto?: number;
  codigo_eps: string;
  numero_autorizacion?: string;
  factura_id?: string;
  created_at: string;
  updated_at: string;

  // Campos para alineación con citas
  cita_id?: string; // Relación con cita origen
  agenda_id?: string; // Heredado de cita
  especialidad_id?: string; // Alineado con citas (en lugar de string libre)
  especialidad?: string; // Mantener para compatibilidad
  hora_inicio?: string; // Alineado con citas
  hora_fin?: string; // Alineado con citas
  duracion_minutos?: number; // Alineado con citas
  consultorio?: string; // Heredado de cita
  estado?: EstadoConsulta; // Estado de la consulta
  tipo_consulta?: TipoConsulta; // Tipo de consulta
  
  // Campos adicionales para la UI
  paciente?: {
    nombre_completo: string;
    tipo_documento?: string;
    numero_documento?: string;
    edad?: number;
    genero?: string;
  };
  profesional?: {
    nombre_completo: string;
    especialidad?: string;
    registro_profesional?: string;
  };
  
  // Campos médicos adicionales
  anamnesis?: string;
  hallazgos?: string;
  plan_tratamiento?: string;
  recomendaciones?: string;
  signos_vitales?: {
    temperatura?: number;
    presion_sistolica?: number;
    presion_diastolica?: number;
    frecuencia_cardiaca?: number;
    frecuencia_respiratoria?: number;
    saturacion_oxigeno?: number;
    peso?: number;
    talla?: number;
    imc?: number;
  };
  examenes_solicitados?: Array<{
    id?: string;
    nombre: string;
    codigo?: string;
    observaciones?: string;
  }>;
  procedimientos_realizados?: Array<{
    id?: string;
    nombre: string;
    codigo?: string;
    observaciones?: string;
  }>;
  medicamentos_formulados?: Array<{
    id?: string;
    nombre: string;
    dosis: string;
    frecuencia: string;
    duracion: string;
    observaciones?: string;
  }>;
  diagnosticos_secundarios?: Array<{
    codigo: string;
    descripcion: string;
    version?: 'CIE-10' | 'CIE-11';
  }>;
}

// Interface para la creación o actualización de consultas
export interface ConsultaInput {
  paciente_id: string;
  profesional_id: string;
  historia_clinica_id: string;
  fecha_inicio_atencion: string;
  motivo_consulta?: string;
  cita_id?: string;
  diagnostico_principal?: string;
  tipo_diagnostico?: TipoDiagnostico;
  codigo_cups?: string;
  finalidad_consulta?: string;
  causa_externa?: string;
  notas?: string;
  codigo_eps: string;
  numero_autorizacion?: string;
}

// Interface para finalizar una consulta
export interface FinalizarConsultaInput {
  id: string;
  diagnostico_principal: string;
  tipo_diagnostico: TipoDiagnostico;
  diagnosticos_secundarios?: Array<{
    codigo: string;
    descripcion: string;
    version?: 'CIE-10' | 'CIE-11';
  }>;
  notas?: string;
  recomendaciones?: string;
  plan_tratamiento?: string;
  examenes_solicitados?: Array<{
    nombre: string;
    codigo?: string;
    observaciones?: string;
  }>;
  procedimientos_realizados?: Array<{
    nombre: string;
    codigo?: string;
    observaciones?: string;
  }>;
  medicamentos_formulados?: Array<{
    nombre: string;
    dosis: string;
    frecuencia: string;
    duracion: string;
    observaciones?: string;
  }>;
}
